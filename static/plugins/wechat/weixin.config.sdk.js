    var pageUrl = encodeURIComponent(window.location.href.split('#')[0]);
    $.ajax({
        url : window.shareData.signatureUrl+pageUrl,
        async:false,
        cache : false,
        dataType : "json",
        success : function(r) {
            r=eval(r);
            if (r.success) {
                wx.config({
                    debug: false,
                    appId: r.appId,
                    timestamp: r.timestamp,
                    nonceStr: r.nonceStr,
                    signature: r.signature,
                    jsApiList: ['onMenuShareTimeline', 'onMenuShareAppMessage']
                });
            }else{
                return;
            }
        },
        error:function(data){

        }
    });

    wx.ready(function () {
        // 2. 分享接口
        // 2.1 监听“分享给朋友”，按钮点击、自定义分享内容及分享结果接口
        wx.onMenuShareAppMessage({
            title: window.shareData.tTitle,
            desc: window.shareData.tContent,
            link: window.shareData.tLink,
            imgUrl: window.shareData.imgUrl,
            success: function () {
                //alert('已分享');
            },
            cancel: function () {
                //alert('已取消');
            }
        });

        // 2.2 监听“分享到朋友圈”按钮点击、自定义分享内容及分享结果接口

        wx.onMenuShareTimeline({
            title: window.shareData.tTitle,
            desc: window.shareData.tContent,
            link: window.shareData.tLink,
            imgUrl: window.shareData.imgUrl,
            success: function () {
                //alert('已分享');
            },
            cancel: function () {
                //alert('已取消');
            }
        });


    });
    wx.error(function(res) {
        alert(res);
    });
