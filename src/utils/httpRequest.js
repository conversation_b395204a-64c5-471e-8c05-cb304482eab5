import Vue from 'vue'
import axios from 'axios'
import router from '@/router'
import qs from 'qs'
import merge from 'lodash/merge'
import { clearLoginInfo } from '@/utils'
import { Notification } from 'element-ui'

const http = axios.create({
  timeout: 1000 * 30,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json; charset=utf-8'
  }
})

/**
 * 请求拦截
 */
http.interceptors.request.use(config => {
  if (!config.url.endsWith('fykj/login')) {
    config.headers['Authorization'] = sessionStorage.getItem('Authorization') // 请求头带上token
  }
  return config
}, error => {
  return Promise.reject(error)
})

/**
 * 响应拦截
 */
let errorLock = false
http.interceptors.response.use(response => {
  let data = response.data
  if (data && data.code === 40101) {
    // 40101, token失效
    // Vue.prototype.$message.error(data.msg)
    clearLoginInfo()
    // token失效跳转页面
    let url = ''
    if (window.location.href.indexOf('/login?redirect=') !== -1) {
      url = decodeURIComponent(window.location.href.substring(window.location.href.lastIndexOf('/login?redirect=') + 16))
      router.push(`/login?redirect=` + url)
    } else {
      url = decodeURIComponent(window.location.href.substring(window.location.href.indexOf('#/') + 1))
      router.push(`/login?redirect=` + url)
    }
  }
  return response
}, error => {
  console.error('当前服务不可用:', error)
  // 处理重复弹框，当服务不可用时，一个页面可能会调用多个接口，会出现重复弹窗，加个锁处理下
  // if (!errorLock) {
  //   errorLock = true
  // } else {
  //   return
  // }
  // Notification.error({
  //   title: '错误',
  //   message: '当前服务不可用，请稍后再试',
  //   onClose: () => {
  //     errorLock = false
  //   }
  // })
  return Promise.reject(error)
})

/**
 * 请求地址处理
 * @param {*} actionName action方法名称
 */
http.adornUrl = (actionName) => {
  // 非生产环境 && 开启代理, 接口前缀统一使用[/proxyApi/]前缀做代理拦截!
  return (process.env.NODE_ENV !== 'production' && process.env.OPEN_PROXY ? '/proxyApi/' : window.SITE_CONFIG.baseUrl) + actionName
}

/**
 * 请求地址处理
 * @param {*} actionName action方法名称
 */
http.adornAttachmentUrl = (actionName) => {
  if (actionName.startsWith('http')) {
    return actionName
  }
  let prefix = window.SITE_CONFIG.baseUrl
  if (actionName.charAt(0) !== '/') {
    actionName = '/' + actionName
  }
  // 非生产环境 && 开启代理, 接口前缀统一使用[/proxyApi/]前缀做代理拦截!
  return (process.env.NODE_ENV !== 'production' && process.env.OPEN_PROXY ? '/proxyApi/' : prefix) + '/file' + actionName
}

/**
 * get请求参数处理
 * @param {*} params 参数对象
 * @param {*} openDefaultParams 是否开启默认参数?
 */
http.adornParams = (params = {}, openDefaultParams = true) => {
  var defaults = {
    't': new Date().getTime()
  }
  return openDefaultParams ? merge(defaults, params) : params
}

/**
 * post请求数据处理
 * @param {*} data 数据对象
 * @param {*} openDefaultData 是否开启默认数据?
 * @param {*} contentType 数据格式
 *  json: 'application/json; charset=utf-8'
 *  form: 'application/x-www-form-urlencoded; charset=utf-8'
 */
http.adornData = (data = {}, openDefaultData = true, contentType = 'json') => {
  let defaults = {
    't': new Date().getTime()
  }
  data = openDefaultData ? merge(defaults, data) : data
  if (contentType === 'file') { // 带文件上传
    let form = new FormData()
    for (let key in data) {
      form.append(key, data[key])
    }
    return form
  }
  return contentType === 'json' ? JSON.stringify(data) : qs.stringify(data)
}

export default http
