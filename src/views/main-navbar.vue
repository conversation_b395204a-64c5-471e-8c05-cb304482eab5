<template>
  <nav class="site-navbar" :class="'site-navbar--' + navbarLayoutType">
    <div class="site-navbar__header">
      <p class="site-navbar__brand" @click="$router.push({ name: 'home' })">
        <img
          src="../assets/img/logo.png"
          style="height: 70%; margin-left: 5px"
        />
        <a
          class="site-navbar__brand-lg"
          style="font-size: 17px; margin-left: -4px"
          href="javascript:;"
          >文明园区综合管理后台</a
        >
        <a class="site-navbar__brand-mini" href="javascript:;">文明园区</a>
      </p>
    </div>
    <div class="site-navbar__body clearfix">
      <el-menu class="site-navbar__menu" mode="horizontal">
        <el-menu-item
          class="site-navbar__switch"
          index="0"
          @click="sidebarFold = !sidebarFold"
        >
          <icon-svg name="zhedie"></icon-svg>
        </el-menu-item>
      </el-menu>
      <el-menu
        class="site-navbar__menu site-navbar__menu--right"
        mode="horizontal"
      >
        <!-- <el-menu-item index="1" @click="$router.push({ name: 'theme' })">
          <template slot="title">
            <el-badge value="new">
              <icon-svg name="shezhi" class="el-icon-setting"></icon-svg>
            </el-badge>
          </template>
        </el-menu-item> -->
        <!--  @click="$router.push({ name: 'theme' })" -->
        <el-menu-item
          index="1"
          class="site-navbar__avatar"
          @click="getMessageList"
        >
          <template slot="title">
              <el-popover
                ref="reference"
                placement="bottom-start"
                trigger="click"
              >
                <main-navbar-message
                  ref="messageDiaglog"
                  @refreshDataList="refreshData()"
                ></main-navbar-message>
                <div slot="reference">
                  <el-badge
                    :value="number"
                    class="item"
                    v-if="number + '' !== '0'"
                  >
                    <i class="el-icon-bell" style="margin-right: -2px"></i>消息
                  </el-badge>
                  <span v-if="number === 0">
                    <i class="el-icon-bell" style="margin-right: -2px"></i>消息
                  </span>
                </div>
              </el-popover>
          </template>
        </el-menu-item>
        <el-menu-item class="site-navbar__avatar" index="2">
          <el-dropdown :show-timeout="0" placement="bottom">
            <span class="el-dropdown-link">
              <img src="~@/assets/img/avatar.png" :alt="userName" />{{
                userName
              }}
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="updatePasswordHandle()"
                >修改密码</el-dropdown-item
              >
              <el-dropdown-item @click.native="logoutHandle()"
                >退出</el-dropdown-item
              >
            </el-dropdown-menu>
          </el-dropdown>
        </el-menu-item>
      </el-menu>
    </div>
    <!-- 弹窗, 修改密码 -->
    <update-password
      v-if="updatePassowrdVisible"
      ref="updatePassowrd"
    ></update-password>
  </nav>
</template>

<script>
import UpdatePassword from './main-navbar-update-password'
import { clearLoginInfo } from '@/utils'
import MainNavbarMessage from './main-navbar-message.vue'
export default {
  data () {
    return {
      updatePassowrdVisible: false,
      number: '',
      drawer: false,
      messageList: []
    }
  },
  components: {
    UpdatePassword,
    MainNavbarMessage
  },
  computed: {
    navbarLayoutType: {
      get () { return this.$store.state.common.navbarLayoutType }
    },
    sidebarFold: {
      get () { return this.$store.state.common.sidebarFold },
      set (val) { this.$store.commit('common/updateSidebarFold', val) }
    },
    mainTabs: {
      get () { return this.$store.state.common.mainTabs },
      set (val) { this.$store.commit('common/updateMainTabs', val) }
    },
    userName: {
      get () { return this.$store.state.user.name }
    }
  },
  created () {
    this.getMessageNumber()
  },
  methods: {
    getMessageNumber () {
      this.$http({
        url: this.$http.adornUrl('/admin/sys/msgLog/countNotRead'),
        method: 'get',
        params: {
          targetType: 5
        }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.number = resp.data.obj
        }
      })
    },
    getMessageList () {
      this.$nextTick(() => {
        this.$refs.messageDiaglog.init(this.number)
      })
      this.getMessageNumber()
    },
    refreshData () {
      this.getMessageNumber()
    },
    // 修改密码
    updatePasswordHandle () {
      this.updatePassowrdVisible = true
      this.$nextTick(() => {
        this.$refs.updatePassowrd.init()
      })
    },
    // 退出
    logoutHandle () {
      this.$confirm(`确定进行[退出]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl(`/auth/auth/logout`),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({ data }) => {
          if (data && data.code === 0) {
            clearLoginInfo()
            this.$router.push({ name: 'login' })
          }
        })
      }).catch(() => { })
    }
  }
}
</script>

<style lang="scss" scoped>
.item {
  margin-top: 15px;
}
.pHide {
  /* 禁止换行 */
  white-space: nowrap;
  /* 超出隐藏 */
  overflow: hidden;
  /* 显示省略号 */
  text-overflow: ellipsis;
}
.divHide {
  color: #a7a8a8;
  cursor: pointer;
}
</style>
