<template>
  <div style="width: 350px; max-height: 600px">
    <div style="margin-bottom: -5px">
      <i class="el-icon-bell" style="color: blue; margin-left: 5px"></i>
      <span style="font-size: 18px">全部消息</span>
      <span
        @click="goMoreMessage"
        style="float: right; font-size: 18px; cursor: pointer;margin-right:5px"
        >更多</span
      >
    </div>

    <el-divider></el-divider>
    <div
      style="
        height: 250px;
        display: flex;
        justify-content: center;
        align-items: center;
      "
      v-if="messageList.length === 0"
    >
      <p align="center" style="font-size: 18px">暂无消息</p>
    </div>
    <div
      v-if="messageList !== []"
      style="
        width: 350px;
        max-height: 500px;
        margin-rigth: 15px;
        margin-top: -15px;
        overflow: hidden;
      "
    >
      <div style="overflow-y: auto;max-height:500px; width: 400px">
        <div
          v-for="item in messageList"
          :key="item.id"
          :class="getWenZiColor(item.readed)"
        >
          <div
            style="margin-left: 5px; margin-top: 5px; cursor: pointer"
            @click="readMessage(item)"
          >
            <div style="float: left">
              <i
                class="el-icon-chat-dot-square"
                v-if="!item.readed"
                style="color: blue; margin-top: 5px"
              ></i>
              <i
                class="el-icon-chat-dot-square"
                v-if="item.readed"
                style="margin-top: 5px"
              ></i>
            </div>
            <div>
              <p
                :class="getWenZiColor(item.readed)"
                style="line-height: 22px; margin-left: 22px; width: 315px"
              >
                {{ item.content }}
              </p>

              <p
                align="left"
                style="margin-left: 22px; width: 300px; margin-top: -10px"
                :class="getWenZiColor(item.readed)"
              >
                {{ item.createDate }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      messageList: [],
      number: ''
    }
  },
  methods: {
    init (number) {
      this.number = number
      this.getMessageList()
    },
    getMessageList () {
      this.$http({
        url: this.$http.adornUrl('/admin/sys/msgLog/personal'),
        method: 'get',
        params: {
          targetType: 5,
          currentPage: 1,
          pageSize: 20
        }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.messageList = resp.data.obj.records
        } else {
          this.messageList = []
        }
      })
    },
    getWenZiColor (readed) {
      if (readed) {
        return 'divHide'
      }
    },
    // 查看详情，并已读
    readMessage (message) {
      this.$http({
        url: this.$http.adornUrl('/admin/sys/msgLog'),
        method: 'get',
        params: {
          id: message.id
        }
      }).then((resp) => {
        this.$emit('refreshDataList')
        this.getMessageList()
      })
    },
    // 查看更多消息
    goMoreMessage () {
      this.$router.push('/sys-message/log/sysmsglog-list')
    }
  }
}
</script>

<style>
.item {
  margin-top: 15px;
}
.pHide {
  /* 禁止换行 */
  white-space: nowrap;
  /* 超出隐藏 */
  overflow: hidden;
  /* 显示省略号 */
  text-overflow: ellipsis;
}
.divHide {
  color: #a7a8a8;
  cursor: pointer;
}
.scrollbar {
  width: 30px;

  height: 300px;

  margin: 0 auto;
}
</style>
