<template>
  <div class="page-position">
    <div class="position-total"  @click="$router.push({path: '/position-evaluate/question/card/list',})">
      <div class="new-count">
        <div class="title">
          <div>待处理</div>
          <img src="~@/assets/img/workbench/evaluate-icon.png"  class="img-icon"/>
        </div>
        <div class="number" v-text="evaluationObj.pendingNum"></div>
      </div>
      <div class="status-count">
        <div class="title">
          <div>待反馈(待回复)</div>
          <img src="~@/assets/img/workbench/evaluate-icon-1.png"  class="img-icon"/>
        </div>
        <div class="number" v-text="evaluationObj.waitReplyNum"></div>
      </div>
      <div class="status-count">
        <div class="title">
          <div>已反馈</div>
          <img src="~@/assets/img/workbench/evaluate-icon-2.png"  class="img-icon"/>
        </div>
        <div class="number" v-text="evaluationObj.feedbackNum"></div>
      </div>
      <div class="status-count">
        <div class="title">
          <div>累计问题</div>
          <img src="~@/assets/img/workbench/evaluate-icon-3.png"  class="img-icon"/>
        </div>
        <div class="number" v-text="evaluationObj.totalQuestionNum"></div>
      </div>
      <div class="status-count">
        <div class="title">
          <div>累计测评数</div>
          <img src="~@/assets/img/workbench/evaluate-icon-3.png"  class="img-icon"/>
        </div>
        <div class="number" v-text="evaluationObj.totalEvaluateNum"></div>
      </div>
    </div>
    <div class="month-add">
      <div class="title">30天问题新增趋势</div>
      <div class="chart-box-block">
        <div id="J_chartMenuTypeBarBox" class="chart-box"></div>
        <div class="month-position-num">
          <div class="name-mb">累计问题</div>
          <div class="name-mb2">{{dailyObj.totalAmount}}</div>
          <div class="name-mb">累计已解决问题</div>
          <div class="name-mb2">{{dailyObj.offShelfAmount}}</div>
        </div>
      </div>
    </div>
    <div class="percent-rate" v-if="isStreet">
      <div class="position-left">
        <div class="text-rate">
          <div class="title">问题分类</div>
          <div >
            <el-table
              :cell-style="cellStyle"
              :header-cell-style="rowStyle"
              :data="categoryList"
              style="width: 100%">
              <el-table-column
                prop="num"
                label="排名"
                width="60"
              >
                <template slot-scope="scope">
                  <span :class=" scope.$index < 3 ? 'table-num' : 'un-num'" >{{scope.$index + 1}}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="name"
                label="问题分类"
              >
              </el-table-column>
              <el-table-column
                prop="pendingNum"
                label="待处理"
              >
              </el-table-column>
              <el-table-column
                prop="waitReplyNum"
                label="待反馈(待回复)"
              >
              </el-table-column>
              <el-table-column
                prop="feedbackNum"
                label="已反馈"
              >
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
      <div class="position-right">
        <div class="image-rate">
          <div class="title">街道测评数占比</div>
          <div id="J_chartPieBox3" class="chart-box"></div>
        </div>
        <div class="image-rate">
          <div class="title">问题类型占比</div>
          <div id="J_chartPieBox4" class="chart-box"></div>
        </div>
      </div>
    </div>
    <div class="question-rate" v-if="isStreet">
      <div class="question-rate-left">
<!--        <div class="title">街道问题占比</div>-->
        <div class="question-content">
          <div class="question-rate-table">
            <div class="title">街道问题占比</div>
              <el-table
                :data="questionData"
                style="width: 100%">
                <el-table-column
                  prop="num"
                  label="排名"
                  width="60"
                >
                  <template slot-scope="scope">
                    <span :class=" scope.$index < 3 ? 'table-num' : 'un-num'" >{{scope.$index + 1}}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="name"
                  label="街道"
                >
                </el-table-column>
                <el-table-column
                  prop="count"
                  label="问题数量"
                >
                </el-table-column>
                <el-table-column
                  prop="percent"
                  label="占比"
                >
                </el-table-column>
              </el-table>
          </div>
          <div class="question-rate-image">
            <div class="title">问题状态占比</div>
              <div id="J_chartPieBox5" class="chart-box"></div>
          </div>
        </div>
      </div>
      <div class="question-rate-right">
          <div class="title">部门得分</div>
          <div class="chart-box-block">
            <div id="J_chartDepartmentBarBox" class="chart-box"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import echarts from 'echarts'
import _ from 'lodash'
export default {
  data () {
    return {
      chartMenuTypeBar: null,
      chartDepartmentBar: null,
      // chartPie: null,
      // positionObj: {},
      dailyXObj: [],
      dailyYObj: [],
      dailyObj: [],
      // regionData: [],
      // categoryData1: [],
      // categoryData2: []
      evaluationObj: [],
      categoryList: [],
      chartPie4: null,
      chartPie3: null,
      chartPie5: null,
      evaluationList: [],
      categoryNumList: [],
      questionData: [],
      isStreet: false
    }
  },
  created () {
  },
  activated () {
    if (this.chartMenuTypeBar) {
      this.chartMenuTypeBar.resize()
    }
    if (this.chartPie3) {
      this.chartPie3.resize()
    }
    if (this.chartPie4) {
      this.chartPie4.resize()
    }
    if (this.chartPie5) {
      this.chartPie5.resize()
    }
    if (this.chartDepartmentBar) {
      this.chartDepartmentBar.resize()
    }
  },
  mounted () {
    this.init()
  },
  methods: {
    init () {
      this.isUserStreet()
      this.getEvaluation()
      this.getCategory()
      this.getEvaluateList()
      this.getCategoryNumList()
      this.getQuestionList()
      this.getDailyData()
      // this.initChartDepartmentBar()
      // this.getPosition()
      // this.getDailyData()
      // // this.initChartPie()
      // this.getRegion()
      // this.getCategory()
    },
    initChartMenuTypeBar () {
      var that = this
      var option = {
        // legend: {
        //   data: ['区内数量', '区外数量'],
        //   left: 'center'
        // },
        // brush: {
        //   toolbox: ['rect', 'polygon', 'lineX', 'lineY', 'keep', 'clear'],
        //   xAxisIndex: 0
        // },
        // toolbox: {
        //   feature: {
        //     magicType: {
        //       type: ['stack']
        //     },
        //     dataView: {}
        //   }
        // },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function (params) {
            return that.dailyObj.dailyStatisticsVoList[params[0].dataIndex].date + '<br>' + '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#0052d9;"></span>' + '新增' + ' ' + that.dailyObj.dailyStatisticsVoList[params[0].dataIndex].amount
          }
        },
        // color: ['#C82C38'],
        xAxis: {
          data: this.dailyXObj,
          // data: ['线上', '线下', '线上+线下'],
          // name: 'X Axis',
          axisLine: { show: false, onZero: true },
          splitLine: { show: false },
          splitArea: { show: false },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          // interval: 2,
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        grid: {
          bottom: 30
        },
        series: [
          {
            // name: '菜单式选学类型',
            type: 'bar',
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0, 0, 0, 1, // 渐变色在下面修改，这里是透明度
                  [{
                    offset: 0,
                    color: '#0052d9'
                  }, {
                    offset: 0.5,
                    color: '#0052d9'
                  }, {
                    offset: 1,
                    color: '#31beff'
                  }
                  ]
                ),
                label: {
                  show: true, // 开启数值显示
                  position: 'top', // 在上方显示
                  textStyle: {
                    // 数值样式
                    // color: "#A0FFFE",
                    fontSize: 14
                  }
                }
              }
            },
            // itemStyle: {
            //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            //     { offset: 0, color: '#FFCCD0' },
            //     { offset: 0.5, color: '#C82C38' },
            //     { offset: 1, color: '#C82C38' }
            //   ])
            // },
            // stack: 'one',
            barWidth: 15, // 柱图宽度
            data: this.dailyYObj
            // data: [1, 2, 3]
            // data: this.menuYCount
          }
        ]
      }
      this.chartMenuTypeBar = echarts.init(document.getElementById('J_chartMenuTypeBarBox'))
      this.chartMenuTypeBar.setOption(option)
      window.addEventListener('resize', () => {
        this.chartMenuTypeBar.resize()
      })
    },
    initChartDepartmentBar () {
      var xDepartment = ['综合协调', '移动公司', '胜浦街道办事处']
      var yDepartment = [40, 88, 60]
      var that = this
      var option = {
        // legend: {
        //   data: ['区内数量', '区外数量'],
        //   left: 'center'
        // },
        // brush: {
        //   toolbox: ['rect', 'polygon', 'lineX', 'lineY', 'keep', 'clear'],
        //   xAxisIndex: 0
        // },
        // toolbox: {
        //   feature: {
        //     magicType: {
        //       type: ['stack']
        //     },
        //     dataView: {}
        //   }
        // },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
          // formatter: function (params) {
          //   return that.dailyObj[params[0].dataIndex].date + '<br>' + '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#0052d9;"></span>' + '新增' + ' ' + that.dailyObj[params[0].dataIndex].amount
          // }
        },
        // color: ['#C82C38'],
        xAxis: {
          // data: xDepartment,
          data: ['线上', '线下', '线上+线下'],
          // name: 'X Axis',
          axisLine: { show: false, onZero: true },
          splitLine: { show: false },
          splitArea: { show: false },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          // interval: 2,
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        grid: {
          bottom: 30
        },
        series: [
          {
            // name: '菜单式选学类型',
            type: 'bar',
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0, 0, 0, 1, // 渐变色在下面修改，这里是透明度
                  [{
                    offset: 0,
                    color: '#91CC75'
                  }, {
                    offset: 0.5,
                    color: '#91CC75'
                  }, {
                    offset: 1,
                    color: '#a3ccb7'
                  }
                  ]
                ),
                label: {
                  show: true, // 开启数值显示
                  position: 'top', // 在上方显示
                  textStyle: {
                    // 数值样式
                    // color: "#A0FFFE",
                    fontSize: 14
                  }
                }
              }
            },
            // itemStyle: {
            //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            //     { offset: 0, color: '#FFCCD0' },
            //     { offset: 0.5, color: '#C82C38' },
            //     { offset: 1, color: '#C82C38' }
            //   ])
            // },
            // stack: 'one',
            barWidth: 15, // 柱图宽度
            // data: yDepartment
            data: [1, 2, 3]
            // data: this.menuYCount
          }
        ]
      }
      this.chartDepartmentBar = echarts.init(document.getElementById('J_chartDepartmentBarBox'))
      this.chartDepartmentBar.setOption(option)
      window.addEventListener('resize', () => {
        this.chartDepartmentBar.resize()
      })
    },
    initChartPie3 () {
      var option = {
        // title: {
        //   text: '职级分布',
        //   left: 'left'
        // },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          top: '80%',
          left: 'center',
          icon: 'circle'
          // textStyle: {
          //   padding: [1,0,0,0]
          // }
        },
        // color: ['#c82c38', '#fa4856', '#ffa800', '#ffc75a', '#ffe9bf', '#d34223'],
        color: ['#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE'],
        series: [
          {
            name: '街道测评数占比',
            type: 'pie',
            radius: ['40%', '70%'],
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            // radius: '50%',
            data: this.evaluationList,
            // data: [
            //   { value: 654, name: '科员' },
            //   { value: 315, name: '科级' },
            //   { value: 117, name: '处级' }
            // ],
            // data: [
            //   { value: 1429, name: '科员' },
            //   { value: 2584, name: '副科' },
            //   { value: 973, name: '正科' },
            //   { value: 1641, name: '副处' },
            //   { value: 1094, name: '正处' },
            //   { value: 851, name: '副厅' },
            //   { value: 1094, name: '正厅' },
            //   { value: 334, name: '其他' }
            // ],
            // emphasis: {
            //   itemStyle: {
            //     shadowBlur: 10,
            //     shadowOffsetX: 0,
            //     shadowColor: 'rgba(0, 0, 0, 0.5)'
            //   }
            // },
            labelLine: {
              normal: {
                show: false
                // show: true
              }
            },
            label: {
              show: false
              // position: 'outside',
              // normal: {
              //   textStyle: {
              //     fontFamily: 'Microsoft YaHei',
              //     fontSize: 14
              //     // color: '#898989'
              //   },
              //   formatter: '{b}\n{d}%'
              // }
            }
          }
        ]
      }
      this.chartPie3 = echarts.init(document.getElementById('J_chartPieBox3'))
      this.chartPie3.setOption(option)
      window.addEventListener('resize', () => {
        this.chartPie3.resize()
      })
    },
    initChartPie4 () {
      console.log(this.statusCount)
      var option = {
        // title: {
        //   text: '职级分布',
        //   left: 'left'
        // },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          top: '80%',
          left: 'center',
          icon: 'circle'
        },
        color: ['#5470C6 ', '#91CC75', '#FAC858', '#EE6666', '#73C0DE'],
        // color: ['#c82c38', '#fa4856', '#ffa800', '#ffc75a', '#ffe9bf', '#d34223', '#ff613e', '#ff896f'],
        series: [
          {
            name: '问题类型占比',
            type: 'pie',
            radius: ['40%', '70%'],
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            data: this.categoryNumList,
            // data: [
            //   { value: 654, name: '科员' },
            //   { value: 315, name: '科级' },
            //   { value: 117, name: '处级' }
            // ],
            // data: [
            //   { value: 1429, name: '科员' },
            //   { value: 2584, name: '副科' },
            //   { value: 973, name: '正科' },
            //   { value: 1641, name: '副处' },
            //   { value: 1094, name: '正处' },
            //   { value: 851, name: '副厅' },
            //   { value: 1094, name: '正厅' },
            //   { value: 334, name: '其他' }
            // ],
            // emphasis: {
            //   itemStyle: {
            //     shadowBlur: 10,
            //     shadowOffsetX: 0,
            //     shadowColor: 'rgba(0, 0, 0, 0.5)'
            //   }
            // },
            labelLine: {
              normal: {
                show: false
                // show: true
              }
            },
            label: {
              show: false
              // position: 'outside',
              // normal: {
              //   textStyle: {
              //     fontFamily: 'Microsoft YaHei',
              //     fontSize: 14
              //   //   color: '#898989'
              //   },
              //   formatter: '{b}\n{d}%'
              // }
            }
          }
        ]
      }
      this.chartPie4 = echarts.init(document.getElementById('J_chartPieBox4'))
      this.chartPie4.setOption(option)
      window.addEventListener('resize', () => {
        this.chartPie4.resize()
      })
    },
    initChartPie5 () {
      var option = {
        // title: {
        //   text: '职级分布',
        //   left: 'left'
        // },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          bottom: '-2%',
          left: 'center',
          icon: 'circle'
        },
        color: ['#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE'],
        // color: ['#c82c38', '#fa4856', '#ffa800', '#ffc75a', '#ffe9bf', '#d34223', '#ff613e', '#ff896f'],
        series: [
          {
            name: '街道问题占比',
            type: 'pie',
            radius: ['40%', '70%'],
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            data: this.questionData,
            // data: [
            //   { value: 654, name: '科员' },
            //   { value: 315, name: '科级' },
            //   { value: 117, name: '处级' }
            // ],
            // data: [
            //   { value: 1429, name: '科员' },
            //   { value: 2584, name: '副科' },
            //   { value: 973, name: '正科' },
            //   { value: 1641, name: '副处' },
            //   { value: 1094, name: '正处' },
            //   { value: 851, name: '副厅' },
            //   { value: 1094, name: '正厅' },
            //   { value: 334, name: '其他' }
            // ],
            // emphasis: {
            //   itemStyle: {
            //     shadowBlur: 10,
            //     shadowOffsetX: 0,
            //     shadowColor: 'rgba(0, 0, 0, 0.5)'
            //   }
            // },
            labelLine: {
              normal: {
                show: false
                // show: true
              }
            },
            label: {
              show: false
              // position: 'outside',
              // normal: {
              //   textStyle: {
              //     fontFamily: 'Microsoft YaHei',
              //     fontSize: 14
              //   //   color: '#898989'
              //   },
              //   formatter: '{b}\n{d}%'
              // }
            }
          }
        ]
      }
      this.chartPie5 = echarts.init(document.getElementById('J_chartPieBox5'))
      this.chartPie5.setOption(option)
      window.addEventListener('resize', () => {
        this.chartPie5.resize()
      })
    },
    isUserStreet () {
      this.$http({
        url: this.$http.adornUrl('/admin/user/isExistStreet'),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.isStreet = !data.obj
        }
      })
    },
    getEvaluation () {
      this.$http({
        url: this.$http.adornUrl('/evaluate/workbench/sum'),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.evaluationObj = data.obj
        }
      })
    },
    getCategory () {
      this.$http({
        url: this.$http.adornUrl('/evaluate/workbench/categoryList'),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.categoryList = data.obj
        }
      })
    },
    getEvaluateList () {
      this.$http({
        url: this.$http.adornUrl('/evaluate/workbench/region/evaluate/list'),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.evaluationList = []
          data.obj.forEach(e => {
            this.evaluationList.push({name: e.name, value: e.count})
          })
          this.initChartPie3()
        }
      })
    },
    getCategoryNumList () {
      this.$http({
        url: this.$http.adornUrl('/evaluate/workbench/categoryNumList'),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.categoryNumList = []
          data.obj.forEach(e => {
            this.categoryNumList.push({name: e.name, value: e.count})
          })
          this.initChartPie4()
        }
      })
    },
    getQuestionList () {
      this.questionData = []
      this.$http({
        url: this.$http.adornUrl('/evaluate/workbench/region/question/list'),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 0) {
          // var sum = 0
          // data.obj.forEach(e => {
          //   sum = sum + e.count
          // })
          var sum = _.sumBy(data.obj, 'count')
          this.questionData = data.obj
          this.questionData.forEach((e, index) => {
            if (sum === 0) {
              e.percent = '0%'
            } else {
              e.percent = Math.floor(e.count / sum * 100) + '%'
            }
            e.value = e.count
          })
          this.initChartPie5()
        }
      })
    },
    getDailyData () {
      this.$http({
        url: this.$http.adornUrl('/position/statistics/workbench/position/daily'),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dailyXObj = []
          this.dailyYObj = []
          this.dailyObj = []
          data.obj.dailyStatisticsVoList.forEach((e, index) => {
            this.dailyXObj.push(e.date.substr(8, 2))
            this.dailyYObj.push(e.amount)
          })
          this.dailyObj = data.obj
          this.initChartMenuTypeBar()
          this.initChartDepartmentBar()
        }
      })
    },
    cellStyle ({row, column, rowIndex, columnIndex}) {
      if (columnIndex === 2) {
        return 'background: #FFFBF4;color: #EB9F03'
      }
      if (columnIndex === 3) {
        return 'background: #FFF1E5;color: #FF5C00'
      }
      if (columnIndex === 4) {
        return 'background: #E9FFF2;color: #048F55'
      }
      return ''
    },
    rowStyle ({row, column, rowIndex, columnIndex}) {
      if (columnIndex === 2) {
        return 'background: #FFFBF4'
      }
      if (columnIndex === 3) {
        return 'background: #FFF1E5'
      }
      if (columnIndex === 4) {
        return 'background: #E9FFF2'
      }
      return ''
    }
  }
}
</script>

<style scoped lang="scss">
  .page-position {
    margin-top: 30px;
    .position-total {
      /*padding: 5px 5px;*/
      /*box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.13);*/
      display: flex;
      justify-content: space-between;
      .new-count {
        background: #3F5ED1;
        border-radius: 8px 8px 8px 8px;
        box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.13);
        width: 19%;
        height: 122px;
        padding: 14px 22px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .title {
          font-size: 20px;
          color: #fff;
          display: flex;
          justify-content: space-between;
          img {
            margin-top: -5px;
            width: 52px;
          }
        }
        .number {
          color: #fff;
          font-size: 36px;
        }
      }
      .status-count {
        /*box-shadow: 0px 6px 8px 0px rgba(0,0,0,0.12);*/
        border-radius: 8px 8px 8px 8px;
        box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.13);
        width: 19%;
        height: 122px;
        padding: 14px 22px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .title {
          font-size: 20px;
          color: #000;
          display: flex;
          justify-content: space-between;
          img {
            width: 47px;
          }
        }
        .number {
          font-size: 36px;
        }
      }
    }
    .month-add {
      box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.13);
      margin-top: 20px;
      margin-bottom: 20px;
      padding: 30px;
      .title {
        font-size: 24px;
        color: #000;
        /*font-size: 16px;*/
        /*margin-bottom: 20px;*/
      }
      .chart-box-block {
        display: flex;
        .chart-box {
          width: 97%;
          min-height: 300px;
          /*width: 100%;*/
          margin-left: -70px;
          margin-right: -60px;
        }
        .month-position-num {
          /*font-size: 16px;*/
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-top: 30px;
          .name-mb {
            font-size: 14px;
            margin-bottom: 10px;
          }
          .name-mb2 {
            font-size: 36px;
            margin-bottom: 40px;
          }
        }
      }

    }
    .percent-rate {
      margin-top: 20px;
      display: flex;
      justify-content: space-between;
      /*height: 300px;*/
      .position-left {
        padding: 30px;
        display: flex;
        .text-rate {
          width: 100%;
          .table-num {
            font-size: 14px;
            width: 20px;
            height:20px;
            background-color:#60b3ff;
            color:#ffffff;
            border-radius: 50%;
            text-align: center;
            line-height: 20px;
          }
          .un-num {
            font-size: 14px;
            width:20px;
            height:20px;
            background-color:#C8C8C8;
            color:#ffffff;
            border-radius: 50%;
            text-align: center;
            line-height: 20px;
          }
        }
        /*.image-rate {*/
        /*  width: 40%;*/
        /*  .chart-box {*/
        /*    height: 300px;*/
        /*  }*/
        /*}*/
        .title {
          font-size: 24px;
          color: #000;
          margin-bottom: 20px;
          /*margin-bottom: 20px;*/
        }
        box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.13);
        width: 49.5%;
      }
      .position-right {
        padding: 30px;
        .title {
          font-size: 24px;
          /*margin-bottom: 20px;*/
        }
        .position-type-content {
          display: flex;
          display: -webkit-flex;
          justify-content: space-between;
        }
        box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.13);
        width: 49.5%;
        display: flex;
        display: -webkit-flex;
        justify-content: space-between;
        .image-rate {
          width: 40%;
          .chart-box {
            height: 400px;
            margin-top: -70px;
            margin-bottom: -30px;
          }
        }
      }
    }
    .question-rate {
      margin-top: 20px;
      display: flex;
      justify-content: space-between;
      .question-rate-left {
        width: 44.5%;
        box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.13);
        padding: 30px;
        .title {
          font-size: 24px;
          margin-bottom: 20px;
        }
        .question-content {
          display: flex;
          display: -webkit-flex;
          .table-num {
            font-size: 14px;
            width:20px;
            height:20px;
            background: #60B3FF;
            color:#ffffff;
            border-radius: 50%;
            text-align: center;
            line-height: 20px;
          }
          .un-num {
            font-size: 14px;
            width:20px;
            height:20px;
            background: #C8C8C8;
            color:#ffffff;
            border-radius: 50%;
            text-align: center;
            line-height: 20px;
          }
          .question-rate-table {
            width: 65%;
            .table-num {
              font-size: 14px;
              width:20px;
              height:20px;
              background-color:#60B3FF;
              color:#ffffff;
              border-radius: 50%;
              text-align: center;
              line-height: 20px;
            }
          }
          .question-rate-image {
            width: 35%;
            .chart-box {
              min-height: 300px;
              margin-top: -50px;
              /*width: 100%;*/
            }
          }
        }
      }
      .question-rate-right {
        width: 54.5%;
        padding: 30px;
        box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.13);
        .title {
          font-size: 24px;
          color: #000;
          /*font-size: 16px;*/
          /*margin-bottom: 20px;*/
        }
        .chart-box-block {
          display: flex;
          .chart-box {
            width: 97%;
            min-height: 300px;
            /*width: 100%;*/
          }
          .month-position-num {
            /*font-size: 16px;*/
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 30px;
            .name-mb {
              font-size: 14px;
              margin-bottom: 10px;
            }
            .name-mb2 {
              font-size: 36px;
              margin-bottom: 40px;
            }
          }
        }
      }

    }
    /deep/.el-table__body {
      .el-table_1_column_1 {
        /*.cell {*/
        /*  width: 30px;*/
        /*  height: 30px;*/
        /*  background-color: #0052d9;*/
        /*  border-radius: 50%;*/
        /*  line-height: 30px;*/
        /*}*/
        /*color: #ffffff;*/
        /*text-align: center;*/
      }
      .cell {
        display: flex;
        justify-content: center;
      }
    }
    /deep/ .el-table__header {
      .cell {
        display: flex;
        justify-content: center;
      }
    }

    /deep/ .el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
      border-bottom: unset;
    }
    /deep/ .el-table th.el-table__cell.is-leaf {
      /*border-bottom: unset;*/
      font-size: 16px;
      color: rgba(0,0,0,0.6);
    }
    /deep/ .el-table tr {
      background-color: #FFF;
      font-size: 16px;
      color: #000;
    }
  }
  /deep/ .el-table::before {
    display: none;
  }
</style>
