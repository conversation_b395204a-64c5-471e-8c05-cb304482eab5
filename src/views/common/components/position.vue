<template>
    <div class="page-position">
      <div class="position-total" @click="$router.push({path: '/position-position/manage/list'})">
        <div class="new-count">
          <div class="title">
            <div>今日新增</div>
            <img src="~@/assets/img/workbench/position-icon.png"  class="img-icon"/>
          </div>
          <div class="number" v-text="positionObj.todayAmount"></div>
        </div>
        <div class="status-count">
          <div class="title">
            <div>草稿</div>
            <img src="~@/assets/img/workbench/position-icon-1.png"  class="img-icon"/>
          </div>
          <div class="number" v-text="positionObj.draftAmount"></div>
        </div>
        <div class="status-count">
          <div class="title">
            <div>审核中</div>
            <img src="~@/assets/img/workbench/position-icon-2.png"  class="img-icon"/>
          </div>
          <div class="number" v-text="positionObj.reviewingAmount"></div>
        </div>
        <div class="status-count">
          <div class="title">
            <div>已下架</div>
            <img src="~@/assets/img/workbench/position-icon-3.png"  class="img-icon"/>
          </div>
          <div class="number" v-text="positionObj.offShelfAmount"></div>
        </div>
        <div class="status-count">
          <div class="title">
            <div>累计点位（正常点位）</div>
            <img src="~@/assets/img/workbench/position-icon-4.png"  class="img-icon"/>
          </div>
          <div class="number" v-text="positionObj.normalAmount"></div>
        </div>
      </div>
      <div class="month-add">
        <div class="title">30天新增</div>
        <div class="chart-box-block">
          <div id="J_chartMenuTypeBarBox" class="chart-box"></div>
          <div class="month-position-num">
            <div class="name-mb">本月累计新增正常点位</div>
            <div class="name-mb2">{{dailyObj.totalAmount}}</div>
            <div class="name-mb">下架点位</div>
            <div class="name-mb2">{{dailyObj.offShelfAmount}}</div>
          </div>
        </div>
      </div>
      <div class="percent-rate" v-if="isStreet">
        <div class="position-left">
          <div class="text-rate">
            <div class="title">街道点位占比</div>
            <div >
              <el-table
                :data="regionData"
                style="width: 100%">
                <el-table-column
                  prop="num"
                  label="排名"
                  width="60"
                >
                  <template slot-scope="scope">
                    <span :class="scope.row.num < 4 ? 'table-num' : 'un-num'"  >{{scope.row.num}}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="areaName"
                  label="街道"
                  width = "120"
                  >
                </el-table-column>
                <el-table-column
                  prop="counts"
                  label="累计点位数量"
                 >
                </el-table-column>
                <el-table-column
                  prop="percent"
                  label="占比"
                  >
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="image-rate">
            <div class="title">占比</div>
            <div id="J_chartPieBox" class="chart-box"></div>
          </div>
        </div>
        <div class="position-right">
          <div class="title">点位类型占比</div>
          <div class="position-type-content">
              <el-table
                :data="categoryData1"
                style="width: 100%">
                <el-table-column
                  prop="num"
                  label="排名"
                  width = "60"
                >
                  <template slot-scope="scope">
                    <span :class=" scope.row.num < 4 ? 'table-num' : 'un-num'"  >{{scope.row.num}}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="categoryName"
                  label="街道"
                  width = "110"
                >
                </el-table-column>
                <el-table-column
                  prop="amount"
                  label="累计点位数量"
                  width = "120"
                >
                </el-table-column>
                <el-table-column
                  prop="percent"
                  label="区域分布"
                >
                </el-table-column>
              </el-table>
            <el-table
              :data="categoryData2"
              style="width: 100%">
              <el-table-column
                prop="num"
                label="排名"
                width = "60"
              >
                <template slot-scope="scope">
                  <span :class=" scope.$index < 3 ? 'table-num' : 'un-num'" >{{scope.row.num}}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="categoryName"
                label="街道"
                width = "110"
              >
              </el-table-column>
              <el-table-column
                prop="amount"
                label="累计点位数量"
                width = "120"
              >
              </el-table-column>
              <el-table-column
                prop="percent"
                label="区域分布"
              >
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
</template>

<script>
  import echarts from 'echarts'
  import _ from 'lodash'
  export default {
    data () {
      return {
        chartMenuTypeBar: null,
        chartPie: null,
        positionObj: {},
        dailyXObj: [],
        dailyYObj: [],
        dailyObj: [],
        regionData: [],
        categoryData1: [],
        categoryData2: [],
        isStreet: false
      }
    },
    created () {
    },
    activated () {
      if (this.chartMenuTypeBar) {
        this.chartMenuTypeBar.resize()
      }
      if (this.chartPie) {
        this.chartPie.resize()
      }
    },
    mounted () {
      this.init()
    },
    methods: {
      init () {
        this.isUserStreet()
        this.getPosition()
        this.getDailyData()
        // this.initChartPie()
        this.getRegion()
        this.getCategory()
      },
      initChartMenuTypeBar () {
        var that = this
        var option = {
          // legend: {
          //   data: ['区内数量', '区外数量'],
          //   left: 'center'
          // },
          // brush: {
          //   toolbox: ['rect', 'polygon', 'lineX', 'lineY', 'keep', 'clear'],
          //   xAxisIndex: 0
          // },
          // toolbox: {
          //   feature: {
          //     magicType: {
          //       type: ['stack']
          //     },
          //     dataView: {}
          //   }
          // },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            },
            formatter: function (params) {
              return that.dailyObj.dailyStatisticsVoList[params[0].dataIndex].date + '<br>' + '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#0052d9;"></span>' + '新增' + ' ' + that.dailyObj.dailyStatisticsVoList[params[0].dataIndex].amount
            }
          },
          // color: ['#C82C38'],
          xAxis: {
            data: this.dailyXObj,
            // data: ['线上', '线下', '线上+线下'],
            // name: 'X Axis',
            axisLine: { show: false, onZero: true },
            splitLine: { show: false },
            splitArea: { show: false },
            axisTick: {
              show: false
            }
          },
          yAxis: {
            // interval: 2,
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed'
              }
            }
          },
          grid: {
            bottom: 30
          },
          series: [
            {
              // name: '菜单式选学类型',
              type: 'bar',
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(
                    0, 0, 0, 1, // 渐变色在下面修改，这里是透明度
                    [{
                      offset: 0,
                      color: '#0052d9'
                    }, {
                      offset: 0.5,
                      color: '#0052d9'
                    },
                    {
                      offset: 1,
                      color: '#31beff'
                    }
                    ]
                  ),
                  label: {
                    show: true, // 开启数值显示
                    position: 'top', // 在上方显示
                    textStyle: {
                      // 数值样式
                      // color: "#A0FFFE",
                      fontSize: 14
                    }
                  }
                }
              },
              // itemStyle: {
              //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              //     { offset: 0, color: '#FFCCD0' },
              //     { offset: 0.5, color: '#C82C38' },
              //     { offset: 1, color: '#C82C38' }
              //   ])
              // },
              // stack: 'one',
              barWidth: 15, // 柱图宽度
              data: this.dailyYObj
              // data: [1, 2, 3]
              // data: this.menuYCount
            }
          ]
        }
        this.chartMenuTypeBar = echarts.init(document.getElementById('J_chartMenuTypeBarBox'))
        this.chartMenuTypeBar.setOption(option)
        window.addEventListener('resize', () => {
          this.chartMenuTypeBar.resize()
        })
      },
      initChartPie () {
        var option = {
          // title: {
          //   text: '职级分布',
          //   left: 'left'
          // },
          tooltip: {
            trigger: 'item'
          },
          legend: {
            bottom: '5%',
            left: 'center',
            icon: 'circle'
          },
          // legend: {
          //   orient: 'vertical',
          //   left: 'right'
          // },
          color: ['#ee6666', '#fac858', '#91cc75', '#5470c6', '#73c0de'],
          // color: ['#c82c38', '#fa4856', '#ffa800', '#454aff', '#ff896f'],
          // color: ['#c82c38', '#fa4856', '#ffa800', '#ffc75a', '#ffe9bf', '#d34223', '#ff613e', '#ff896f'],
          series: [
            {
              name: '街道点位占比',
              type: 'pie',
              radius: ['40%', '70%'],
              // radius: '50%',
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
              },
              data: this.regionData,
              // emphasis: {
              //   itemStyle: {
              //     shadowBlur: 10,
              //     shadowOffsetX: 0,
              //     shadowColor: 'rgba(0, 0, 0, 0.5)'
              //   }
              // },
              labelLine: {
                normal: {
                  show: false
                  // show: true
                }
              }
              // label: {
              //   show: false,
              //   position: 'outside',
              //   normal: {
              //     textStyle: {
              //       // color: '#898989'
              //       fontFamily: 'Microsoft YaHei',
              //       fontSize: 14
              //     },
              //     formatter: '{b}\n{d}%'
              //   }
              // }
            }
          ]
        }
        this.chartPie = echarts.init(document.getElementById('J_chartPieBox'))
        this.chartPie.setOption(option)
        window.addEventListener('resize', () => {
          this.chartPie.resize()
        })
      },
      isUserStreet () {
        this.$http({
          url: this.$http.adornUrl('/admin/user/isExistStreet'),
          method: 'get'
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.isStreet = !data.obj
          }
        })
      },
      getPosition () {
        this.$http({
          url: this.$http.adornUrl('/position/statistics/workbench/position'),
          method: 'get'
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.positionObj = data.obj
          }
        })
      },
      getDailyData () {
        this.$http({
          url: this.$http.adornUrl('/position/statistics/workbench/position/daily'),
          method: 'get'
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.dailyXObj = []
            this.dailyYObj = []
            this.dailyObj = []
            data.obj.dailyStatisticsVoList.forEach((e, index) => {
              this.dailyXObj.push(e.date.substr(8, 2))
              this.dailyYObj.push(e.amount)
            })
            this.dailyObj = data.obj
            this.initChartMenuTypeBar()
          }
        })
      },
      getRegion () {
        this.regionData = []
        this.$http({
          url: this.$http.adornUrl('/position/statistics/workbench/position/region'),
          method: 'get'
        }).then(({ data }) => {
          if (data && data.code === 0) {
            // this.positionObj = data.obj
            // var sum = 0
            // data.obj.forEach(e => {
            //   sum = sum + e.counts
            // })
            var sum = _.sumBy(data.obj, 'counts')
            this.regionData = data.obj
            this.regionData.forEach((e, index) => {
              e.num = index + 1
              e.percent = Math.floor(e.counts / sum * 100) + '%'
              e.name = e.areaName
              e.value = e.counts
            })
            this.initChartPie()
          }
        })
      },
      // 关于分类统计展示
      getCategory () {
        this.$http({
          url: this.$http.adornUrl('/position/statistics/workbench/position/category'),
          method: 'get'
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.categoryData1 = []
            this.categoryData2 = []
            var objList = _.take(data.obj, 10)
            // var sum = 0
            // objList.forEach(e => {
            //   sum = sum + e.amount
            // })
            var sum = _.sumBy(objList, 'amount')
            console.log(objList)
            objList.forEach((e, index) => {
              e.num = index + 1
              e.percent = Math.floor(e.amount / sum * 100) + '%'
              if (index < 5) {
                this.categoryData1.push(e)
              } else {
                this.categoryData2.push(e)
              }
            })
          }
        })
      }
    }
  }
</script>

<style scoped lang="scss">
  .page-position {
    margin-top: 30px;
    .position-total {
      /*padding: 5px 5px;*/
      /*box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.13);*/
      display: flex;
      justify-content: space-between;
      .new-count {
        background: #3F5ED1;
        border-radius: 8px 8px 8px 8px;
        box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.13);
        width: 19%;
        height: 122px;
        padding: 14px 22px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .title {
          font-size: 20px;
          color: #fff;
          display: flex;
          justify-content: space-between;
          img {
            margin-top: -5px;
            width: 52px;
          }
        }
        .number {
          color: #fff;
          font-size: 36px;
        }
      }
      .status-count {
        /*box-shadow: 0px 6px 8px 0px rgba(0,0,0,0.12);*/
        border-radius: 8px 8px 8px 8px;
        box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.13);
        width: 19%;
        height: 122px;
        padding: 14px 22px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .title {
          font-size: 20px;
          color: #000;
          display: flex;
          justify-content: space-between;
          img {
            width: 47px;
          }
        }
        .number {
          font-size: 36px;
        }
      }
    }
    .month-add {
      box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.13);
      margin-top: 20px;
      margin-bottom: 20px;
      padding: 30px;
      .title {
        font-size: 24px;
        color: #000;
        /*font-size: 16px;*/
        /*margin-bottom: 20px;*/
      }
      .chart-box-block {
        display: flex;
        .chart-box {
          width: 97%;
          min-height: 300px;
          /*width: 100%;*/
          margin-left: -70px;
          margin-right: -60px;
        }
        .month-position-num {
          /*font-size: 16px;*/
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-top: 30px;
          .name-mb {
            font-size: 14px;
            margin-bottom: 10px;
          }
          .name-mb2 {
            font-size: 36px;
            margin-bottom: 40px;
          }
        }
      }

    }
    .percent-rate {
      display: flex;
      justify-content: space-between;
      /*height: 300px;*/
      .position-left {
        width: 44.5%;
        padding: 30px;
        display: flex;
        .text-rate{
          width: 65%;
          .table-num {
            font-size: 14px;
            width:20px;
            height:20px;
            background-color: #60b3ff;
            /*background-color:#0052d9;*/
            color:#ffffff;
            border-radius: 50%;
            text-align: center;
            line-height: 20px;
          }
          .un-num {
            font-size: 14px;
            width:20px;
            height:20px;
            background-color: #C8C8C8;
            /*background-color:#c0c4cc;*/
            color:#ffffff;
            border-radius: 50%;
            text-align: center;
            line-height: 20px;
          }
        }
        .image-rate {
          width: 35%;
          .chart-box {
            height: 300px;
            margin-top: -60px;
          }
        }
        .title {
          font-size: 24px;
          color: #000;
          /*font-size: 16px;*/
          margin-bottom: 20px;
          /*margin-bottom: 20px;*/
        }
        box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.13);
        /*width: 49.5%;*/
      }
      .position-right {
        width: 54.5%;
        padding: 30px;
        .title {
          font-size: 24px;
          margin-bottom: 20px;
        }
        .position-type-content {
          display: flex;
          display: -webkit-flex;
          justify-content: space-between;
          .table-num {
            font-size: 14px;
            width:20px;
            height:20px;
            background-color: #60b3ff;
            /*background-color:#0052d9;*/
            color:#ffffff;
            border-radius: 50%;
            text-align: center;
            line-height: 20px;
          }
          .un-num {
            font-size: 14px;
            width:20px;
            height:20px;
            background-color: #C8C8C8;
            /*background-color:#c0c4cc;*/
            color:#ffffff;
            border-radius: 50%;
            text-align: center;
            line-height: 20px;
          }
        }
        box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.13);
        /*width: 49.5%;*/
      }
    }
    /deep/.el-table__body {
      .el-table_1_column_1 {
        /*.cell {*/
        /*  width: 30px;*/
        /*  height: 30px;*/
        /*  background-color: #0052d9;*/
        /*  border-radius: 50%;*/
        /*  line-height: 30px;*/
        /*}*/
        /*color: #ffffff;*/
        /*text-align: center;*/
      }
      .cell {
        display: flex;
        justify-content: center;
      }
    }
    /deep/ .el-table__header {
      .cell {
        display: flex;
        justify-content: center;
      }
    }

    /deep/ .el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
      border-bottom: unset;
    }
    /deep/ .el-table th.el-table__cell.is-leaf {
      font-size: 16px;
      color: rgba(0,0,0,0.6);
      /*border-bottom: unset;*/
    }
    /deep/ .el-table tr {
      background-color: #FFF;
      font-size: 16px;
      color: #000;
    }
  }
  /deep/ .el-table::before {
  display: none;
  }
</style>
