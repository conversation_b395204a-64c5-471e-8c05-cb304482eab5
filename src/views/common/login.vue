<template>
  <div class="page-site-login-old">
    <div class="site-content__wrapper">
      <div class="site-content">
        <div class="brand-info">
          <h2 class="brand-info__text">文明城市一体化综合管理平台</h2>
          <!-- <p class="brand-info__intro">后台管理</p> -->
        </div>
        <div v-if="loginType === 'password'" class="login-main">
          <h3>管理员登录</h3>
          <el-form
            class="login-form"
            :model="dataForm"
            :rules="dataRule"
            ref="dataForm"
            @keyup.enter.native="dataFormSubmit()"
            status-icon
          >
            <el-form-item prop="userName">
              <el-input
                v-model="dataForm.userName"
                placeholder="用户名/手机号"
              ></el-input>
              <img
                src="~@/assets/img/icon-user.png"
                class="icon-input"
                alt=""
              />
            </el-form-item>
            <el-form-item prop="password">
              <el-input
                v-model="dataForm.password"
                type="password"
                placeholder="密码"
              ></el-input>
              <img
                src="~@/assets/img/icon-password.png"
                class="icon-input"
                alt=""
              />
            </el-form-item>
            <el-form-item prop="captcha">
              <el-row>
                <el-col :span="14">
                  <el-input
                    v-model="dataForm.captcha"
                    placeholder="验证码"
                  ></el-input>
                  <img
                    src="~@/assets/img/icon-valid-code.png"
                    class="icon-input"
                    alt=""
                  />
                </el-col>
                <el-col :span="10" class="login-captcha">
                  <img :src="captchaPath" @click="getCaptcha()" alt="" />
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item>
              <el-button
                :disabled="submitDisabled"
                class="login-btn-submit"
                type="primary"
                @click="dataFormSubmit()"
                >登录</el-button
              >
            </el-form-item>
            <!--            <el-form-item>-->
            <!--              <el-button class="login-btn-change" type="text" @click="changeLoginType()">切换登录方式</el-button>-->
            <!--            </el-form-item>-->
          </el-form>
        </div>
        <div v-if="loginType === 'sms_code'" class="login-main">
          <h3>手机验证码登录</h3>
          <el-form
            class="login-form"
            :model="dataForm"
            :rules="dataRule"
            ref="dataForm"
            @keyup.enter.native="dataFormSubmit()"
            status-icon
          >
            <el-form-item prop="userName">
              <el-input
                v-model="dataForm.userName"
                placeholder="手机号"
              ></el-input>
              <img
                src="~@/assets/img/icon-user.png"
                class="icon-input"
                alt=""
              />
            </el-form-item>
            <el-form-item prop="password">
              <el-input
                v-model="dataForm.password"
                placeholder="验证码"
              ></el-input>
              <img
                src="~@/assets/img/icon-valid-code.png"
                class="icon-input"
                alt=""
              />
            </el-form-item>
            <el-form-item>
              <el-button
                class="login-btn-vericate"
                type="text"
                @click="getVerificationCode()"
                >获取验证码</el-button
              >
            </el-form-item>
            <el-form-item>
              <el-button
                :disabled="submitDisabled"
                class="login-btn-submit"
                type="primary"
                @click="dataFormSubmit()"
                >登录</el-button
              >
            </el-form-item>
            <el-form-item>
              <el-button
                class="login-btn-change"
                type="text"
                @click="changeLoginType()"
                >切换登录方式</el-button
              >
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { uuid } from '@/utils'
export default {
  data () {
    return {
      submitDisabled: false,
      captchaPath: '',
      loginType: 'password',
      dataForm: {
        userName: '',
        password: '',
        captcha: '',
        uuid: ''
      },
      dataRule: {
        userName: [
          { required: true, message: '帐号不能为空', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '密码不能为空', trigger: 'blur' }
        ],
        captcha: [
          { required: true, message: '验证码不能为空', trigger: 'blur' }
        ]
      },
      redirect: undefined
    }
  },
  created () {
    this.getCaptcha()
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  methods: {
    // 获取验证码
    getCaptcha () {
      this.dataForm.uuid = uuid()
      this.captchaPath = this.$http.adornUrl(`/auth/auth/login/captcha?uuid=${this.dataForm.uuid}`)
    },
    // 提交表单
    dataFormSubmit () {
      if (this.$route.query.redirect) {
        this.redirect = decodeURIComponent(window.location.href.substring(window.location.href.indexOf('redirect=') + 9))
      }
      this.submitDisabled = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const s = []
          s.push(this.dataForm.password)
          this.$http({
            url: this.$http.adornUrl('/auth/auth/login'),
            method: 'post',
            data: this.$http.adornData({
              'username': this.dataForm.userName,
              'password': this.encode('0x12', s),
              'captcha': this.dataForm.captcha,
              'uuid': this.dataForm.uuid,
              'grantType': this.loginType,
              'type': '1'
            })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message.info(data.msg)
              // oauth2用的token
              // this.$cookie.set('Authorization', data.obj.token_type + ' ' + data.obj.access_token)
              sessionStorage.setItem('Authorization', data.obj.token_type + ' ' + data.obj.access_token)
              // 把token对象放在sessionStorage中
              sessionStorage.setItem('token', data.obj.access_token)
              sessionStorage.setItem('oAuthToken', data.obj)
              this.$router.push({ path: this.redirect || '/home' })
            } else {
              this.submitDisabled = false
              this.$message.error(data.msg)
              this.getCaptcha()
            }
          })
        } else {
          this.submitDisabled = false
        }
      })
    },
    getVerificationCode () {
      var valid = this.dataForm.userName !== ''
      if (!valid) {
        this.$message.error('请输入手机号')
      }
      if (valid) {
        this.$http({
          url: this.$http.adornUrl('/verify/verifyCode'),
          method: 'post',
          data: this.$http.adornData({
            'mobile': this.dataForm.userName,
            'code': 'yqtjpt',
            'expireTime': 20
          })
        }).then(({ data }) => {
          if (!data || data.code !== 0) {
            this.$message.error(data.msg)
          }
        })
      }
    },
    changeLoginType () {
      this.loginType = this.loginType === 'password' ? 'sms_code' : 'password'
    },
    // 自己写的密码加密逻辑
    encode (key, s) {
      const keyVal = parseInt(key)
      const arr = []
      for (let i = 0; i < s.length; ++i) {
        const ele = s[i]
        arr.push(ele.length ^ keyVal)
        for (let j = 0; j < ele.length; ++j) {
          arr.push(ele[j].charCodeAt(0) ^ keyVal)
        }
      }
      arr.push(keyVal)
      const context = 'CV16' + arr.join('%')
      return context
    }
  }
}
</script>

<style lang="scss" scoped>
.page-site-login-old {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  // background-color: rgba(38, 50, 56, 0.6);
  overflow: hidden;
  &:before {
    position: fixed;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100%;
    height: 100%;
    content: "";
    background-image: url(~@/assets/img/login_bg.jpg);
    background-size: cover;
  }
  .site-content__wrapper {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    padding: 0;
    margin: 0;
    overflow-x: hidden;
    overflow-y: auto;
    background-color: transparent;
  }
  .site-content {
    min-height: 100%;
    padding: 30px 500px 30px 30px;
  }
  .brand-info {
    margin: 220px 100px 0 90px;
    color: #fff;
  }
  .brand-info__text {
    margin: 0 0 22px 0;
    font-size: 48px;
    font-weight: 400;
    text-transform: uppercase;
  }
  .brand-info__intro {
    margin: 10px 0;
    font-size: 16px;
    line-height: 1.58;
    opacity: 0.6;
  }
  .login-main {
    position: absolute;
    top: 0;
    right: 0;
    padding: 150px 60px 180px;
    width: 470px;
    min-height: 100%;
    background-color: #fff;
  }
  .login-title {
    font-size: 16px;
  }
  .login-captcha {
    overflow: hidden;
    > img {
      width: 100%;
      cursor: pointer;
    }
  }
  .login-btn-submit {
    width: 100%;
    margin-top: 20px;
  }
  .login-btn-vericate {
    padding-left: 280px;
  }
  .login-btn-change {
    padding-left: 260px;
  }
  .login-form {
    .el-form-item__content {
      position: relative;
    }
    .el-form-item {
      margin-bottom: 35px;
    }
    .el-input input {
      border-bottom: 1px solid #c0c0c0;
    }
    .el-input__inner {
      padding-left: 30px;
      border: none;
      border-radius: 0;
    }
    .icon-input {
      position: absolute;
      top: 10px;
      left: 5px;
    }
  }
}
</style>
