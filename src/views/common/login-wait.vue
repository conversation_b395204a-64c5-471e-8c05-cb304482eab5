<template>
  <div class="site-wrapper site-page--not-found">
    <div class="site-content__wrapper">
      <div class="site-content">
        <p class="not-found-desc">正在登录中，请耐心等待！</p>
      </div>
    </div>
  </div>
</template>

<script>
import {encrypt} from '@/utils/cryptoUtil'

export default {
  data () {
    return {
    }
  },
  created () {
    console.log('login-wait', this.$route.query.code)
    this.loginByWechat()
  },
  methods: {
    // 微信登录
    loginByWechat () {
      this.code = this.$route.query.code
      this.$http({
        url: this.$http.adornUrl('/wechat/api/open/tools/callBack'),
        method: 'get',
        params: this.$http.adornParams({
          'state': 'sip_cp_mp',
          'code': this.code
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.loginCallback(data.obj)
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    async loginCallback (obj) {
      if (obj.mobile) {
        this.$http({
          url: this.$http.adornUrl('/auth/auth/login'),
          method: 'post',
          data: this.$http.adornData({
            'username': obj.mobile,
            'password': obj.code,
            'grantType': 'one_code'
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message.info('登录成功')
            // this.$cookie.set('Authorization', data.obj.token_type + ' ' + data.obj.access_token)
            sessionStorage.setItem('Authorization', data.obj.token_type + ' ' + data.obj.access_token)
            sessionStorage.setItem('oAuthToken', data.obj)
            this.$router.replace({name: 'home'})
          } else {
            // this.submitDisabled = false
            this.$message.error('请通过手机号验证码绑定，绑定我才后续可直接扫码登录')
          }
        })
      } else {
        this.$router.push({
          path: '/login',
          query: {
            loginType: 'sms_code',
            isShowTab: 'bindPhone',
            unionId: obj.unionId,
            openId: obj.openid
          }
        })
      }
    }
  }
}
</script>

<style lang="scss">
  .site-wrapper.site-page--not-found {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: hidden;
    .site-content__wrapper {
      padding: 0;
      margin: 0;
      background-color: #fff;
    }
    .site-content {
      position: fixed;
      top: 15%;
      left: 50%;
      z-index: 2;
      padding: 30px;
      text-align: center;
      transform: translate(-50%, 0);
    }
    .not-found-title {
      margin: 20px 0 15px;
      font-size: 10em;
      font-weight: 400;
      color: rgb(55, 71, 79);
    }
    .not-found-desc {
      margin: 0 0 30px;
      font-size: 26px;
      text-transform: uppercase;
      color: rgb(118, 131, 143);
      > em {
        font-style: normal;
        color: #ee8145;
      }
    }
    .not-found-btn-gohome {
      margin-left: 30px;
    }
  }
</style>
