<template>
  <div class="site-wrapper page-login-new" id="login_page">
    <div class="site-content__wrapper">
      <div class="site-content">
        <div class="login-main">
          <h2>文明园区一体化综合管理平台</h2>
          <el-tabs v-show="isShowTab !== 'bindPhone'" v-model="loginType" @tab-click="handleClick" stretch>
            <el-tab-pane class="login-title" label="短信登录" name="sms_code"><h3/></el-tab-pane>
            <el-tab-pane class="login-title" label="密码登录" name="password"><h3/></el-tab-pane>
            <el-tab-pane class="login-title" label="扫码登录" name="wx_qrcode"><h3/></el-tab-pane>
          </el-tabs>
          <el-form v-show="loginType === 'password'" class="login-form" :model="dataForm" :rules="dataRule"
                   ref="dataForm"
                   @keyup.enter.native="dataFormSubmit()" status-icon>
            <h6 v-if="this.unionId" style="margin: 0px 0px 10px 0px">
              您的微信账号暂未绑定用户，请使用账号密码登录进行绑定，之后可直接扫码登录</h6>
            <el-form-item prop="userName">
              <el-input v-model="dataForm.userName" placeholder="用户名"></el-input>
              <img src="~@/assets/img/icon-user-new.png" class="icon-input" alt="" style="width: 21px; height: 21px">
            </el-form-item>
            <el-form-item prop="password">
              <el-input v-model="dataForm.password" type="password" placeholder="密码"></el-input>
              <img src="~@/assets/img/icon-password-new.png" class="icon-input" alt=""
                   style="width: 21px; height: 21px">
            </el-form-item>
            <el-form-item prop="captcha">
              <el-input v-model="dataForm.captcha" placeholder="验证码">
                <img :src="captchaPath" @click="getCaptcha()" alt="" slot="suffix">
              </el-input>
              <img src="~@/assets/img/icon-valid-code-new.png" class="icon-input" alt=""
                   style="width: 21px; height: 21px">
            </el-form-item>
            <el-form-item>
              <el-button :disabled="submitDisabled" class="login-btn-submit" type="primary" @click="dataFormSubmit()"
                         style="background-color: #42aefa; border-width:2px; border-color: #42aefa; opacity: 0.9">
                登录
              </el-button>
            </el-form-item>
          </el-form>
          <el-form v-show="loginType === 'sms_code'" class="login-form" :model="dataForm" :rules="dataRule"
                   ref="dataForm"
                   @keyup.enter.native="dataFormSubmit()" status-icon
                   :style="this.unionId ? {'padding-top': '7px'} : {'padding-top': '50px'}">
            <h4 v-if="this.unionId" >您的微信账号暂未绑定用户，请使用账号密码登录进行绑定，之后可直接扫码登录</h4>
            <el-form-item prop="userName">
              <el-input v-model="dataForm.userName" placeholder="手机号"></el-input>
              <img src="~@/assets/img/icon-user-new.png" class="icon-input" alt="" style="width: 21px; height: 21px">
            </el-form-item>
            <el-form-item prop="validateCode" style="padding-top: 40px">
              <el-input v-model="dataForm.validateCode" placeholder="短信验证码">
                <el-button type="text" @click="getVerificationCode()" slot="suffix" :disabled="countdown > 0">
                  {{ countdown > 0 ? `${countdown} 秒后重新发送` : '获取验证码' }}
                </el-button>
              </el-input>
              <img src="~@/assets/img/icon-valid-code-new.png" class="icon-input" alt=""
                   style="width: 21px; height: 21px">
            </el-form-item>
            <el-form-item>
              <el-button :disabled="submitDisabled" class="login-btn-submit" type="primary" @click="dataFormSubmit()"
                         style="background-color: #42aefa;  border-width:2px; border-color: #42aefa; opacity: 0.9">
                {{ unionId ? '绑定手机号' : '登录' }}
              </el-button>
            </el-form-item>
          </el-form>
          <el-form v-show="loginType === 'wx_qrcode'" class="login-form" :model="dataForm" :rules="dataRule"
                   style="margin-top: -20px"
                   ref="dataForm"
                   @keyup.enter.native="dataFormSubmit()" status-icon>
            <div id="wechat-qrcode"></div>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {uuid} from '@/utils'
import '../../utils/wxLogin'

export default {
  data () {
    return {
      countdown: 0,
      submitDisabled: false,
      captchaPath: '',
      canGetVerifyCode: true,
      loginType: this.$route.query.loginType || 'sms_code',
      unionId: this.$route.query.unionId || '',
      isShowTab: this.$route.query.isShowTab || '',
      timer: null,
      dataForm: {
        userName: '',
        password: '',
        validateCode: '',
        captcha: '',
        uuid: ''
      },
      dataRule: {
        userName: [
          {required: true, message: '帐号不能为空', trigger: 'blur'}
        ],
        password: [
          {validator: this.passwordValidate, trigger: 'blur'}
        ],
        validateCode: [
          {validator: this.passwordValidate, trigger: 'blur'}
        ],
        captcha: [
          {required: true, message: '验证码不能为空', trigger: 'blur'}
        ]
      }
    }
  },
  created () {
    this.getCaptcha()
  },
  mounted () {
    this.initWeChatLoginQRCode()
    // 处理根路径访问登录页面时，无法跳转的bug
    window.addEventListener('hashchange', (event) => {
      console.log(event)
      if (event.newURL.includes('login-wait')) {
        setTimeout(() => {
          if (document.getElementById('wechat-qrcode')) {
            this.$router.go(0)
          }
        }, 1500)
      }
    })
  },
  methods: {
    passwordValidate (rule, value, callback) {
      if (value && value.length > 0) {
        callback()
      } else {
        callback(new Error(this.loginType === 'password' ? '请输入密码' : '请输入短信验证码'))
      }
    },
    handleClick () {
      this.$refs['dataForm'].resetFields()
      this.dataForm.captcha = ''
      this.dataForm.password = ''
      this.dataForm.userName = ''
      this.dataForm.uuid = ''
      if (this.loginType === 'wx_qrcode') {
        this.$nextTick(() => {
          this.initWeChatLoginQRCode()
        })
      }
      if (this.loginType === 'password') {
        this.getCaptcha()
      }
      this.countdown = 0
      clearInterval(this.timer)
    },
    initWeChatLoginQRCode () {
      const result = new window.WxLogin({
        self_redirect: false,
        id: 'wechat-qrcode',
        appid: window.SITE_CONFIG['appID'],
        scope: 'snsapi_login',
        redirect_uri: encodeURIComponent(window.SITE_CONFIG['openRedirectUrl']),
        state: 'sip_cp_mp#wechat_redirect',
        response_type: 'code'
      })
    },
    // 获取验证码
    getCaptcha () {
      this.dataForm.uuid = uuid()
      this.captchaPath = this.$http.adornUrl(`/auth/auth/login/captcha?uuid=${this.dataForm.uuid}`)
    },
    // 提交表单
    dataFormSubmit () {
      if (this.$route.query.redirect) {
        this.redirect = decodeURIComponent(window.location.href.substring(window.location.href.indexOf('redirect=') + 9))
      }
      this.submitDisabled = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          let params = {}
          let path = ''
          if (this.unionId && this.loginType === 'sms_code') { // 微信扫码绑定手机号
            params = {
              unionId: this.$route.query.unionId,
              openid: this.$route.query.openId,
              mobile: this.dataForm.userName,
              code: this.dataForm.validateCode
            }
            path = '/wechat/api/open/tools/binding/phone'
          }
          if (this.loginType === 'sms_code' && !this.unionId) {
            this.dataForm.password = this.dataForm.validateCode
            params = {
              'username': this.dataForm.userName,
              'password': this.dataForm.password,
              'captcha': this.dataForm.captcha,
              'uuid': this.dataForm.uuid,
              'grantType': this.loginType,
              'platform': 'pc'
            }
            path = '/auth/auth/login'
          } else if (this.loginType === 'password') {
            const s = []
            s.push(this.dataForm.password)
            this.dataForm.password = this.encode('0x12', s)
            params = {
              'username': this.dataForm.userName,
              'password': this.dataForm.password,
              'captcha': this.dataForm.captcha,
              'uuid': this.dataForm.uuid,
              'grantType': this.loginType,
              'platform': 'pc'
            }
            path = '/auth/auth/login'
          }
          console.log('最后传参', params)
          this.$http({
            url: this.$http.adornUrl(path),
            method: 'post',
            data: this.$http.adornData(params)
          }).then(({data}) => {
            if (data && data.code === 0) {
              console.log('data绑定的', data)
              if (this.unionId) { // 再登录获取token
                this.$http({
                  url: this.$http.adornUrl('/auth/auth/login'),
                  method: 'post',
                  data: this.$http.adornData({
                    'username': data.obj.mobile,
                    'password': data.obj.code,
                    'grantType': 'one_code'
                  })
                }).then(({data}) => {
                  if (data && data.code === 0) {
                    this.$message.info('登录成功')
                    sessionStorage.setItem('Authorization', data.obj.token_type + ' ' + data.obj.access_token)
                    sessionStorage.setItem('oAuthToken', data.obj)
                    this.$router.replace({name: 'home'})
                  } else {
                    this.$message.error(data.msg)
                  }
                })
              } else { // 登录另外两种方式
                this.$message.info(data.msg)
                sessionStorage.setItem('Authorization', data.obj.token_type + ' ' + data.obj.access_token)
                sessionStorage.setItem('oAuthToken', JSON.stringify(data.obj))
                this.$router.push({ path: '/home' })
              }
            } else {
              this.submitDisabled = false
              this.$message.error(data.msg)
              this.getCaptcha()
            }
          })
        } else {
          this.submitDisabled = false
        }
      })
    },
    getVerificationCode () {
      var valid = this.dataForm.userName !== ''
      if (!valid) {
        this.$message.error('请输入手机号')
      }
      if (valid) {
        this.countdown = 60
        this.timer = setInterval(() => {
          if (this.countdown > 0) {
            this.countdown--
          } else {
            clearInterval(this.timer)
          }
        }, 1000)
        this.canGetVerifyCode = false
        this.$http({
          url: this.$http.adornUrl('/admin/message/sendShortLetter'),
          method: 'get',
          params: this.$http.adornParams({
            'phone': this.dataForm.userName
          })
        }).then(({data}) => {
          if (!data || data.code !== 0) {
            this.$message.error(data.msg)
          }
        })
      }
    },
    // 密码加密逻辑
    encode (key, s) {
      const keyVal = parseInt(key)
      const arr = []
      for (let i = 0; i < s.length; ++i) {
        const ele = s[i]
        arr.push(ele.length ^ keyVal)
        for (let j = 0; j < ele.length; ++j) {
          arr.push(ele[j].charCodeAt(0) ^ keyVal)
        }
      }
      arr.push(keyVal)
      const context = 'CV16' + arr.join('%')
      return context
    }
  }
}
</script>

<style lang="scss" scoped>
 .page-login-new {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(38, 50, 56, .6);
  overflow: hidden;

  &:before {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 2;
    width: 100%;
    height: 100%;
    content: "";
    background-image: url(~@/assets/img/login_bg-new.jpg);
    background-size: 100% 100%;
  }

  .site-content__wrapper {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    padding: 0;
    margin: 0;
    overflow-x: hidden;
    overflow-y: auto;
    background-color: transparent;
  }

  .site-content {
    min-height: 100%;
    padding: 30px 500px 30px 30px;
  }

  .brand-info {
    z-index: 2;
    position: absolute;
    margin: 12% 100px 0 11%;
    font-weight: bolder;
  }

  .brand-info__text {
    margin: 0 0 22px 0;
    font-size: 55px;
    font-weight: 400;
    text-transform: uppercase;
    font-family: Caflisch, serif;
    color: #031259;
  }

  .brand-info__intro {
    margin: 10px 0;
    font-size: 16px;
    line-height: 1.58;
    opacity: .8;
    font-family: Caflisch, serif;
    color: #031259;
  }

  .login-main {
    text-align: center;
    height: 580px;
    position: absolute;
    z-index: 2;
    top: 15%;
    right: 10%;
    bottom: 10%;
    padding: 34px 55px 53px;
    background: rgba(255, 255, 255, 0.75);
    box-shadow: 0px 12px 14px 1px rgba(1, 28, 60, 0.4);
    border-radius: 21px 21px 21px 21px;
    opacity: 1;
    border-image: linear-gradient(135deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0)) 1 1;
  }

  .login-title {
    font-size: 16px;
  }

  .login-captcha {
    overflow: hidden;

    > img {
      width: 100%;
      cursor: pointer;
    }
  }

  .login-btn-submit {
    width: 100%;
    margin-top: 40px;
  }

  .login-btn-submit:hover {
    border: 2px solid #acd8e8 !important;
  }

  .find_team:hover {
    color: #081f7e !important;
  }

  .login-btn-vericate {
    padding-left: 280px;
  }

  .login-btn-change {
    padding-left: 260px;
  }

  /deep/ .login-form {
    width: 350px;

    .el-form-item__content {
      position: relative;
    }

    .el-form-item {
      margin-bottom: 35px;
    }

    .el-input input {
      border-bottom: 1px solid #9b9898;
    }

    .el-input__inner {
      padding-left: 30px;
      border: none;
      border-radius: 0;
      background: transparent !important;
      color: black;
      font-weight: bolder;
    }

    .el-input__inner::placeholder {
      color: #818796;
    }

    .icon-input {
      position: absolute;
      top: 10px;
      left: 5px;
    }

    .el-input__suffix {
      right: 0px;

      .el-input__suffix-inner img {
        height: 100%;
        padding-bottom: 3px;
        border-radius: 5%;
      }
    }

    .el-form-item__error {
      padding-left: 30px;
      font-weight: bolder;
    }
  }
}

#wechat-qrcode {
  text-align: center;
}
</style>
