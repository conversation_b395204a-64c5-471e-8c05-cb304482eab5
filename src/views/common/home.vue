<template>
  <div class="mod-home">
    <div class="tab-menu" >
      <div class="tab-menu-item">
        <div @click="handleClick('first')" :class="activeName === 'first' ? 'is-active' : 'menu-item'"> 文明点位</div>
        <div @click="handleClick('second')" :class="activeName === 'second' ? 'is-active' : 'menu-item'"> 随手拍</div>
        <div @click="handleClick('third')" :class="activeName === 'third' ? 'is-active' : 'menu-item'"> 点位测评</div>
      </div>
    </div>
    <Position ref="position" v-if="activeName === 'first'"></Position>
    <Clap ref="clap" v-if="activeName === 'second'"></Clap>
    <Evaluation ref="evaluation" v-if="activeName === 'third'"></Evaluation>
  </div>
</template>

<script>
  import moment from 'moment'
  import Position from './components/position'
  import Clap from './components/clap'
  import Evaluation from './components/evaluation'
  export default {
    components: {
      Position,
      Clap,
      Evaluation
    },
    data () {
      return {
        activeName: 'first',
        year: moment().format('yyyy')
      }
    },
    activated () {
    },
    destroyed () {
      document.body.setAttribute('style', 'zoom: unset')
    },
    mounted () {
      this.$refs.position.init()
    },
    methods: {
      init () {
        // this.$refs.position.init()
      },
      handleClick (name) {
        this.activeName = name
        setTimeout(e => {
          if (this.activeName === 'first') {
            this.$refs.position.init()
          }
          if (this.activeName === 'second') {
            this.$refs.clap.init()
          }
          if (this.activeName === 'third') {
            this.$refs.evaluation.init()
          }
        }, 2)
      }
    }
  }
</script>

<style lang="scss" scoped>
.mod-home {
  line-height: 1.5;
.tab-menu {
  display: flex;
  display: -webkit-flex;
  justify-content: space-between;
  margin-bottom: 20px;
.tab-menu-item {
  display: flex;
  display: -webkit-flex;
  cursor: pointer;
  font-size: 16px;
.menu-item {
  margin-right: 40px;
  padding-bottom: 2px;
  color: #303133;
  /*border-bottom: 2px solid #303133;*/
}
.is-active {
  margin-right: 40px;
  padding-bottom: 2px;
  color: #0602d7;
  border-bottom: 2px solid #0602d7;
}
}
}
}
</style>
