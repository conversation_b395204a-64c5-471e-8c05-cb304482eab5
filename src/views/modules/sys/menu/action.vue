<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm">
      <el-form-item>
        <el-button
          type="primary"
          @click="addOrUpdateHandle()"
          v-if="dataForm.menuId"
          >创建操作</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      style="width: 100%"
    >
      <el-table-column
        prop="actionName"
        header-align="center"
        align="center"
        label="操作名称"
      >
        <template slot-scope="scope">
          <el-badge
            is-dot
            v-if="scope.row.status"
            type="success "
            class="status-badge"
          >
          </el-badge>
          <el-badge is-dot v-else type="danger" class="status-badge">
          </el-badge>
          <span>{{ scope.row.actionName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="actionCode"
        header-align="center"
        align="center"
        label="代码"
      >
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row.id)"
            >编辑</el-button
          >
          <el-button
            type="text"
            size="small"
            @click="apiBindHandle(scope.row.id)"
            >绑定API</el-button
          >
          <el-button
            type="text"
            size="small"
            @click="deleteHandle(scope.row.id)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></add-or-update>
    <api-bind
      v-if="apiBindVisible"
      ref="apiBind"
      @refreshDataList="getDataList"
    ></api-bind>
  </div>
</template>

<script>
import AddOrUpdate from './action-add-or-update'
import ApiBind from './action-api-bind'

export default {
  data () {
    return {
      dataForm: {
        menuId: null
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      apiBindVisible: false
    }
  },
  components: {
    AddOrUpdate,
    ApiBind
  },
  activated () {
  },
  methods: {
    init (menuId) {
      this.dataForm.menuId = menuId
      if (menuId) {
        this.getDataList(menuId)
      }
    },
    // 获取数据列表
    getDataList () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/action/findByMenuId'),
        method: 'get',
        params: this.$http.adornParams({
          'menuId': this.dataForm.menuId
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.obj
        } else {
          this.$message.error('加载操作失败')
        }
        this.dataListLoading = false
      })
    },
    // 新增 / 修改
    addOrUpdateHandle (id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id, this.dataForm.menuId)
      })
    },
    // 绑定api
    apiBindHandle (id) {
      this.apiBindVisible = true
      this.$nextTick(() => {
        this.$refs.apiBind.init(id)
      })
    },
    // 删除
    deleteHandle (id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/action/removeByIds'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': ids.join(',')
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    }
  }
}
</script>
