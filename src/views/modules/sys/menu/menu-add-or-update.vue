<template>
  <div v-loading="isLoading">
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
    >
      <el-form-item :label="'菜单名'" prop="name">
        <el-input v-model="dataForm.name" :placeholder="'菜单名'"></el-input>
      </el-form-item>
      <el-form-item label="上级菜单" prop="parentName">
        <el-popover
          ref="menuListPopover"
          placement="bottom-start"
          trigger="click"
        >
          <el-tree
            :data="menuList"
            :props="menuListTreeProps"
            node-key="id"
            :default-expanded-keys="[getRootPID(), dataForm.id]"
            ref="menuListTree"
            @current-change="menuListTreeCurrentChangeHandle"
            :highlight-current="true"
            :expand-on-click-node="false"
          >
          </el-tree>
        </el-popover>
        <el-input
          v-show="showParentName"
          v-model="dataForm.parentName"
          v-popover:menuListPopover
          :readonly="true"
          placeholder="点击选择上级菜单"
          class="menu-list__input"
        ></el-input>
      </el-form-item>
      <el-form-item label="菜单路由" prop="url">
        <el-input v-model="dataForm.url" placeholder="菜单路由">
          <template slot="append">
            <el-select v-model="dataForm.openTarget" style="width: 120px">
              <el-option :key="'_self'" :label="'窗口内打开'" :value="'_self'">
              </el-option>
              <el-option :key="'_blank'" :label="'新窗口'" :value="'_blank'">
              </el-option>
            </el-select>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="新菜单路由" prop="newUrl">
        <el-input v-model="dataForm.newUrl" placeholder="新菜单路由">
          <template slot="append">
            <el-select v-model="dataForm.openTarget" style="width: 120px">
              <el-option :key="'_self'" :label="'窗口内打开'" :value="'_self'">
              </el-option>
              <el-option :key="'_blank'" :label="'新窗口'" :value="'_blank'">
              </el-option>
            </el-select>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item :label="'备注'" prop="remark">
        <el-input v-model="dataForm.remark" :placeholder="'备注'"></el-input>
      </el-form-item>
      <el-form-item label="菜单分组" prop="groupType">
        <el-radio-group
          v-model="dataForm.groupType"
          @change="groupTypeChange(dataForm.groupType)"
        >
          <el-radio v-for="type in groupList" :label="type.id" :key="type.id">{{
            type.name
          }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="dataForm.status">
          <el-radio :label="true">启用</el-radio>
          <el-radio :label="false">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="排序号" prop="sequence">
        <el-input-number
          v-model="dataForm.sequence"
          controls-position="right"
          :min="0"
          label="排序号"
        ></el-input-number>
      </el-form-item>
      <div v-if="dataForm.groupType + '' !== '1' || !dataForm.groupType">
        <el-form-item v-if="dataForm.type !== 2" label="菜单图标" prop="icon">
          <el-row>
            <el-col :span="22">
              <el-popover
                ref="iconListPopover"
                placement="bottom-start"
                trigger="click"
                popper-class="mod-menu__icon-popover"
              >
                <div class="mod-menu__icon-inner">
                  <div class="mod-menu__icon-list">
                    <el-button
                      v-for="(item, index) in iconList"
                      :key="index"
                      @click="iconActiveHandle(item)"
                      :class="{ 'is-active': item === dataForm.icon }"
                    >
                      <icon-svg :name="item"></icon-svg>
                    </el-button>
                  </div>
                </div>
              </el-popover>
              <el-input
                v-model="dataForm.icon"
                v-popover:iconListPopover
                :readonly="true"
                placeholder="菜单图标名称"
                class="icon-list__input"
              ></el-input>
            </el-col>
            <el-col :span="2" class="icon-list__tips">
              <el-tooltip placement="top" effect="light">
                <div slot="content">
                  全站推荐使用SVG Sprite, 详细请参考:<a
                    href="//github.com/daxiongYang/renren-fast-vue/blob/master/src/icons/index.js"
                    target="_blank"
                    >icons/index.js</a
                  >描述
                </div>
                <i class="el-icon-warning"></i>
              </el-tooltip>
            </el-col>
          </el-row>
        </el-form-item>
      </div>
      <div v-if="dataForm.groupType + '' !== '0' && dataForm.groupType">
        <el-form-item v-if="dataForm.type !== 2" label="菜单图标" prop="icon">
          <el-upload
            :action="this.$http.adornUrl('/file/oss/upload')"
            class="avatar-uploader"
            :headers="myHeaders"
            :show-file-list="false"
            :data="{ serverCode: this.serverCode, media: false }"
            :on-success="handleAvatarSuccess"
          >
            <img
              v-if="dataForm.icon"
              :src="$http.adornUrl('/file' + dataForm.icon)"
              class="avatar"
            />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="resetForm(dataForm.id)">重置</el-button>
      <el-button type="primary" @click="dataFormSubmit()">保存菜单</el-button>
    </span>
  </div>
</template>

<script>
import { treeDataTranslate } from '@/utils'
import Icon from '@/icons'
import _ from 'lodash'
import Vue from 'vue'
const rootPID = 9999999999999

export default {
  data () {
    return {
      visible: false,
      isLoading: false,
      bakDataForm: {},
      dataForm: {
        id: null,
        groupType: 1,
        name: null,
        remark: null,
        parentId: null,
        parentName: '',
        url: null,
        newUrl: null,
        code: null,
        sequence: 0,
        icon: null,
        path: null,
        version: 0,
        status: true,
        openTarget: '_self',
        iconList: []
      },
      parentName: '',
      dataRule: {
        name: [
          { required: true, message: '菜单名称不能为空', trigger: 'blur' }
        ],
        parentName: [
          { required: true, message: '请选择上级菜单', trigger: 'blur' }
        ]
      },
      menuList: [],
      groupList: [{ id: 0, name: '管理后台' }, { id: 1, name: 'APP' }],
      menuListTreeProps: {
        label: 'name',
        children: 'children'
      },
      isHide: false,
      myHeaders: { Authorization: sessionStorage.getItem('Authorization') },
      serverCode: 'LocalServer',
      groupData0: '',
      groupData1: '',
      showParentName: true
    }
  },
  created () {
    this.iconList = Icon.getNameList()
    this.groupTypeChange()
  },
  methods: {
    init (id, parentId) {
      this.groupData0 = ''
      this.groupData1 = ''

      this.isLoading = true
      this.dataForm.id = id || undefined
      this.menuList = []
      this.$http({
        url: this.$http.adornUrl('/admin/user/authorities'),
        method: 'get',
        params: this.$http.adornParams({ groupType: 0 })
      }).then(({ data }) => {
        let tmp = [{ name: '根菜单', 'id': rootPID }]
        _.forEach(data.obj.menuList, (it) => {
          if (it.parentId == null) {
            it.parentId = rootPID
          }
        })
        tmp.push(...data.obj.menuList)
        this.menuList = treeDataTranslate(tmp)
        this.dataForm.parentId = parentId
      }).then(() => {
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          this.bakDataForm = _.cloneDeep(this.dataForm)
        })
      }).then(() => {
        if (!this.dataForm.id) {
          // 新增
          this.menuListTreeSetCurrentNode()
        } else {
          // 修改
          this.$http({
            url: this.$http.adornUrl(`/admin/resource`),
            method: 'get',
            params: this.$http.adornParams({ id: this.dataForm.id })
          }).then(({ data }) => {
            this.dataForm = data.obj
            this.dataForm.parentId = data.obj.parentId || rootPID
            if (this.dataForm.parentId + '' === '0') {
              this.dataForm.parentId = rootPID
            }
            this.menuListTreeSetCurrentNode()
            this.bakDataForm = _.cloneDeep(this.dataForm)
          })
        }
        this.isLoading = false
      })
    },
    getRootPID () {
      return rootPID
    },
    resetForm () {
      this.dataForm = _.cloneDeep(this.bakDataForm)
    },
    // 菜单树选中
    menuListTreeCurrentChangeHandle (data, node) {
      this.showParentName = false
      if (data) {
        this.dataForm.parentId = data.id
        this.dataForm.parentName = data.name
        this.$refs[`menuListPopover`].doClose()
      }
      this.showParentName = true
    },
    // 菜单树设置当前选中节点
    menuListTreeSetCurrentNode () {
      if (this.dataForm.parentId) {
        this.$refs.menuListTree.setCurrentKey(this.dataForm.parentId)
        this.dataForm.parentName = (this.$refs.menuListTree.getCurrentNode() || {})['name']
      } else {
        this.$refs.menuListTree.setCurrentKey([])
        this.dataForm.parentName = ''
      }
    },
    // 图标选中
    iconActiveHandle (iconName) {
      this.dataForm.icon = iconName
      this.$refs[`iconListPopover`].doClose()
    },
    handleAvatarSuccess (res, file) {
      this.dataForm.icon = res.obj.path
    },
    // 菜单分组更改
    groupTypeChange (data) {
      const icon = this.dataForm.icon
      if (data + '' === '1') {
        this.dataForm.icon = this.groupData1
        this.groupData0 = icon
        this.isHide = true
      } else {
        this.dataForm.icon = this.groupData0
        this.groupData1 = icon
        this.isHide = false
      }
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/admin/resource/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'id': this.dataForm.id || undefined,
              'type': this.dataForm.type,
              'groupType': this.dataForm.groupType,
              'name': this.dataForm.name,
              'parentId': this.dataForm.parentId === rootPID ? 0 : this.dataForm.parentId,
              'url': this.dataForm.url,
              'newUrl': this.dataForm.newUrl,
              'code': this.dataForm.code,
              'sequence': this.dataForm.sequence,
              'openTarget': this.dataForm.openTarget,
              'icon': this.dataForm.icon,
              'status': this.dataForm.status,
              'path': this.dataForm.path,
              'version': this.dataForm.version
            })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList', data.obj.id)
                }
              })
              this.isHide = false
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="scss">
.mod-menu {
  .menu-list__input,
  .icon-list__input {
    > .el-input__inner {
      cursor: pointer;
    }
  }

  &__icon-popover {
    width: 458px;
    overflow: hidden;
  }

  &__icon-inner {
    width: 478px;
    max-height: 258px;
    overflow-x: hidden;
    overflow-y: auto;
  }

  &__icon-list {
    width: 458px;
    padding: 0;
    margin: -8px 0 0 -8px;

    > .el-button {
      padding: 8px;
      margin: 8px 0 0 8px;

      > span {
        display: inline-block;
        vertical-align: middle;
        width: 18px;
        height: 18px;
        font-size: 18px;
      }
    }
  }

  .icon-list__tips {
    font-size: 18px;
    text-align: center;
    color: #e6a23c;
    cursor: pointer;
  }
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
