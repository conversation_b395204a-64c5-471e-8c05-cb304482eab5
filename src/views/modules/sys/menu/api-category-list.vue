<template>
  <div
    class="mod-dict"
    v-if="!categorySaveOrUpdate"
    v-loading="categoryEditLoading"
  >
    <el-form :inline="true" :model="dataForm">
      <el-form-item>
        <el-button
          title="类目新增"
          class="el-icon-circle-plus-outline"
          circle
          style="font-size: 30px; border: 0px; padding: 5px"
          @click="addOrUpdateHandle()"
        ></el-button>
        <el-button
          title="完成/返回"
          class="el-icon-finished"
          circle
          style="font-size: 30px; border: 0px; padding: 5px"
          @click="returnPrimaryPageOrRefreshList(true, false)"
        ></el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border style="width: 100%">
      <table-tree-column
        prop="name"
        header-align="center"
        treeKey="id"
        label="类目名称"
      >
      </table-tree-column>
      <el-table-column
        prop="sequence"
        header-align="center"
        align="center"
        label="排序"
      >
      </el-table-column>
      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        label="状态"
      >
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            active-color="#13ce66"
            inactive-color="#ff4949"
            @change="setEnableHandle(scope.row.id, scope.row.status)"
          >
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column header-align="center" align="center" label="操作">
        <template slot-scope="scope">
          <el-button
            title="编辑"
            class="el-icon-edit"
            circle
            style="font-size: 20px; border: 0px; padding: 5px"
            @click="addOrUpdateHandle(scope.row.id)"
          ></el-button>
          <el-button
            title="删除"
            class="el-icon-delete"
            circle
            style="font-size: 20px; border: 0px; padding: 5px"
            @click="deleteHandle(scope.row.id)"
          ></el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <!-- 弹窗, 新增 / 修改 -->
  <div v-else>
    <add-or-update
      ref="addOrUpdate"
      @refreshDataList="returnPrimaryPageOrRefreshList"
    ></add-or-update>
  </div>
</template>

<script>
import TableTreeColumn from '@/components/table-tree-column'
import AddOrUpdate from './api-category-add-or-update'
import { treeDataTranslate } from '@/utils'
export default {
  data () {
    return {
      categoryEditLoading: false,
      categorySaveOrUpdate: false,
      dataForm: {},
      dataList: [],
      dataListLoading: false
    }
  },
  components: {
    TableTreeColumn,
    AddOrUpdate
  },
  methods: {
    // 接口类目列表页初始化
    init () {
      this.categorySaveOrUpdate = false
      this.getDataList()
    },
    // 获取数据列表
    getDataList () {
      this.categorySaveOrUpdate = false
      this.categoryEditLoading = true
      this.$parent.$parent.$parent.categoryIndex = '接口类目编辑列表'
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/api/category/all'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        this.dataList = treeDataTranslate(data, 'id')
        this.dataListLoading = false
        this.categoryEditLoading = false
      })
    },
    // 新增 / 修改
    addOrUpdateHandle (id) {
      this.categorySaveOrUpdate = true
      if (id) {
        this.$parent.$parent.$parent.categoryIndex = '接口类目编辑'
      } else {
        this.$parent.$parent.$parent.categoryIndex = '接口类目新增'
      }
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle (id) {
      this.$confirm(`确定删除该类目?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.categoryEditLoading = true
        this.$http({
          url: this.$http.adornUrl(`/admin/api/category/removeByIds`),
          method: 'get',
          params: this.$http.adornParams({ 'ids': id })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.returnPrimaryPageOrRefreshList(false, true)
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      }).catch(() => { })
    },
    // 返回接口管理主页或仅刷新接口类目列表和主页面menu
    returnPrimaryPageOrRefreshList (isReturn, needUpdate) {
      if (!isReturn) {
        this.getDataList()
      }
      this.$emit('returnPrimaryPage', isReturn, needUpdate)
    },
    // 启用、禁用
    setEnableHandle (id, enable) {
      this.$confirm(`确定${enable ? '启用' : '禁用'}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.categoryEditLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/api/category/setEnable'),
          method: 'post',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.returnPrimaryPageOrRefreshList(false, true)
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      }).catch(() => { })
    }
  }
}
</script>
