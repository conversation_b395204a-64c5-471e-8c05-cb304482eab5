<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item
            label="资源名称"
            prop="actionName"
            :error="actionNameError"
          >
            <el-input
              v-model="dataForm.actionName"
              placeholder="资源名称"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="资源编码"
            prop="actionCode"
            :error="actionCodeError"
          >
            <el-input
              v-model="dataForm.actionCode"
              placeholder="最好用A:B:C的形式，否则容易重复！！"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item
            label="资源描述"
            prop="actionDesc"
            :error="actionDescError"
          >
            <el-input
              v-model="dataForm.actionDesc"
              :rows="5"
              type="textarea"
              placeholder="资源描述"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="优先级" prop="priority" :error="priorityError">
            <el-input-number
              v-model="dataForm.priority"
              controls-position="right"
              :min="0"
            ></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status" :error="statusError">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="true">启用</el-radio>
              <el-radio :label="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <!--      <el-row :gutter="20">-->
      <!--        <el-col :span="12">-->
      <!--          <el-form-item label="服务名称" prop="serviceId" :error="serviceIdError">-->
      <!--            <el-input v-model="dataForm.serviceId" placeholder="服务名称"></el-input>-->
      <!--          </el-form-item>-->
      <!--        </el-col>-->
      <!--      </el-row>-->
      <!--      <el-row :gutter="20">-->
      <!--        <el-col :span="12">-->
      <!--          <el-form-item label="绑定的api_id" prop="bindApiId" :error="bindApiIdError">-->
      <!--            <el-input v-model="dataForm.bindApiId" placeholder="绑定的api_id"></el-input>-->
      <!--          </el-form-item>-->
      <!--        </el-col>-->
      <!--      </el-row>-->
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: null,
        version: null,
        actionCode: '',
        actionName: '',
        actionDesc: '',
        menuId: '',
        priority: '',
        status: true,
        serviceId: '',
        bindApiId: ''
      },
      dataRule: {
        actionName: [
          { required: true, message: '资源名称不能为空', trigger: 'blur' }
        ],
        actionCode: [
          { required: true, message: '编码不能为空', trigger: 'blur' }
        ],
        priority: [
          { required: true, message: '优先级 越小越靠前不能为空', trigger: 'blur' }
        ]
      },
      actionCodeError: null,
      actionNameError: null,
      actionDescError: null,
      menuIdError: null,
      priorityError: null,
      statusError: null,
      persistError: null,
      serviceIdError: null,
      bindApiIdError: null
    }
  },
  methods: {
    init (id, menuId) {
      this.dataForm.id = id || null
      this.dataForm.menuId = menuId
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/action`),
            method: 'get',
            params: this.$http.adornParams({ id: this.dataForm.id })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/admin/action/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>
