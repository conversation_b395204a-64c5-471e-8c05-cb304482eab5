<template>
  <div>
    <list
      v-if="orgListVisible"
      ref="orgList"
    ></list>
  </div>
</template>

<script>
import List from '../common-list.vue'
export default {
  data () {
    return {
      orgListVisible: false,
      treeUrl: '/admin/org/tree/user'
    }
  },
  components: {
    List
  },
  activated () {
    this.init()
  },
  methods: {
    init () {
      this.orgListVisible = true
      this.$nextTick(() => {
        this.$refs.orgList.treeUrl = '/admin/org/tree/user'
        this.$refs.orgList.getDataList()
      })
    }
  }

}
</script>


List<style>
</style>