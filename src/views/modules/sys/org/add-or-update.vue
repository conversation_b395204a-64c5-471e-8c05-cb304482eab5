<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="组织编号" prop="code" :error="codeError">
            <el-input v-model="dataForm.code" placeholder="编号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="组织名称" prop="name" :error="nameError">
            <el-input v-model="dataForm.name" placeholder="名称"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="上级组织" prop="parentId" :error="parentIdError">
            <el-popover
              ref="orgListPopover"
              placement="bottom-start"
              trigger="click"
            >
              <el-tree
                style="height: 375px; overflow: auto"
                :data="orgList"
                :props="orgProps"
                node-key="id"
                ref="orgTree"
                @current-change="orgTreeCurrentChangeHandle"
                :default-expand-all="true"
                :highlight-current="true"
                :expand-on-click-node="false"
              >
              </el-tree>
            </el-popover>
            <el-input
              v-model="dataForm.parentName"
              v-popover:orgListPopover
              :readonly="true"
              placeholder="点击选择上级组织"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sequence" :error="sequenceError">
            <el-input-number
              v-model="dataForm.sequence"
              controls-position="right"
              :min="0"
            ></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="所在区域">
            <el-col :span="12">
              <el-select
                ref="regionSelected"
                v-model="dataForm.regionCode"
                placeholder="选择所在区域"
                clearable
              >
                <el-option
                  v-for="item in regionList"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                >
                </el-option>
              </el-select>
            </el-col>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  data () {
    let validateCode = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入组织编码'))
      } else {
        this.$http({
          url: this.$http.adornUrl('/admin/org/code/unique'),
          method: 'get',
          params: this.$http.adornParams({ id: this.dataForm.id, code: value })
        }).then(({ data }) => {
          if (!data.obj) {
            callback(new Error('行组织编码重复'))
          } else {
            callback()
          }
        })
      }
    }
    return {
      visible: false,
      regionList: {},
      dataForm: {
        id: null,
        version: null,
        code: '',
        name: '',
        sequence: '',
        parentId: '',
        parentName: '',
        regionCode: '',
        regionName: ''
      },
      dataRule: {
        code: [
          { required: true, message: '编码不能为空', trigger: 'blur' },
          { max: 50, message: '编码长度不能超过50个字符', trigger: 'blur' },
          { validator: validateCode, trigger: 'blur' }
        ],
        name: [
          { required: true, message: '名称不能为空', trigger: 'blur' },
          { max: 50, message: '名称长度不能超过50个字符', trigger: 'blur' }
        ],
        sequence: [
          { required: true, message: '排序不能为空', trigger: 'blur' }
        ]
      },
      statusList: [],
      orgList: [],
      orgProps: {
        label: 'name',
        children: 'children'
      },
      codeError: null,
      nameError: null,
      sequenceError: null,
      parentIdError: null,
      levelError: null,
      treeUrl: ''
    }
  },
  components: {
  },
  methods: {
    init (id, pid, treeUrl) {
      this.dataForm.id = id || null
      this.$http({
        url: this.$http.adornUrl(treeUrl),
        method: 'get',
        params: this.$http.adornParams({ id: id })
      }).then(({ data }) => {
        this.orgList = data.obj || []
      }).then(() => {
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          this.dataForm.parentId = pid || null
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/admin/org`),
              method: 'get',
              params: this.$http.adornParams({ id: this.dataForm.id })
            }).then(({ data }) => {
              if (data && data.code === 0) {
                this.dataForm = data.obj
              }
            })
          }
          this.orgListTreeSetCurrentNode()
          this.regionInit()
        })
      })
    },
    // 区域树选中
    orgTreeCurrentChangeHandle (data, node) {
      this.dataForm.parentId = data.id
      this.dataForm.parentName = data.name
      this.$refs[`orgListPopover`].doClose()
    },
    // 区域树设置当前选中节点
    orgListTreeSetCurrentNode () {
      let key = this.dataForm.parentId
      if (key) {
        this.$refs.orgTree.setCurrentKey(key)
        this.dataForm.parentName = (this.$refs.orgTree.getCurrentNode() || {})['name']
      } else {
        this.$refs.orgTree.setCurrentKey([])
        this.dataForm.parentName = ''
      }
    },
    // 区域下拉
    regionInit () {
      this.$http({
        url: this.$http.adornUrl(`/position/area/top`),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.regionList = data.obj
        }
      })
    },
    // 表单提交
    dataFormSubmit () {
      if (this.dataForm.regionCode) {
        this.dataForm.regionName = this.$refs.regionSelected.selected.label
      } else {
        this.dataForm.regionName = ''
      }
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/admin/org/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="scss">
.el-select {
  width: 100%;
}
.el-cascader {
  width: 100%;
}
</style>
