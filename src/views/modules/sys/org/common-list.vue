<template>
  <div class="mod-config">
    <el-row>
      <el-col :span="6">
        <el-card shadow="hover" class="box-card">
          <el-input placeholder="输入关键字进行过滤" v-model="filterText">
          </el-input>
          <div style="overflow: hidden; width: 100%">
            <div
              style="
                overflow-y: auto;
                max-height: 800px;
                min-width: 105%;
                max-width: 700px;
              "
            >
              <el-tree
                class="filter-tree"
                :data="treeData"
                :props="props"
                :highlight-current="true"
                :expand-on-click-node="false"
                :filter-node-method="filterNode"
                :default-expand-all="true"
                @current-change="currentChange"
                ref="tree"
              >
              </el-tree>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="18">
        <el-card shadow="hover" style="margin-left: 20px">
          <el-form :model="data" ref="dataForm" label-width="80px">
            <el-row :gutter="20" class="mb8">
              <el-col :span="1.5">
                <el-button
                  icon="el-icon-plus"
                  v-if="isAuth('org-add') || isAuth('org-add2')"
                  type="primary"
                  @click="addHandle()"
                  >新增</el-button
                >
              </el-col>
              <el-col :span="1.5">
                <el-button
                  icon="el-icon-edit"
                  v-if="isAuth('org-update') || isAuth('org-update2')"
                  type="warning"
                  @click="updateHandle()"
                  >编辑</el-button
                >
              </el-col>
              <el-col :span="1.5">
                <el-button
                  icon="el-icon-delete"
                  v-if="isAuth('org-delete') || isAuth('org-delete2')"
                  type="danger"
                  @click="deleteHandle()"
                  >删除</el-button
                >
              </el-col>
            </el-row>
            <br />
            <el-table :data="orgList" border tyle="width: 100%">
              <!-- @selection-change="selectionChangeHandle" -->
              <!-- <el-table-column type="selection" width="50" align="center" /> -->
              <el-table-column
                label="所在区域"
                align="center"
                prop="regionName"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                label="组织名称"
                align="center"
                prop="name"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                label="组织编号"
                align="center"
                prop="code"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                label="上级组织"
                align="center"
                prop="parentName"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                label="排序"
                align="center"
                prop="sequence"
                width="120"
              />
              <!-- <el-table-column label="状态" align="center">
                <template slot-scope="scope">
                  <el-switch
                    v-model="scope.row.status"
                    :active-value="true"
                    :inactive-value="false"
                    @change="handleStatusChange(scope.row)"
                  ></el-switch>
                </template>
              </el-table-column> -->
              <el-table-column
                label="创建时间"
                align="center"
                prop="createDate"
                width="160"
              >
                <template slot-scope="scope">
                  <span>{{ scope.row.createDate }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="操作"
                align="center"
                width="160"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="scope">
                  <el-button
                    v-if="isAuth('org-update')"
                    type="text"
                    @click="updateHandle(scope.row.id)"
                    >编辑</el-button
                  >
                  <el-button
                    v-if="isAuth('org-delete')"
                    type="text"
                    @click="deleteHandle(scope.row.id)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>

            <!-- <el-row :gutter="20">
              <el-col :span="10" :offset="2">
                <el-form-item label="组织名称:" prop="name">
                  <span align="left" v-model="data.name" :title="data.name">{{data.name}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item label="组织编号:" prop="code">
                  <span align="left" v-model="data.code" :title="data.code">{{data.code}}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="10" :offset="2">
                <el-form-item label="上级组织:" prop="parentName">
                  <span align="left" v-model="data.parentName" :title="data.parentName">{{data.parentName}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item label="排序:" prop="sequence">
                  <span align="left" v-model="data.sequence" :title="data.sequence">{{data.sequence}}</span>
                </el-form-item>
              </el-col>
            </el-row> -->
          </el-form>
        </el-card>
      </el-col>
    </el-row>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './add-or-update'
export default {
  watch: {
    filterText (val) {
      this.$refs.tree.filter(val)
    }
  },
  data () {
    return {
      filterText: '',
      treeData: [],
      props: {
        key: 'id',
        label: 'name',
        children: 'children'
      },
      data: {
        id: null,
        code: '',
        name: '',
        parentName: '',
        sequence: '',
        typeName: '',
        longitude: '',
        latitude: ''
      },
      companyDataForm: {
        standardNum: null,
        standardFilledInNum: null,
        standardNotReportNum: null,
        readyProjectNum: null,
        readyProjectFilledInNum: null,
        readyProjectNotReportNum: null
      },
      addOrUpdateVisible: false,
      orgList: [],
      treeUrl: ''
    }
  },
  components: {
    AddOrUpdate
  },

  // activated () {
  //   this.getDataList()
  // },
  methods: {
    filterNode (value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    // 获取数据列表
    getDataList () {
      this.$http({
        url: this.$http.adornUrl(this.treeUrl),
        method: 'get',
        params: this.$http.adornParams({ level: 1 })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.treeData = data.obj
        } else {
          this.treeData = []
        }
        this.data = {}
      })
      if (this.data.id) {
        this.getOrgById(this.data.id)
      }
    },
    currentChange (data) {
      if (data) {
        this.data.id = data.id
      }
      this.getOrgById(this.data.id)
    },
    getOrgById (id) {
      this.$http({
        url: this.$http.adornUrl('/admin/org'),
        method: 'get',
        params: this.$http.adornParams({
          id: id
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.data = data.obj
        } else {
          this.data = {}
        }
        this.orgList = []
        this.orgList.push(this.data)
      })
    },
    // 新增 / 修改
    addHandle () {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        if (!this.data.id) {
          if (this.treeUrl + '' === '/admin/org/tree/user') {
            this.data.id = this.treeData[0].id
          }
        }

        this.$refs.addOrUpdate.init(null, this.data.id, this.treeUrl)
      })
    },
    // 新增 / 修改
    updateHandle () {
      if (!this.data.id) {
        this.$message.error('请选择组织')
        return
      }
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        if (!this.data.id) {
          if (this.treeUrl + '' === '/admin/org/tree/user') {
            this.data.id = this.treeData[0].id
          }
        }
        this.$refs.addOrUpdate.init(this.data.id, null, this.treeUrl)
      })
    },
    // 删除
    deleteHandle () {
      if (!this.data.id) {
        this.$message.error('请选择组织')
        return
      }
      this.$confirm(`确定进行删除操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/org/removeByIds'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': this.data.id
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.orgList = []
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.getDataList()
                this.data = {}
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    }
  }
}
</script>
