<template>
  <el-dialog
  append-to-body
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="应用信息" name="first">
        <el-form
          :model="dataForm"
          :rules="dataRule"
          ref="dataForm"
          label-width="120px"
        >
          <el-form-item label="应用图标" prop="icon">
            <el-upload
              class="avatar-uploader"
              :action="this.$http.adornUrl(`/file/oss/upload`)"
              :headers="myHeaders"
              :data="{ serverCode: this.serverCode, media: false }"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
            >
              <img
                v-if="dataForm.icon"
                :src="$http.adornUrl(dataForm.icon)"
                class="avatar"
              />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-form-item>
          <el-form-item label="AppId" prop="appId">
            <el-input
              v-model="dataForm.appId"
              placeholder="AppId"
              disabled
            ></el-input>
          </el-form-item>
          <el-form-item label="开发者" prop="author">
            <el-input v-model="dataForm.author" placeholder="开发者"></el-input>
          </el-form-item>
          <el-form-item label="应用名称" prop="name">
            <el-input v-model="dataForm.name" placeholder="应用名称"></el-input>
          </el-form-item>
          <el-form-item label="英文名称" prop="code">
            <el-input v-model="dataForm.code" placeholder="英文名称"></el-input>
          </el-form-item>
          <el-form-item label="应用类型" prop="type">
            <el-dict :code="'app-type'" v-model="dataForm.type"></el-dict>
          </el-form-item>
          <el-form-item label="官网地址" prop="website">
            <el-input
              v-model="dataForm.website"
              placeholder="官网地址"
            ></el-input>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="true">启用</el-radio>
              <el-radio :label="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="描述" prop="remark">
            <el-input
              v-model="dataForm.remark"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4 }"
              placeholder="描述"
            ></el-input>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="开发信息" name="second">
        <el-form
          :model="devInfoForm"
          :rules="devInfoRule"
          ref="devInfoForm"
          label-width="150px"
        >
          <el-form-item label="AppId" prop="clientId">
            <el-input
              v-model="devInfoForm.clientId"
              placeholder="AppId"
              disabled
            ></el-input>
          </el-form-item>
          <el-form-item label="SecretKey" prop="clientSecret">
            <el-input
              v-if="showSecret"
              v-model="devInfoForm.clientSecret"
              placeholder="AppId"
              readonly
            ></el-input>
            <span v-if="showSecret" style="color: #f56c6c"
              >本页面关闭后将不再存储和显示SecretKey，请妥善保存。</span
            >
            <el-button v-if="!showSecret" type="text" @click="resetConfirm"
              >重置密钥</el-button
            >
          </el-form-item>
          <el-form-item label="授权类型" prop="types">
            <el-checkbox-group v-model="devInfoForm.types">
              <el-checkbox
                v-for="item in authTypeList"
                :label="item.code"
                :key="item.code"
                >{{ item.name }}</el-checkbox
              >
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="授权范围" prop="scope">
            <el-input
              v-model="devInfoForm.scope"
              placeholder="授权范围"
            ></el-input>
          </el-form-item>
          <el-form-item label="访问令牌有效期" prop="accessTokenValidity">
            <el-input
              v-model="devInfoForm.accessTokenValidity"
              placeholder="访问令牌有效期"
            >
              <template slot="append">秒</template>
            </el-input>
          </el-form-item>
          <el-form-item label="刷新令牌有效期" prop="refreshTokenValidity">
            <el-input
              v-model="devInfoForm.refreshTokenValidity"
              placeholder="刷新令牌有效期"
            >
              <template slot="append">秒</template>
            </el-input>
          </el-form-item>
          <el-form-item label="第三方登录回调地址" prop="webServerRedirectUri">
            <el-input
              v-model="devInfoForm.webServerRedirectUri"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4 }"
              placeholder="第三方登录回调地址"
            ></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="additionalInformation">
            <el-input
              v-model="devInfoForm.additionalInformation"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4 }"
              placeholder="备注"
            ></el-input>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="功能授权" name="third">
        <el-form
          :model="apiForm"
          :rules="apiRule"
          ref="apiForm"
          label-width="150px"
        >
          <el-transfer
            filterable
            :filter-method="filterMethod"
            filter-placeholder="请输入接口名称"
            v-model="apiForm.pathIdList"
            :titles="['选择接口', '已选择接口']"
            :data="appApiList"
          >
          </el-transfer>
        </el-form>
      </el-tab-pane>
    </el-tabs>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Vue from 'vue'
export default {
  data () {
    return {
      visible: false,
      showSecret: true,
      hasDevInfo: false,
      activeName: 'first',
      serverCode: 'LocalServer',
      appApiList: [],
      myHeaders: { Authorization: sessionStorage.getItem('Authorization') },
      apiForm: {
        pathIdList: [],
        appId: null
      },
      dataForm: {
        id: null,
        version: 0,
        code: null,
        name: null,
        appId: null,
        type: null,
        status: false,
        website: null,
        author: null,
        icon: null,
        remark: null
      },
      devInfoForm: {
        clientId: null,
        clientSecret: null,
        scope: null,
        types: [],
        authorizedGrantTypes: null,
        webServerRedirectUri: null,
        accessTokenValidity: null,
        refreshTokenValidity: null,
        additionalInformation: null,
        autoapprove: 'false'
      },
      dataRule: {
        name: [
          { required: true, message: '应用名称不能为空', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '英文名称不能为空', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '应用类型不能为空', trigger: 'blur' }
        ]
      },
      devInfoRule: {
        types: [
          { required: true, message: '授权类型不能为空', trigger: 'change' }
        ],
        accessTokenValidity: [
          { required: true, message: '访问令牌有效期不能为空', trigger: 'change' }
        ],
        refreshTokenValidity: [
          { required: true, message: '刷新令牌有效期不能为空', trigger: 'change' }
        ]
      },
      apiRule: {},
      authTypeList: []
    }
  },
  methods: {
    init (id) {
      this.getAuthTypeList()
      this.showSecret = true
      this.activeName = 'first'
      this.dataForm.id = id || null
      this.visible = true
      this.getAppApiList()
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.$refs['devInfoForm'].resetFields()
        this.apiForm.pathIdList = []
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/app`),
            method: 'get',
            params: this.$http.adornParams({ id: this.dataForm.id })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
              if (data.obj.clientInfo) {
                this.showSecret = false
                this.devInfoForm = data.obj.clientInfo
              } else {
                this.devInfoForm.clientId = this.dataForm.appId
                this.getSecret()
              }
              this.apiForm.pathIdList = data.obj.apiIdList
              this.apiForm.appId = this.dataForm.appId
            }
          })
        } else {
          this.getAppId()
          this.getSecret()
        }
      })
    },
    filterMethod (query, item) {
      return item.label.indexOf(query) > -1
    },
    resetConfirm () {
      this.$confirm('请注意：重置密钥立即生效，所有使用旧密钥的接口将立即失效！你确定要重置开发密钥吗？', '温馨提示', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确定重置',
        cancelButtonText: '取消'
      }).then(() => {
        this.resetSecret()
      }).catch(action => {
        console.log(action)
      })
    },
    resetSecret () {
      this.$http({
        url: this.$http.adornUrl(`/admin/app/client/secret/reset`),
        method: 'get',
        params: this.$http.adornParams({ appId: this.dataForm.appId })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.$message.success(data.msg)
          this.devInfoForm.clientSecret = data.obj
          this.showSecret = true
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    handleClick (tab, event) {
      console.log(tab, event)
    },
    getAppId () {
      this.$http({
        url: this.$http.adornUrl(`/admin/app/appId`),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataForm.appId = data.obj
          this.devInfoForm.clientId = data.obj
          this.apiForm.appId = data.obj
        }
      })
    },
    getSecret () {
      this.$http({
        url: this.$http.adornUrl(`/admin/app/appId`),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.devInfoForm.clientSecret = data.obj
        }
      })
    },
    getAuthTypeList () {
      this.$http({
        url: this.$http.adornUrl(`/admin/dict/parent`),
        method: 'get',
        params: this.$http.adornParams({ code: 'auth-type' })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.authTypeList = data.obj || []
        }
      })
    },
    submit () {
      if (this.activeName === 'first') {
        this.dataFormSubmit()
      }
      if (this.activeName === 'second') {
        this.devFormSubmit()
      }
      if (this.activeName === 'third') {
        this.apiSubmit()
      }
    },
    getAppApiList () {
      this.appApiList = []
      this.$http({
        url: this.$http.adornUrl(`/admin/appApi/all`),
        method: 'get'
      }).then(({ data }) => {
        data.forEach((item, index) => {
          this.appApiList.push({
            label: item.path + '-' + item.name,
            key: item.id
          })
        })
      })
    },
    //
    apiSubmit () {
      this.$refs['apiForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/admin/app/path/savePath`),
            method: 'post',
            data: this.$http.adornData(this.apiForm)
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/admin/app/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    // 开发信息表单提交
    devFormSubmit () {
      this.$refs['devInfoForm'].validate((valid) => {
        if (valid) {
          this.devInfoForm.authorizedGrantTypes = null
          if (this.devInfoForm.types) {
            this.devInfoForm.authorizedGrantTypes = this.devInfoForm.types.join(',')
          }
          this.$http({
            url: this.$http.adornUrl(`/admin/app/client/save`),
            method: 'post',
            data: this.$http.adornData(this.devInfoForm)
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 500
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    // 上传图片成功
    handleAvatarSuccess (res, file) {
      if (res.success) {
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 500
        })
        this.dataForm.icon = res.obj.path
      } else {
        this.$message.error('上传失败')
      }
    },
    beforeAvatarUpload: function (file) {
      let isAccept = ['image/jpeg', 'image/png', 'image/bmp'].indexOf(file.type) !== -1
      let isLt2M = file.size / 1024 / 1024 < 2

      if (!isAccept) {
        this.$message.error('上传图片只能是图片!')
      }
      if (!isLt2M) {
        this.$message.error('上传文件大小不能超过 2MB!')
      }
      return isAccept && isLt2M
    }
  }
}
</script>
<style lang="scss">
.el-transfer-panel {
  width: 300px;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
</style>
