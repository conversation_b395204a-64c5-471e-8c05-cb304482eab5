<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="接口名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" @click="queryPage()">查询</el-button>
        <el-button icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button icon="el-icon-delete" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50">
      </el-table-column>
       <el-table-column
        prop="name"
        header-align="center"
        align="center"
        label="接口名称">
      </el-table-column>
         <el-table-column
        prop="requestType"
        header-align="center"
        align="center"
        label="接口请求方式">
      </el-table-column>
         <el-table-column
        prop="apiUrl"
        header-align="center"
        align="center"
        label="接口路径">
      </el-table-column>
         <el-table-column
        prop="contentType"
        header-align="center"
        align="center"
        label="响应类型">
      </el-table-column>
         <el-table-column
        prop="apiDesc"
        header-align="center"
        align="center"
        label="接口描述">
      </el-table-column>
         <el-table-column
        prop="open"
        header-align="center"
        align="center"
        label="是否对外开放">
        <template slot-scope="scope">
            <el-tag v-if="scope.row.open === false" size="small" type="danger">否</el-tag>
            <el-tag v-else size="small">是</el-tag>
        </template>
     </el-table-column>
         <el-table-column
        prop="token"
        header-align="center"
        align="center"
        label="是否需要认证">
        <template slot-scope="scope">
            <el-tag v-if="scope.row.token === false" size="small" type="danger">否</el-tag>
            <el-tag v-else size="small">是</el-tag>
        </template>
     </el-table-column>
         <el-table-column
        prop="enable"
        header-align="center"
        align="center"
        label="是否启用">
        <template slot-scope="scope">
            <el-tag v-if="scope.row.enable === false" size="small" type="danger">否</el-tag>
            <el-tag v-else size="small">是</el-tag>
        </template>
     </el-table-column>
<!--         <el-table-column-->
<!--        prop="requestExample"-->
<!--        header-align="center"-->
<!--        align="center"-->
<!--        label="请求示例">-->
<!--      </el-table-column>-->
<!--         <el-table-column-->
<!--        prop="responseExample"-->
<!--        header-align="center"-->
<!--        align="center"-->
<!--        label="返回示例">-->
<!--      </el-table-column>-->
        <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
  import AddOrUpdate from './manage-add-or-update'
  export default {
    data () {
      return {
        dataForm: {
          name: ''
        },
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: [],
        addOrUpdateVisible: false
      }
    },
    components: {
      AddOrUpdate
    },
    activated () {
      this.queryPage()
    },
    methods: {
      queryPage () {
        this.pageIndex = 1
        this.getDataList()
      },
      // 获取数据列表
      getDataList () {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/admin/api/manage/pages'),
          method: 'post',
          data: this.$http.adornData({
            'currentPage': this.pageIndex,
            'pageSize': this.pageSize,
            'name': this.dataForm.name
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataList = data.obj.records
            this.totalPage = data.obj.total
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 多选
      selectionChangeHandle (val) {
        this.dataListSelections = val
      },
      // 新增 / 修改
      addOrUpdateHandle (id) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(id)
        })
      },
      // 删除
      deleteHandle (id) {
        var ids = id ? [id] : this.dataListSelections.map(item => {
          return item.id
        })
        this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/admin/api/manage/removeByIds'),
            method: 'get',
            params: this.$http.adornParams({
              'ids': ids.join(',')
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        })
      }
    }
  }
</script>
