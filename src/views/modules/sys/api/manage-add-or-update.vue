<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
      <el-row :gutter="20">
        <el-col :span="12">
           <el-form-item label="接口名称" prop="name" :error="nameError" label-width="110px">
                <el-input v-model="dataForm.name" placeholder="接口名称"></el-input>
            </el-form-item>
        </el-col>
            <el-col :span="12">
           <el-form-item label="接口请求方式" prop="requestType" :error="requestTypeError" label-width="110px">
             <el-select v-model="dataForm.requestType" placeholder="接口请求方式">
               <el-option
                 v-for="item in options"
                 :key="item.value"
                 :label="item.label"
                 :value="item.value">
               </el-option>
             </el-select>
            </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
           <el-form-item label="接口路径" prop="apiUrl" :error="apiUrlError" label-width="110px">
                <el-input v-model="dataForm.apiUrl" placeholder="接口路径"></el-input>
            </el-form-item>
        </el-col>
            <el-col :span="12">
           <el-form-item label="响应类型" prop="contentType" :error="contentTypeError" label-width="110px">
                <el-input v-model="dataForm.contentType" placeholder="响应类型"></el-input>
            </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
           <el-form-item label="接口描述" label-width="110px">
                <el-input type="textarea" v-model="dataForm.apiDesc" placeholder="接口描述"></el-input>
            </el-form-item>
        </el-col>
            <el-col :span="12">
            <el-form-item label="是否对外开放" prop="open" :error="openError" label-width="110px">
               <el-radio-group v-model="dataForm.open">
                   <el-radio :label="true">启用</el-radio>
                   <el-radio :label="false">禁用</el-radio>
               </el-radio-group>
            </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
            <el-form-item label="是否需要认证" prop="token" :error="tokenError" label-width="110px">
               <el-radio-group v-model="dataForm.token">
                   <el-radio :label="true">启用</el-radio>
                   <el-radio :label="false">禁用</el-radio>
               </el-radio-group>
            </el-form-item>
        </el-col>
            <el-col :span="12">
            <el-form-item label="是否启用" prop="enable" :error="enableError" label-width="110px">
               <el-radio-group v-model="dataForm.enable">
                   <el-radio :label="true">启用</el-radio>
                   <el-radio :label="false">禁用</el-radio>
               </el-radio-group>
            </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
           <el-form-item label="请求示例" prop="requestExample" label-width="110px">
                <el-input type="textarea" v-model="dataForm.requestExample" placeholder="请求示例"></el-input>
            </el-form-item>
        </el-col>
            <el-col :span="12">
           <el-form-item label="返回示例" prop="responseExample" label-width="110px">
                <el-input type="textarea" v-model="dataForm.responseExample" placeholder="返回示例"></el-input>
            </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: null,
          version: null,
          name: '',
          requestType: '',
          apiUrl: '',
          contentType: '',
          apiDesc: '',
          open: false,
          token: false,
          enable: false,
          requestExample: '',
          responseExample: ''
        },
        options: [{
          label: 'POST',
          value: 'POST'
        }, {
          label: 'GET',
          value: 'GET'
        }, {
          label: 'PUT',
          value: 'PUT'
        }, {
          label: 'DELETE',
          value: 'DELETE'
        }],
        dataRule: {
          name: [
            { required: true, message: '接口名称不能为空', trigger: 'blur' }
          ],
          requestType: [
            { required: true, message: '接口请求方式不能为空', trigger: 'blur' }
          ],
          apiUrl: [
            { required: true, message: '接口路径不能为空', trigger: 'blur' }
          ],
          contentType: [
            { required: true, message: '响应类型不能为空', trigger: 'blur' }
          ],
          open: [
            { required: true, message: '是否对外开放不能为空', trigger: 'blur' }
          ],
          token: [
            { required: true, message: '是否需要认证不能为空', trigger: 'blur' }
          ],
          enable: [
            { required: true, message: '是否启用不能为空', trigger: 'blur' }
          ]
        },
        nameError: null,
        requestTypeError: null,
        apiUrlError: null,
        contentTypeError: null,
        openError: null,
        tokenError: null,
        enableError: null
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || null
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/admin/api/manage`),
              method: 'get',
              params: this.$http.adornParams({ id: this.dataForm.id })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm = data.obj
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/admin/api/manage/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData(this.dataForm)
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else if (data && data.code === 303) {
                for (let it of data.obj) {
                  this[`${it.field}Error`] = it.message
                }
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
