<template>
  <el-dialog
    v-loading="isLoading"
    title="用户授权"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="60%"
  >
    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
      <el-tab-pane name="1" label="已授权用户">
        <grant-user-choose ref="alreadyGrantUser"></grant-user-choose>
      </el-tab-pane>
      <el-tab-pane name="2" label="未授权用户">
        <grant-user-choose ref="grantUser"></grant-user-choose>
      </el-tab-pane>
    </el-tabs>
    <!-- 查询条件 -->
  </el-dialog>
</template>

<script>
import grantUserChoose from './grant-user-choose.vue'
export default {
  components: { grantUserChoose },
  data () {
    return {
      visible: false,
      isLoading: false,
      dataListLoading: false,
      dataForm: {
        roleId: null,
        username: null,
        name: null,
        phone: null,
        currentPage: 1,
        pageSize: 10
      },
      userList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListSelections: [],
      activeName: '1'
    }
  },
  methods: {
    init (id) {
      this.visible = true
      this.dataForm.username = null
      this.dataForm.name = null
      this.dataForm.phone = null
      this.dataForm.currentPage = 1
      this.dataForm.pageSize = 10
      this.dataListSelections = ''
      this.dataForm.roleId = id
      this.activeName = '1'
      this.initData(id, this.activeName)
    },
    initData (id, type) {
      this.$nextTick(() => {
        this.$refs.alreadyGrantUser.init(id, type)
      })
    },
    initData2 (id, type) {
      this.$nextTick(() => {
        this.$refs.grantUser.init(id, type)
      })
    },
    handleClick (tab, event) {
      if (tab.name + '' === '1') {
        this.initData(this.dataForm.roleId, tab.name)
      } else if (tab.name + '' === '2') {
        this.initData2(this.dataForm.roleId, tab.name)
      }
    }

  }

}
</script>

<style>
</style>
