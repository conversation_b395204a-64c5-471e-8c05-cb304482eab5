<template>
  <div>
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <el-form-item prop="username">
        <el-input
          v-model="dataForm.username"
          placeholder="用户名"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item prop="name">
        <el-input
          v-model="dataForm.name"
          placeholder="姓名"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item prop="phone">
        <el-input
          v-model="dataForm.phone"
          placeholder="手机号"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button
          type="danger"
          v-if="type + '' === '1'"
          :disabled="dataListSelections.length === 0"
          @click="cancelGrantHandle()"
          >批量取消</el-button
        >
        <el-button
          type="primary"
          v-if="type + '' === '2'"
          :disabled="dataListSelections.length === 0"
          @click="cancelGrantHandle()"
          >授权</el-button
        >
      </el-form-item>
    </el-form>
    <!-- 数据展示 -->
    <el-table
      :data="userList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%"
    >
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50"
      >
      </el-table-column>
      <el-table-column prop="username" align="center" label="用户名">
      </el-table-column>
      <el-table-column prop="name" align="center" label="姓名">
      </el-table-column>
      <el-table-column prop="mobile" align="center" label="手机号">
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            v-if="type + '' === '1'"
            @click="cancelGrantHandle(scope.row.id, scope.row.name)"
            >取消授权</el-button
          >
          <el-button
            type="text"
            size="small"
            v-if="type + '' === '2'"
            @click="cancelGrantHandle(scope.row.id, scope.row.name)"
            >授权</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="dataForm.currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="dataForm.pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination>
  </div>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      isLoading: false,
      dataListLoading: false,
      dataForm: {
        roleId: null,
        username: null,
        name: null,
        phone: null,
        currentPage: 1,
        pageSize: 10
      },
      userList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListSelections: [],
      type: ''
    }
  },
  methods: {
    init (id, type) {
      this.type = type
      this.dataForm.username = null
      this.dataForm.name = null
      this.dataForm.phone = null
      this.dataForm.currentPage = 1
      this.dataForm.pageSize = 10
      this.dataListSelections = ''
      this.dataForm.roleId = id
      this.visible = true
      this.getDataList()
    },
    // 获取数据
    getDataList () {
      this.$http({
        url: this.$http.adornUrl(this.type === '1' ? `/admin/user/getUserByRole` : `/admin/user/getUserNotRole`),
        method: 'get',
        params: this.dataForm
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.userList = resp.data.obj.records
          this.totalPage = resp.data.obj.total
        } else {
          this.userList = []
        }
        this.dataListLoading = false
      })
    },
    // 取消授权
    cancelGrantHandle (id, name) {
      console.log(this.dataForm.roleId)
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      var names = name ? [name] : this.dataListSelections.map(item => {
        return item.name
      })
      this.$confirm(`确定要进行` + (this.type + '' === '1' ? '取消授权' : '授权') + `操作吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl(this.type === '1' ? '/admin/role/cancelGrant' : '/admin/role/grant'),
          method: 'post',
          data: this.$http.adornParams({
            'userIds': ids,
            'roleId': this.dataForm.roleId
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      }).catch(() => { })
    },
    // 多选
    selectionChangeHandle (val) {
      this.dataListSelections = val
    },
    // 每页数
    sizeChangeHandle (val) {
      this.dataForm.pageSize = val
      this.dataForm.currentPage = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.dataForm.currentPage = val
      this.getDataList()
    }
  }

}
</script>

<style>
</style>