<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
    >
      <el-form-item label="角色编码" prop="code">
        <el-input v-model="dataForm.code" placeholder="角色编码"></el-input>
      </el-form-item>
      <el-form-item label="角色名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="角色名称"></el-input>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="dataForm.status">
          <el-radio :label="false">禁用</el-radio>
          <el-radio :label="true">正常</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="数据权限" prop="dataAuth">
        <el-radio-group v-model="dataForm.dataAuth">
          <el-radio :label="0">全部</el-radio>
          <el-radio :label="1">部门</el-radio>
          <el-radio :label="2">用户</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="排序" prop="sequence">
        <el-input v-model="dataForm.sequence" placeholder="排序"></el-input>
      </el-form-item>
      <el-form-item size="mini" label="后台授权">
        <tree-table
          ref="tree"
          style="max-height: 240px; overflow: auto"
          expand-key="name"
          :expand-type="false"
          :is-fold="false"
          :tree-type="true"
          :selectable="true"
          :columns="columns"
          :data="menuList"
        >
          <template slot="operation" slot-scope="scope">
            <div>
              <div class="selectBtnGroup" v-if="scope.row.actionList">
                <el-button
                  type="text"
                  @click="selectAllAction(scope.row.actionList)"
                  >全选</el-button
                >
                <el-button
                  type="text"
                  @click="cancelSelectAllAction(scope.row.actionList)"
                  >取消全选</el-button
                >
              </div>
              <div style="margin: 2px 0"></div>
              <el-checkbox-group v-model="dataForm.actionIdList">
                <el-checkbox
                  v-for="item in scope.row.actionList"
                  :label="item.id"
                  :key="item.id"
                  >{{ item.actionName }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </template>
        </tree-table>
      </el-form-item>
      <el-form-item size="mini" label="APP授权">
        <tree-table
          ref="app-tree"
          style="max-height: 240px; overflow: auto"
          expand-key="name"
          :expand-type="false"
          :is-fold="false"
          :tree-type="true"
          :selectable="true"
          :columns="columns"
          :data="appMenuList"
        >
          <template slot="operation" slot-scope="scope">
            <div>
              <div class="selectBtnGroup" v-if="scope.row.actionList">
                <el-button
                  type="text"
                  @click="selectAllAction(scope.row.actionList)"
                  >全选</el-button
                >
                <el-button
                  type="text"
                  @click="cancelSelectAllAction(scope.row.actionList)"
                  >取消全选</el-button
                >
              </div>
              <div style="margin: 2px 0"></div>
              <el-checkbox-group v-model="dataForm.appActionIdList">
                <el-checkbox
                  v-for="item in scope.row.actionList"
                  :label="item.id"
                  :key="item.id"
                  >{{ item.actionName }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </template>
        </tree-table>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" v-loading="isLoading" @click="dataFormSubmit()"
        >确定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import _ from 'lodash'
import { treeDataTranslate } from '@/utils'

export default {
  data () {
    return {
      visible: false,
      isLoading: false,
      menuList: [],
      appMenuList: [],
      menuListTreeProps: {
        // label: 'nameRemark',
        label: 'name',
        children: 'children'
      },
      columns: [{
        title: '菜单',
        key: 'name',
        minWidth: '250px'
      }, {
        title: '操作',
        type: 'template',
        template: 'operation',
        minWidth: '200px'
      }],
      dataForm: {
        id: undefined,
        actionIdList: [],
        appActionIdList: [],
        code: '',
        name: '',
        status: true,
        dataAuth: 2,
        sequence: 99
      },
      menuId: [],
      menuName: [],
      dataRule: {
        code: [
          { required: true, message: '角色编码不能为空', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '角色名称不能为空', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '角色状态不能为空', trigger: 'blur' }
        ],
        dataAuth: [
          { required: true, message: '数据权限不能为空', trigger: 'blur' }
        ],
        sequence: [
          { required: true, message: '角色排序不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init (id) {
      let menus = []
      let appMenus = []
      this.dataForm.actionIdList = []
      this.dataForm.appActionIdList = []
      this.dataForm.id = id || undefined
      this.$http({
        url: this.$http.adornUrl('/admin/resource/menus'),
        method: 'get',
        params: this.$http.adornParams({ group: 0 })
      }).then(({ data }) => {
        menus = data.obj
        if (menus) {
          menus.forEach(data => {
            if (data.parentId + '' === '0') {
              data.parentId = null
            }
          })
        }
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/resource/menus'),
          method: 'get',
          params: this.$http.adornParams({ group: 1 })
        }).then(({ data }) => {
          appMenus = data.obj
          if (appMenus) {
            appMenus.forEach(data => {
              if (data.parentId + '' === '0') {
                data.parentId = null
              }
            })
          }
        }).then(() => {
          this.visible = true
          this.$nextTick(() => {
            this.$refs['dataForm'].resetFields()
          })
        }).then(() => {
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/admin/role/findOneById`),
              method: 'get',
              params: this.$http.adornParams({
                'id': this.dataForm.id
              })
            }).then(({ data }) => {
              if (data && data.code === 0) {
                this.dataForm.code = data.obj.code
                this.dataForm.name = data.obj.name
                this.dataForm.status = data.obj.status
                this.dataForm.sequence = data.obj.sequence
                this.dataForm.actionIdList = data.obj.actionIdList
                this.dataForm.appActionIdList = data.obj.appActionIdList
                this.dataForm.dataAuth = data.obj.dataAuth
                _.forEach(menus, (item) => {
                  item._isChecked = data.obj.menuIdList.includes(item.id)
                })
                this.menuList = treeDataTranslate(menus, 'id')
                _.forEach(appMenus, (item) => {
                  item._isChecked = data.obj.appMenuIdList.includes(item.id)
                })
                this.appMenuList = treeDataTranslate(appMenus, 'id')
              }
            })
          } else {
            _.forEach(menus, (item) => {
              item._isChecked = false
            })
            this.menuList = treeDataTranslate(menus, 'id')
            _.forEach(appMenus, (item) => {
              item._isChecked = false
            })
            this.appMenuList = treeDataTranslate(appMenus, 'id')
          }
        })
      })
    },
    // getMenuList () {
    //   this.$http({
    //     url: this.$http.adornUrl('/admin/resource/menus'),
    //     method: 'get',
    //     params: this.$http.adornParams({group: 0})
    //   }).then(({data}) => {
    //     this.menuList = data.obj
    //   })
    // },
    // getAppMenuList () {
    //   this.$http({
    //     url: this.$http.adornUrl('/admin/resource/menus'),
    //     method: 'get',
    //     params: this.$http.adornParams({group: 1})
    //   }).then(({data}) => {
    //     this.appMenuList = data.obj
    //   })
    // },
    // 处理按钮全选
    selectAllAction (menuActionList) {
      _.forEach(menuActionList, (item) => {
        if (!this.dataForm.actionIdList.includes(item.id)) {
          this.dataForm.actionIdList.push(item.id)
        }
      })
    },
    // 处理取消全选
    cancelSelectAllAction (menuActionList) {
      _.forEach(menuActionList, (item) => {
        let indexOfMenu = this.dataForm.actionIdList.indexOf(item.id)
        if (indexOfMenu >= 0) {
          this.dataForm.actionIdList.splice(indexOfMenu, 1)
        }
      })
    },
    getCheckedId () {
      this.menuId = this.$refs['tree'].getCheckedProp('id')
      const menuId = ',' + this.menuId.join(',') + ','
      this.menuName = this.$refs['tree'].getCheckedProp('name')
      console.log(this.menuId, this.menuName)
      if (this.$refs['tree'].bodyData) {
        // 添加缺少的数据
        this.$refs['tree'].bodyData.forEach(data => {
          if (data._isChecked) {
            // 处理父节点
            if (data.parentId) {
              this.handleParentNode(data)
            }
          }
        })
        // 删除数据
        this.$refs['tree'].bodyData.forEach(data => {
          if (data._isChecked) {
            // 处理子节点
            if (data.children) {
              this.handleChildrenNode(data)
            }
          }
        })
      }
    },
    // 处理父节点
    handleParentNode (data) {
      let parentIds = this.getParentIds(data)
      if (parentIds) {
        let menuId = ',' + this.menuId.join(',') + ','
        parentIds.forEach(parentId => {
          if (menuId.indexOf(parentId) <= 0) {
            this.menuId.push(parentId)
          }
        })
      }
    },
    // 获取该节点所对应的父节点
    getParentIds (data) {
      let parentIds = []
      if (data.parentId) {
        parentIds.push(data.parentId)
        this.$refs['tree'].bodyData.forEach(menu => {
          if (menu.id + '' === data.parentId + '') {
            if (menu.parentId) {
              let parentIds2 = this.getParentIds(menu)
              if (parentIds2) {
                parentIds2.forEach(parentId => {
                  parentIds.push(parentId)
                })
              }
            }
          }
        })
      }
      return parentIds
    },
    // 处理子节点
    handleChildrenNode (data) {
      let ids = this.getChildrenId(data)
      if (ids) {
        let menuId = ',' + this.menuId.join(',') + ','
        let childrenId = null
        ids.forEach(id => {
          if (menuId.indexOf(',' + id + ',') > 0) {
            childrenId = id
          }
        })
        if (!childrenId) {
          this.menuId.splice(this.menuId.indexOf(data.id), 1)
          // 并判断父节点情况
          if (data.parentId) {
            this.$refs['tree'].bodyData.forEach(menu => {
              if (menu.id + '' === data.parentId) {
                this.handleChildrenNode(menu)
              }
            })
          }
        }
      } else {
        if (data.children) {
          this.menuId.splice(this.menuId.indexOf(data.id), 1)
        }
      }
    },
    // 获取自节点的所有id组合
    getChildrenId (data) {
      let ids = []
      if (data.children) {
        data.children.forEach(menu => {
          if (menu.children) {
            let ids2 = this.getChildrenId(menu)
            if (ids2) {
              ids2.forEach(id2 => {
                ids.push(id2)
              })
            }
          } else {
            ids.push(menu.id)
          }
        })
      }
      return ids
    },
    // 表单提交
    dataFormSubmit () {
      this.getCheckedId()
      let name = []
      this.$refs['tree'].bodyData.forEach(data => {
        if (this.menuId) {
          this.menuId.forEach(id => {
            if (id + '' === data.id + '') {
              name.push(data.name)
            }
          })
        }
      })
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.isLoading = true
          this.$http({
            url: this.$http.adornUrl(`/admin/role/saveRole`),
            method: 'post',
            data: this.$http.adornData({
              'id': this.dataForm.id || undefined,
              'code': this.dataForm.code,
              'name': this.dataForm.name,
              'status': this.dataForm.status,
              'sequence': this.dataForm.sequence,
              dataAuth: this.dataForm.dataAuth,
              'menuIdList': this.menuId,
              'actionIdList': this.dataForm.actionIdList,
              appMenuIdList: this.$refs['app-tree'].getCheckedProp('id'),
              appActionIdList: this.dataForm.actionIdList
            })
          }).then(({ data }) => {
            this.isLoading = false
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>
