<template>
  <div>
    <member-list v-if="userListVisible" ref="userList"></member-list>
  </div>
</template>

<script>
import MemberList from './member-list.vue'
export default {
  data () {
    return {
      userListVisible: false,
      treeUrl: '/admin/org/tree'
    }
  },
  components: {
    MemberList
  },
  activated () {
    this.init()
  },
  methods: {
    init () {
      this.userListVisible = true
      this.$nextTick(() => {
        this.$refs.userList.treeUrl = this.treeUrl
        this.$refs.userList.getDataList()
      })
    }
  }

}
</script>


List<style>
</style>