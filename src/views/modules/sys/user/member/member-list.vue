<template>
  <div class="mod-config">
    <el-row>
      <el-col :span="5">
        <el-card shadow="hover" class="box-card">
          <el-input placeholder="输入关键字进行过滤" v-model="filterText">
          </el-input>
          <div style="overflow: hidden; width: 100%">
            <div
              style="
                overflow-y: auto;
                max-height: 800px;
                min-width: 110%;
                max-width: 700px;
              "
            >
              <el-tree
                class="filter-tree"
                :data="treeData"
                :props="props"
                :highlight-current="true"
                :expand-on-click-node="false"
                :filter-node-method="filterNode"
                :default-expand-all="true"
                @current-change="currentChange"
                ref="tree"
              >
              </el-tree>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="19">
        <el-card shadow="hover" style="margin-left: 20px">
          <!-- 条件 -->
          <div>
            <el-form
              :model="queryParams"
              ref="queryForm"
              label-width="80px"
              v-show="showSearch"
              :inline="true"
            >
              <el-form-item label="用户名" prop="username">
                <el-input
                  v-model="queryParams.username"
                  placeholder="请输入用户名称"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>

              <el-form-item label="用户姓名" prop="name">
                <el-input
                  v-model="queryParams.name"
                  placeholder="请输入用户名"
                  clearable
                  size="small"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>

              <el-form-item label="手机号" prop="phone">
                <el-input
                  v-model="queryParams.phone"
                  placeholder="请输入手机号"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>

              <el-form-item label="状态" prop="status">
                <el-select
                  @change="handleQuery"
                  v-model="queryParams.status"
                  placeholder="请选择测评类型"
                  clearable
                >
                  <el-option label="停用" value="false" />
                  <el-option label="正常" value="true" />
                </el-select>
              </el-form-item>

              <el-form-item label="创建时间" prop="code">
                <el-date-picker
                  v-model="dateRange"
                  style="width: 240px"
                  value-format="yyyy-MM-dd"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                ></el-date-picker>
              </el-form-item>

              <el-button icon="el-icon-search" @click="handleQuery">查询</el-button>
              <el-button icon="el-icon-refresh" type="warning" @click="resetForm('queryForm')"
                >重置</el-button
              >
            </el-form>

            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  icon="el-icon-plus"
                  @click="addOrUpdateHandle()"
                  >新增</el-button
                >
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="success"
                  icon="el-icon-edit"
                  :disabled="dataListSelections.length !== 1"
                  @click="addOrUpdateHandle(dataListSelections[0].id)"
                  >修改</el-button
                >
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="danger"
                  icon="el-icon-delete"
                  :disabled="dataListSelections.length <= 0"
                  @click="deleteHandle()"
                  >删除</el-button
                >
              </el-col>
              <el-col :span="1.5">
                <el-button
                  v-if="false"
                  type="info"
                  icon="el-icon-upload2"
                  @click="handleImport"
                  >导入</el-button
                >
              </el-col>
              <el-col :span="1.5">
                <el-button
                  v-if="false"
                  type="warning"
                  icon="el-icon-download"
                  @click="handleExport"
                  >导出</el-button
                >
              </el-col>
              <div style="float: right">
                <el-button
                  @click="hideInfo"
                  icon="el-icon-search"
                  circle
                ></el-button>
                <el-button
                  @click="resetForm('queryForm')"
                  icon="el-icon-refresh"
                  circle
                ></el-button>
              </div>
            </el-row>
            <br />
            <el-table
              v-loading="loading"
              :data="userList"
              border
              @selection-change="selectionChangeHandle"
            >
              <el-table-column type="selection" width="50" align="center" />
              <el-table-column
                label="用户姓名"
                align="center"
                prop="name"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                label="用户名"
                align="center"
                prop="username"
                :show-overflow-tooltip="true"
              />

              <el-table-column
                label="部门"
                align="center"
                prop="orgName"
                :show-overflow-tooltip="true"
              />
              <el-table-column label="手机号码" align="center" prop="mobile" />
              <el-table-column label="状态" align="center">
                <template slot-scope="scope">
                  <el-switch
                    v-model="scope.row.status"
                    :active-value="true"
                    :inactive-value="false"
                    @change="handleStatusChange(scope.row)"
                  ></el-switch>
                </template>
              </el-table-column>
              <el-table-column
                label="真实姓名"
                align="center"
                prop="authName"
              />
              <el-table-column
                label="身份证号"
                align="center"
                prop="idNum"
              >
                <template slot-scope="scope">
                  <span>
                    {{ scope.row.idNum ? scope.row.idNum.substring(0,4) + '***' + scope.row.idNum.substring(scope.row.idNum.length - 4) : '' }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column
                label="创建时间"
                align="center"
                prop="createTime"
                width="160"
              >
                <template slot-scope="scope">
                  <span>{{ scope.row.createDate }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="操作"
                align="center"
                width="200"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    @click="addOrUpdateHandle(scope.row.id)"
                    >修改</el-button
                  >
                  <el-button
                    v-if="scope.row.userId !== 1"
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click="deleteHandle(scope.row.id)"
                    >删除</el-button
                  >
                  <el-button
                    v-if="isAuth('sys:user:update')"
                    type="text"
                    size="small"
                    @click="resetPassword(scope.row)"
                    >修改密码</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="block">
            <el-pagination
              @size-change="sizeChangeHandle"
              @current-change="currentChangeHandle"
              :current-page="queryParams.currentPage"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="queryParams.pageSize"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
            >
            </el-pagination>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-dialog
      title="修改密码"
      :visible.sync="dialogVisible"
      width="30%"
      :close-on-click-modal="false"
    >
      <el-form
        :model="upateInfo"
        :rules="upateRule"
        ref="dataForm"
        @keyup.enter.native="dataFormSubmit()"
        label-width="80px"
      >
        <el-form-item label="姓名" prop="password">
          <span>{{ upateInfo.name }}</span>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="upateInfo.password"
            type="password"
            placeholder="密码"
          ></el-input>
        </el-form-item>

        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="upateInfo.confirmPassword"
            type="password"
            placeholder="确认密码"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="resetPass">确 定</el-button>
      </span>
    </el-dialog>

    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="handleQuery"
    ></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from '../user-add-or-update'
export default {
  components: {
    AddOrUpdate
  },
  watch: {
    filterText (val) {
      this.$refs.tree.filter(val)
    }
  },
  data () {
    let validatePassword = (rule, value, callback) => {
      if (!this.upateInfo.id && !/\S/.test(value)) {
        callback(new Error('密码不能为空'))
      } else {
        callback()
      }
    }
    let validateConfirmPassword = (rule, value, callback) => {
      if (this.upateInfo.id && !/\S/.test(value)) {
        callback(new Error('确认密码不能为空'))
      } else if (this.upateInfo.password !== value) {
        callback(new Error('确认密码与密码输入不一致'))
      } else {
        callback()
      }
    }

    return {
      filterText: '',
      treeData: [],
      props: {
        key: 'id',
        label: 'name',
        children: 'children'
      },
      queryParams: {
        username: '',
        name: '',
        phone: '',
        status: '',
        startTime: '',
        endTime: '',
        currentPage: 1,
        pageSize: 10,
        code: '',
        orgId: ''
      },
      // 日期范围
      dateRange: [],
      showSearch: true,
      userList: [],
      loading: false,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      total: 0,
      addOrUpdateVisible: false,
      ids: [],
      dataListSelections: [],
      dialogVisible: false,
      upateInfo: {
        id: '',
        name: '',
        password: '',
        confirmPassword: ''
      },
      upateRule: {
        password: [
          { validator: validatePassword, trigger: 'blur' }
        ],
        confirmPassword: [
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      },
      treeUrl: ''
    }
  },

  // activated () {
  //   this.getDataList()
  //   this.handleQuery()
  // },
  methods: {
    handleQuery () {
      if (this.dateRange) {
        this.queryParams.startTime = this.dateRange[0]
        this.queryParams.endTime = this.dateRange[1]
      } else {
        this.queryParams.startTime = null
        this.queryParams.endTime = null
      }
      this.loading = true
      this.$http({
        url: this.$http.adornUrl(`/admin/user/memberList`),
        method: 'get',
        params: this.queryParams
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.userList = resp.data.obj.records
          this.total = resp.data.obj.total
        } else {
          this.userList = []
        }
        this.loading = false
      })
    },
    hideInfo () {
      if (this.showSearch) {
        this.showSearch = false
      } else {
        this.showSearch = true
      }
    },
    filterNode (value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    // 获取数据列表
    async getDataList () {
      await this.$http({
        url: this.$http.adornUrl(this.treeUrl),
        method: 'get',
        params: this.$http.adornParams({ level: 1 })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.treeData = data.obj
        } else {
          this.treeData = []
        }
        this.data = {}
      })
      if (this.treeUrl === '/admin/org/tree/user') {
        if (this.treeData) {
          this.queryParams.code = this.treeData[0].code
        }
      }
      this.handleQuery()
    },
    handleClose (done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done()
        })
        .catch(_ => { })
    },
    resetPassword (row) {
      this.dialogVisible = true
      this.upateInfo = []
      this.upateInfo = row
      this.upateInfo.password = ''
    },
    resetPass () {
      this.$http({
        url: this.$http.adornUrl('/admin/user/resetPassWord'),
        method: 'get',
        params: {
          id: this.upateInfo.id,
          password: this.upateInfo.password,
          confirmPassword: this.upateInfo.confirmPassword
        }
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 500,
            onClose: () => {
              this.handleQuery()
            }
          })
          this.dialogVisible = false
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    currentChange (data) {
      if (data) {
        this.queryParams.code = data.code
        this.queryParams.orgId = data.id
        this.handleQuery()
      }
    },
    // 多选框选中数据
    selectionChangeHandle (val) {
      this.dataListSelections = val
    },
    handleStatusChange (row) {
      const user = {
        id: row.id,
        status: row.status
      }
      this.$http({
        url: this.$http.adornUrl('/admin/user/updateStatus'),
        method: 'get',
        params: user
      }).then(resp => {
        if (resp.data && resp.data.code === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 500,
            onClose: () => {
              this.handleQuery()
            }
          })
        } else {
          this.$message.error(resp.data.msg)
        }
      })
    },
    resetForm (formName) {
      this.$refs[formName].resetFields()
      this.queryParams = {}
      this.queryParams.pageSize = 10
      this.queryParams.currentPage = 1
      this.dateRange = []
      if (this.treeUrl === '/admin/org/tree/user') {
        if (this.treeData) {
          this.queryParams.code = this.treeData[0].code
        }
      }
      this.handleQuery()
    },
    // 每页数
    sizeChangeHandle (val) {
      this.queryParams.pageSize = val
      this.queryParams.currentPage = 1
      this.handleQuery()
    },
    // 当前页
    currentChangeHandle (val) {
      this.queryParams.currentPage = val
      this.handleQuery()
    },
    // 删除
    deleteHandle (id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定进行${id ? '删除' : '批量删除'}操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/user/removeByIds'),
          method: 'get',
          params: this.$http.adornParams({ 'ids': ids.join(',') }, false)
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.handleQuery()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      }).catch(() => { })
    },
    addOrUpdateHandle (id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        if (id && !this.queryParams.orgId && this.treeUrl === '/admin/org/tree/user' && this.treeData) {
          this.queryParams.orgId = this.treeData[0].id
        }
        this.$refs.addOrUpdate.treeUrl = this.treeUrl
        this.$refs.addOrUpdate.init(id, this.queryParams.orgId)
      })
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['queryForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/admin/user/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>
