<template>
  <div class="mod-user">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <el-form-item>
        <el-input
          v-model="dataForm.name"
          placeholder="用户姓名"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="dataForm.username"
          placeholder="用户名"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="dataForm.mobile"
          placeholder="手机号"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button
          v-if="isAuth('sys:user:list')"
          icon="el-icon-search"
          @click="currentChangeHandle(1)"
          >查询</el-button
        >
        <el-button
          icon="el-icon-plus"
          v-if="isAuth('sys:user:save')"
          type="primary"
          @click="addOrUpdateHandle()"
          >新增</el-button
        >
        <el-button
          icon="el-icon-delete"
          v-if="isAuth('sys:user:delete')"
          type="danger"
          @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0"
          >批量删除</el-button
        >
        <el-button
          icon="el-icon-upload2"
          type="success"
          @click="importUsers()"
        >导入</el-button
        >
        <el-button
          icon="el-icon-download"
          type="success"
          @click="exportUsers()"
          :disabled="dataList.length <= 0"
        >导出</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%"
    >
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50"
      >
      </el-table-column>
      <el-table-column
        prop="name"
        header-align="center"
        align="center"
        label="用户姓名"
      >
      </el-table-column>
      <el-table-column
        prop="username"
        header-align="center"
        align="center"
        label="用户名"
      >
      </el-table-column>
      <el-table-column
        prop="mobile"
        header-align="center"
        align="center"
        label="手机"
      >
      </el-table-column>
      <el-table-column
        prop="email"
        header-align="center"
        align="center"
        label="邮箱"
      >
      </el-table-column>
      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        label="状态"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === false" size="small" type="danger"
            >禁用</el-tag
          >
          <el-tag v-else size="small">正常</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="createDate"
        header-align="center"
        align="center"
        width="180"
        label="创建时间"
      >
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="200"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            v-if="isAuth('sys:user:update')"
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row.id)"
            >修改</el-button
          >
          <el-button
            v-if="isAuth('sys:user:delete')"
            type="text"
            size="small"
            @click="deleteHandle(scope.row.id)"
            >删除</el-button
          >
          <el-button
            v-if="isAuth('sys:user:update')"
            type="text"
            size="small"
            @click="resetPassword(scope.row)"
            >修改密码</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="dataForm.currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="dataForm.pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination>

    <el-dialog
      title="修改密码"
      :visible.sync="dialogVisible"
      width="40%"
      :close-on-click-modal="false"
    >
      <el-form
        :model="resetInfo"
        :rules="resetRule"
        ref="resetForm"
        @keyup.enter.native="resetPass()"
        label-width="80px"
      >
        <el-form-item label="姓名">
          <span>{{ resetInfo.name }}</span>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="resetInfo.password"
            type="password"
            placeholder="密码"
          ></el-input>
        </el-form-item>

        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="resetInfo.confirmPassword"
            type="password"
            placeholder="确认密码"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="resetPass">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './user-add-or-update'
export default {
  data () {
    let validatePassword = async (rule, value, callback) => {
      await this.$http({
        url: this.$http.adornUrl('/admin/user/passWord/check'),
        method: 'get',
        params: {
          password: value
        }
      }).then(({ data }) => {
        this.checkPasswordCode = data.code
      })
      if (this.checkPasswordCode !== 0) {
        callback(new Error('密码需满足大小写、数字、特殊字符、长度在8-20位要求'))
      } else {
        callback()
      }
    }
    let validateConfirmPassword = (rule, value, callback) => {
      if (!/\S/.test(value)) {
        callback(new Error('确认密码不能为空'))
      } else if (this.resetInfo.password !== value) {
        callback(new Error('确认密码与密码输入不一致'))
      } else {
        callback()
      }
    }
    return {
      checkPasswordCode: '',
      dataForm: {
        keyword: '',
        userName: null,
        name: null,
        mobile: null,
        currentPage: 1,
        pageSize: 10
      },
      dataList: [],
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      dialogVisible: false,
      resetInfo: {
        id: '',
        name: '',
        password: '',
        confirmPassword: ''
      },
      resetRule: {
        password: [
          { validator: validatePassword, trigger: 'blur' }
        ],
        confirmPassword: [
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      }
    }
  },
  components: {
    AddOrUpdate
  },
  activated () {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/user/pages'),
        method: 'post',
        data: this.$http.adornData(this.dataForm)
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.dataForm.pageSize = val
      this.dataForm.currentPage = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.dataForm.currentPage = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle (val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle (id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id, '')
      })
    },
    resetPassword (row) {
      this.dialogVisible = true
      this.resetInfo.name = row.name
      this.resetInfo.id = row.id
      this.resetInfo.password = ''
      this.resetInfo.confirmPassword = ''
    },
    resetPass () {
      this.$refs['resetForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl('/admin/user/resetPassWord'),
            method: 'get',
            params: {
              id: this.resetInfo.id,
              password: this.resetInfo.password,
              confirmPassword: this.resetInfo.confirmPassword
            }
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.getDataList()
                }
              })
              this.dialogVisible = false
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    handleClose (done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done()
        })
        .catch(_ => { })
    },
    // 删除
    deleteHandle (id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定进行${id ? '删除' : '批量删除'}操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/user/removeByIds'),
          method: 'get',
          params: this.$http.adornParams({ 'ids': ids.join(',') }, false)
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      }).catch(() => { })
    },
    // 导入
    importUsers () {
      this.$router.push({
        path: '/evaluateImport',
        query: {
          parentId: this.$route.query.parentId,
          type: 4
        }
      })
    },
    // 导出
    exportUsers () {
      this.$http({
        url: this.$http.adornUrl('/admin/user/excel/export'),
        method: 'post',
        responseType: 'arraybuffer',
        data: this.$http.adornData(this.dataForm)
      }).then(({data}) => {
        if (data.code && data.code !== 0) {
          this.$message.error('导出失败')
        } else {
          let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
          window.location.href = URL.createObjectURL(blob)
          this.$message({
            type: 'success',
            message: '导出数据成功'
          })
        }
      })
    }
  }
}
</script>
