<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户名" prop="username">
            <el-input
              v-model="dataForm.username"
              placeholder="登录帐号"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input auto-complete="new-password" v-model="dataForm.name" placeholder="姓名"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item
            label="密码"
            v-if="dataForm.id === null"
            prop="password"
            :class="{ 'is-required': !dataForm.id }"
          >
            <el-input
              auto-complete="new-password"
              v-model="dataForm.password"
              type="password"
              placeholder="密码"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="确认密码"
            v-if="dataForm.id === null"
            prop="confirmPassword"
            :class="{ 'is-required': !dataForm.id }"
          >
            <el-input
            auto-complete="new-password"
              v-model="dataForm.confirmPassword"
              type="password"
              placeholder="确认密码"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="手机号" prop="mobile">
            <el-input
            style="display:none"
            auto-complete="new-password"
              v-model="dataForm.confirmPassword"
              type="password"
              placeholder="确认密码"
            ></el-input>
            <el-input v-model="dataForm.mobile" placeholder="手机号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="dataForm.email" placeholder="邮箱"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="false">禁用</el-radio>
              <el-radio :label="true">启用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="roleHide">
          <el-form-item label="角色" prop="roleIds" placeholder="请选择">
            <!-- <el-checkbox-group v-model="dataForm.roleIds">
              <el-checkbox
                v-for="item in roleList"
                :key="item.id"
                :label="item.id"
                :value="item.id"
                >{{ item.name }}
              </el-checkbox>
            </el-checkbox-group> -->
            <el-select
              v-model="dataForm.roleIds"
              clearable
              multiple
              filterable
              style="width: 100%"
              placeholder="请选择角色"
            >
              <el-option
                v-for="item in roleList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="组织架构" prop="orgName">
            <el-popover
              ref="orgListPopover"
              placement="bottom-start"
              trigger="click"
            >
              <el-tree
                style="height: 375px; overflow: auto"
                :data="orgList"
                :props="orgProps"
                node-key="id"
                ref="orgTree"
                @current-change="orgTreeCurrentChangeHandle"
                :default-expand-all="true"
                :highlight-current="true"
                :expand-on-click-node="false"
              >
              </el-tree>
            </el-popover>
            <el-input
              v-model="dataForm.orgName"
              v-popover:orgListPopover
              :readonly="true"
              placeholder="点击选择组织架构"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否领导" prop="leader">
            <el-radio-group v-model="dataForm.leader">
              <el-radio :label="false">否</el-radio>
              <el-radio :label="true">是</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { isEmail, isMobile, isName } from '@/utils/validate'

export default {
  data () {
    let validateName = (rule, value, callback) => {
      if (!isName(value)) {
        callback(new Error('姓名长度不能超过10'))
      } else {
        callback()
      }
    }
    let validatePassword = async (rule, value, callback) => {
      await this.$http({
        url: this.$http.adornUrl('/admin/user/passWord/check'),
        method: 'get',
        params: {
          password: value
        }
      }).then(({ data }) => {
        this.checkPasswordCode = data.code
      })
      if (this.checkPasswordCode !== 0) {
        callback(new Error('密码需大小写、数字、特殊字符、8-20位'))
      } else {
        callback()
      }
    }
    let validateConfirmPassword = (rule, value, callback) => {
      if (!/\S/.test(value)) {
        callback(new Error('确认密码不能为空'))
      } else if (this.dataForm.password !== value) {
        callback(new Error('确认密码与密码输入不一致'))
      } else {
        callback()
      }
    }
    let validateEmail = (rule, value, callback) => {
      if (!value) {
        callback()
      }
      if (!isEmail(value)) {
        callback(new Error('邮箱格式错误'))
      } else {
        callback()
      }
    }
    let validateMobile = (rule, value, callback) => {
      if (!isMobile(value)) {
        callback(new Error('手机号格式错误'))
      } else {
        callback()
      }
    }

    let validateUsername = (rule, value, callback) => {
      if (!value) {
        callback(new Error('用户名不能为空'))
      } else if (value.length < 5 || value.length > 15) {
        callback(new Error('用户名应在5到15个字符'))
      } else if (!/^\w+$/.test(value)) {
        callback(new Error('用户名只能由字母和数字组成'))
      } else {
        callback()
      }
    }
    return {
      checkPasswordCode: '',
      visible: false,
      roleList: [],
      orgList: [],
      orgProps: {
        key: 'id',
        label: 'name',
        children: 'children'
      },
      dataForm: {
        id: null,
        username: null,
        password: null,
        confirmPassword: null,
        email: null,
        tel: null,
        mobile: null,
        roleIds: [],
        orgId: null,
        orgName: null,
        name: null,
        status: true,
        leader: false,
        version: null
      },
      dataRule: {
        username: [
          { validator: validateUsername, trigger: 'blur' },
          { required: true, message: '用户名不能为空', trigger: 'blur' }
        ],
        password: [
          { validator: validatePassword, trigger: 'blur' }
        ],
        confirmPassword: [
          { validator: validateConfirmPassword, trigger: 'blur' }
        ],
        email: [
          { validator: validateEmail, trigger: 'blur' }
        ],
        mobile: [
          { required: true, message: '手机号不能为空', trigger: 'blur' },
          { validator: validateMobile, trigger: 'blur' }
        ],
        // roleIds: [
        //   { required: true, message: '用户角色不能为空', trigger: 'blur' }
        // ],
        status: [
          { required: true, message: '状态不能为空', trigger: 'blur' }
        ],
        orgName: [
          { required: true, message: '组织机构不能为空', trigger: 'change' }
        ],
        leader: [
          { required: true, message: '是否领导不能为空', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '用户姓名不能为空', trigger: 'blur' },
          { validator: validateName, trigger: 'blur' }
        ]
      },
      treeUrl: '',
      roleHide: true
    }
  },
  methods: {
    init (id, orgId) {
      this.dataForm.id = id || null
      this.dataForm.password = null
      this.dataForm.confirmPassword = null
      this.dataForm.orgId = orgId
      this.dataForm.orgName = ''
      this.getRoleList()
      this.$http({
        url: this.$http.adornUrl('/admin/org/tree/user'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        this.orgList = data.obj || []
      }).then(() => {
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl('/admin/user'),
              method: 'get',
              params: this.$http.adornParams({ 'id': this.dataForm.id })
            }).then(({ data }) => {
              if (data && data.code === 0) {
                data.obj.password = null
                data.obj.confirmPassword = null
                data.obj.orgName = null
                data.obj.roleIds = data.obj.roleIdsStr ? data.obj.roleIdsStr.split(',') : []
                this.dataForm = data.obj
                this.orgListTreeSetCurrentNode()
              }
            })
          }
        })
        if (this.dataForm.orgId) {
          this.orgListTreeSetCurrentNode()
        }
      })
    },
    // 区域树选中
    orgTreeCurrentChangeHandle (data, node) {
      this.dataForm.orgId = data.id
      this.dataForm.orgName = data.name
      this.$refs[`orgListPopover`].doClose()
    },
    // 区域树设置当前选中节点
    orgListTreeSetCurrentNode () {
      let key = this.dataForm.orgId
      if (key) {
        this.$nextTick(() => {
          this.$refs.orgTree.setCurrentKey(key)
          this.dataForm.orgName = (this.$refs.orgTree.getCurrentNode() || {})['name']
        })
      } else {
        this.$refs.orgTree.setCurrentKey([])
        this.dataForm.orgName = ''
      }
    },
    getRoleList () {
      this.roleList = []
      if (this.treeUrl !== '/admin/org/tree/user') {
        this.$http({
          url: this.$http.adornUrl('/admin/role/all'),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({ data }) => {
          this.roleList = data || []
        })
      } else {
        this.roleHide = false
        this.$http({
          url: this.$http.adornUrl('/admin/role/findOneByCode'),
          method: 'get',
          params: {
            code: 'StreetChildRole'
          }
        }).then(({ data }) => {
          if (data.obj) {
            this.dataForm.roleIds.push(data.obj.id)
          }
        })
      }
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/admin/user/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>
