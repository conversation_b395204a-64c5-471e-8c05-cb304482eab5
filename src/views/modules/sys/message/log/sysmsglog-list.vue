<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item>
        <el-input
          v-model="dataForm.receiveUserName"
          placeholder="接收人"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="dataForm.targetType"
          placeholder="接收方式"
          clearable
        >
          <el-option
            v-for="item in targetList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" @click="queryPage()">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%"
    >
      <!--      <el-table-column-->
      <!--        type="selection"-->
      <!--        header-align="center"-->
      <!--        align="center"-->
      <!--        width="50">-->
      <!--      </el-table-column>-->
      <el-table-column
        prop="content"
        header-align="center"
        align="center"
        label="消息内容"
      >
      </el-table-column>
      <!--         <el-table-column-->
      <!--        prop="adminUrl"-->
      <!--        header-align="center"-->
      <!--        align="center"-->
      <!--        label="后台跳转路由地址">-->
      <!--      </el-table-column>-->
      <!--         <el-table-column-->
      <!--        prop="appUrl"-->
      <!--        header-align="center"-->
      <!--        align="center"-->
      <!--        label="小程序跳转路由地址">-->
      <!--      </el-table-column>-->
      <!--         <el-table-column-->
      <!--        prop="receiveUserId"-->
      <!--        header-align="center"-->
      <!--        align="center"-->
      <!--        label="接收人ID">-->
      <!--      </el-table-column>-->
      <el-table-column
        prop="receiveUserName"
        header-align="center"
        align="center"
        label="接收人"
      >
      </el-table-column>
      <el-table-column
        prop="targetType"
        header-align="center"
        align="center"
        label="接收方式"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.targetType === 1">后台接收</el-tag>
          <el-tag v-if="scope.row.targetType === 2">小程序接收</el-tag>
          <el-tag v-if="scope.row.targetType === 4">后台、小程序都接收</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="read"
        header-align="center"
        align="center"
        label="是否已读"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.readed === false" size="small" type="danger"
            >否</el-tag
          >
          <el-tag v-else size="small">是</el-tag>
        </template>
      </el-table-column>
      <!--        <el-table-column-->
      <!--        fixed="right"-->
      <!--        header-align="center"-->
      <!--        align="center"-->
      <!--        width="150"-->
      <!--        label="操作">-->
      <!--        <template slot-scope="scope">-->
      <!--          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>-->
      <!--          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 30, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination>
  </div>
</template>

<script>
export default {
  data () {
    return {
      targetList: [{
        value: 1,
        label: '后台接收'
      }, {
        value: 2,
        label: '小程序接收'
      }, {
        value: 4,
        label: '两者都接收'
      }],
      dataForm: {
        receiveUserName: ''
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 30,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false
    }
  },
  activated () {
    this.queryPage()
  },
  methods: {
    queryPage () {
      this.pageIndex = 1
      this.getDataList()
    },
    // 获取数据列表
    getDataList () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/sys/msgLog/pages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'receiveUserName': this.dataForm.receiveUserName,
          'targetType': this.dataForm.targetType
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle (val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle (id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle (id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/sys/msgLog/removeByIds'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': ids.join(',')
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    }
  }
}
</script>
