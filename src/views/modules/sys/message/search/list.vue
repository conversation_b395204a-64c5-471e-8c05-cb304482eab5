<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item>
        <el-input
          v-model="dataForm.content"
          placeholder="消息内容"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="dataForm.creatorName"
          placeholder="发送人"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="dataForm.receiveName"
          placeholder="接收人"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="dataForm.receivePhone"
          placeholder="手机号"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="dataForm.startTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="开始时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="dataForm.endTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="结束时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-select clearable v-model="dataForm.isRead" placeholder="是否已读">
          <el-option label="已读" value="Y">已读</el-option>
          <el-option label="未读" value="N">未读</el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          clearable
          v-model="dataForm.sendStatus"
          placeholder="发送状态"
        >
          <el-option label="发送成功" value="0">发送成功</el-option>
          <el-option label="发送失败" value="1">发送失败</el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" @click="queryPage()">查询</el-button>
        <el-button
          v-if="isAuth('sys:sysmessage:save')"
          type="primary"
          @click="addOrUpdateHandle()"
          >新增</el-button
        >
        <el-button
          v-if="isAuth('sys:messageSearch:delete')"
          type="danger"
          @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0"
          >批量删除</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%"
    >
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50"
      >
      </el-table-column>
      <el-table-column
        prop="content"
        header-align="center"
        align="center"
        show-overflow-tooltip
        label="消息内容"
      >
      </el-table-column>
      <el-table-column
        prop="creatorName"
        header-align="center"
        align="center"
        label="发送人"
      >
      </el-table-column>
      <el-table-column
        prop="receiveName"
        header-align="center"
        align="center"
        label="接收人"
      >
      </el-table-column>
      <el-table-column
        prop="receivePhone"
        header-align="center"
        align="center"
        label="接收人手机号"
      >
      </el-table-column>
      <el-table-column
        prop="createDate"
        header-align="center"
        align="center"
        label="发送时间"
      >
      </el-table-column>
      <el-table-column
        prop="sendStatus"
        header-align="center"
        align="center"
        label="发送状态"
      >
        <template slot-scope="scope">
          <span>{{
            scope.row.sendStatus + "" === "0" ? "发送成功" : "发送失败"
          }}</span>
        </template>
      </el-table-column>

      <el-table-column
        prop="isRead"
        header-align="center"
        align="center"
        label="是否已读"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.isRead + "" === "0" ? "未读" : "已读" }}</span>
        </template>
      </el-table-column>

      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row.id)"
            >详情</el-button
          >
          <el-button
            v-if="isAuth('sys:messageSearch:delete')"
            type="text"
            size="small"
            @click="deleteHandle(scope.row.id)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 30, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <sysmessage-detail
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></sysmessage-detail>
  </div>
</template>

<script>
import AddOrUpdate from '../manage/sysmessage-add-or-update.vue'
import SysmessageDetail from './sysmessage-detail.vue'
export default {
  data () {
    return {
      dataForm: {
        title: '',
        content: '',
        creatorName: '',
        receivePhone: '',
        receiveName: '',
        startTime: null,
        endTime: null,
        messageType: null,
        status: null,
        sendStatus: null,
        isRead: null,
        currentPage: 1,
        pageSize: 10
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 30,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false
    }
  },
  components: {
    AddOrUpdate,
    SysmessageDetail
  },
  activated () {
    this.queryPage()
  },
  methods: {
    queryPage () {
      this.pageIndex = 1
      this.getDataList()
    },
    // 获取数据列表
    getDataList () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/message/pages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'title': this.dataForm.title,
          'content': this.dataForm.content,
          'creatorName': this.dataForm.creatorName,
          'receiveName': this.dataForm.receiveName,
          'startTime': this.dataForm.startTime,
          'endTime': this.dataForm.endTime,
          'status': this.dataForm.status,
          'sendStatus': this.dataForm.sendStatus,
          'isRead': this.dataForm.isRead,
          'receivePhone': this.dataForm.receivePhone
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle (val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle (id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle (id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/message/removeByIds'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': ids.join(',')
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    }
  }
}
</script>
