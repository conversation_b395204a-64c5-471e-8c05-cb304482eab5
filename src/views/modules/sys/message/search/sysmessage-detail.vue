<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '详情'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="120px"
    >
      <!-- <el-form-item label="消息类型" prop="title">
        <el-input
          type="textarea"
          :autosize="{ minRows: 1, maxRows: 4 }"
          v-model="dataForm.title"
          placeholder="消息标题"
          readonly="true"
        ></el-input>
      </el-form-item> -->
      <el-form-item label="消息内容" prop="content">
        <el-input
          type="textarea"
          :autosize="{ minRows: 4, maxRows: 10 }"
          v-model="dataForm.content"
          placeholder="消息内容"
          readonly="true"
        ></el-input>
      </el-form-item>

      <el-form-item label="发送状态" prop="sendStatus">
        <el-input
          v-model="dataForm.sendStatus + '' === '0' ? '发送成功' : '发送失败'"
          placeholder="消息内容"
          readonly="true"
        ></el-input>
      </el-form-item>

      <el-form-item label="发送人姓名" prop="creatorName">
        <el-input
          v-model="dataForm.creatorName"
          placeholder="发送人姓名"
          readonly="true"
        ></el-input>
      </el-form-item>

      <el-form-item label="接收人姓名" prop="receiveName">
        <el-input
          v-model="dataForm.receiveName"
          placeholder="接收人姓名"
          readonly="true"
        ></el-input>
      </el-form-item>

      <el-form-item label="接收人手机号" prop="receivePhone">
        <el-input
          v-model="dataForm.receivePhone"
          placeholder="接收人手机号"
          readonly="true"
        ></el-input>
      </el-form-item>

      <el-form-item label="发送时间" prop="createDate">
        <el-input
          v-model="dataForm.createDate"
          placeholder=""
          readonly="true"
        ></el-input>
      </el-form-item>

      <el-form-item label="是否已读" prop="isRead" :error="isReadError">
        <el-input
          v-model="dataForm.isRead + '' === 'Y' ? '已读' : '未读'"
          placeholder="消息内容"
          readonly="true"
        ></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <!-- <el-button type="primary" @click="dataFormSubmit()">确定</el-button> -->
      <el-button type="primary" @click="visible = false">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: null,
        version: null,
        content: '',
        title: '',
        moduleId: '',
        type: '',
        messageType: '',
        status: '',
        sendStatus: '',
        creatorName: '',
        receiveId: '',
        receiveName: '',
        receivePhone: '',
        isRead: '',
        updator: ''
      }

    }
  },
  methods: {
    init (id) {
      this.dataForm.id = id || null
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/message`),
            method: 'get',
            params: this.$http.adornParams({ id: this.dataForm.id })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        // if (valid) {
        //   this.$http({
        //     url: this.$http.adornUrl(`/admin/message/${!this.dataForm.id ? 'save' : 'update'}`),
        //     method: 'post',
        //     data: this.$http.adornData(this.dataForm)
        //   }).then(({data}) => {
        //     if (data && data.code === 0) {
        //       this.$message({
        //         message: '操作成功',
        //         type: 'success',
        //         duration: 500,
        //         onClose: () => {
        //           this.visible = false
        //           this.$emit('refreshDataList')
        //         }
        //       })
        //     } else if (data && data.code === 303) {
        //       for (let it of data.obj) {
        //         this[`${it.field}Error`] = it.message
        //       }
        //     } else {
        //       this.$message.error(data.msg)
        //     }
        //   })
        // }
      })
    }
  }
}
</script>
