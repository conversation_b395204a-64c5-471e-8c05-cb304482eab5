<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '详情'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="120px"
    >
      <el-form-item label="消息类型" prop="title" :error="titleError">
        <el-input
          type="textarea"
          :autosize="{ minRows: 1, maxRows: 4 }"
          v-model="dataForm.title"
          placeholder="消息标题"
        ></el-input>
      </el-form-item>
      <el-form-item label="消息内容" prop="content" :error="contentError">
        <el-input
          type="textarea"
          :autosize="{ minRows: 4, maxRows: 10 }"
          v-model="dataForm.content"
          placeholder="消息内容"
        ></el-input>
      </el-form-item>

      <el-form-item
        label="消息分类"
        prop="messageType"
        :error="messageTypeError"
      >
        <el-radio-group v-model="dataForm.messageType">
          <el-radio label="message">消息通知</el-radio>
          <el-radio label="sms">短信通知</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="状态" prop="status" :error="statusError">
        <el-radio-group v-model="dataForm.status">
          <el-radio label="0">启用</el-radio>
          <el-radio label="1">暂停</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="发送状态" prop="sendStatus" :error="sendStatusError">
        <el-select v-model="dataForm.sendStatus" style="width: 100%">
          <el-option label="发送成功" value="0">发送成功</el-option>
          <el-option label="发送失败" value="1">发送失败</el-option>
        </el-select>
      </el-form-item>

      <el-form-item
        label="发送人姓名"
        prop="creatorName"
        :error="creatorNameError"
      >
        <el-input
          v-model="dataForm.creatorName"
          placeholder="发送人姓名"
        ></el-input>
      </el-form-item>

      <el-form-item
        label="接收人姓名"
        prop="receiveName"
        :error="receiveNameError"
      >
        <el-input
          v-model="dataForm.receiveName"
          placeholder="接收人姓名"
        ></el-input>
      </el-form-item>

      <el-form-item
        label="接收人手机号"
        prop="receivePhone"
        :error="receivePhoneError"
      >
        <el-input
          v-model="dataForm.receivePhone"
          placeholder="接收人手机号"
        ></el-input>
      </el-form-item>

      <el-form-item
        label="发送时间"
        prop="createDate"
        :error="receivePhoneError"
      >
        <el-input v-model="dataForm.createDate" placeholder=""></el-input>
      </el-form-item>

      <el-form-item label="是否已读" prop="isRead" :error="isReadError">
        <el-select v-model="dataForm.isRead" style="width: 100%">
          <el-option label="已读" value="Y">已读</el-option>
          <el-option label="未读" value="N">未读</el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <!-- <el-button type="primary" @click="dataFormSubmit()">确定</el-button> -->
      <el-button type="primary" @click="visible = false">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: null,
        version: null,
        content: '',
        title: '',
        moduleId: '',
        type: '',
        messageType: '',
        status: '',
        sendStatus: '',
        creatorName: '',
        receiveId: '',
        receiveName: '',
        receivePhone: '',
        isRead: '',
        updator: ''
      },
      dataRule: {
        title: [
          { required: true, message: '消息标题不能为空', trigger: 'blur' }
        ],
        messageType: [
          { required: true, message: '发送的类型，短信还是消息不能为空', trigger: 'blur' }
        ],
        creatorName: [
          { required: true, message: '发送人姓名不能为空', trigger: 'blur' }
        ],
        receiveId: [
          { required: true, message: '接收人id不能为空', trigger: 'blur' }
        ],
        receiveName: [
          { required: true, message: '接收人姓名不能为空', trigger: 'blur' }
        ],
        receivePhone: [
          { required: true, message: '接收人手机号不能为空', trigger: 'blur' }
        ],
        isRead: [
          { required: true, message: '是否已读不能为空', trigger: 'blur' }
        ]
      },
      contentError: null,
      titleError: null,
      moduleIdError: null,
      typeError: null,
      messageTypeError: null,
      statusError: null,
      sendStatusError: null,
      creatorNameError: null,
      receiveIdError: null,
      receiveNameError: null,
      receivePhoneError: null,
      isReadError: null,
      updatorError: null
    }
  },
  methods: {
    init (id) {
      this.dataForm.id = id || null
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/message`),
            method: 'get',
            params: this.$http.adornParams({ id: this.dataForm.id })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        // if (valid) {
        //   this.$http({
        //     url: this.$http.adornUrl(`/admin/message/${!this.dataForm.id ? 'save' : 'update'}`),
        //     method: 'post',
        //     data: this.$http.adornData(this.dataForm)
        //   }).then(({data}) => {
        //     if (data && data.code === 0) {
        //       this.$message({
        //         message: '操作成功',
        //         type: 'success',
        //         duration: 500,
        //         onClose: () => {
        //           this.visible = false
        //           this.$emit('refreshDataList')
        //         }
        //       })
        //     } else if (data && data.code === 303) {
        //       for (let it of data.obj) {
        //         this[`${it.field}Error`] = it.message
        //       }
        //     } else {
        //       this.$message.error(data.msg)
        //     }
        //   })
        // }
      })
    }
  }
}
</script>
