<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="120px"
    >
      <!-- <el-form-item label="类型" prop="type" :error="typeError">
        <el-radio-group v-model="dataForm.type" @change="messageHandChange">
          <el-radio label="1">短信通知</el-radio>
          <el-radio label="2">消息通知</el-radio>
        </el-radio-group>
      </el-form-item> -->
      <el-form-item label="标题 " prop="name" :error="nameError">
        <el-input
          v-model="dataForm.name"
          placeholder="请输入标题名称 "
        ></el-input>
      </el-form-item>

      <el-form-item label="编码" prop="code" :error="codeError">
        <el-input
          v-model="dataForm.code"
          placeholder="请输入模板编码"
        ></el-input>
      </el-form-item>

      <el-form-item label="模板内容" prop="template" :error="templateError">
        <el-input
          type="textarea"
          :autosize="{ minRows: 4, maxRows: 10 }"
          v-model="dataForm.template"
          placeholder="模板内容"
        ></el-input>
      </el-form-item>
      <div v-if="isHide">
        <el-form-item label="短信ip地址" prop="host" :error="hostError">
          <el-input
            v-model="dataForm.host"
            placeholder="ip地址（带http或https)"
          ></el-input>
        </el-form-item>
        <el-form-item label="企业ID" prop="userId" :error="userIdError">
          <el-input v-model="dataForm.userId" placeholder="企业ID"></el-input>
        </el-form-item>

        <el-form-item label="账户" prop="account" :error="accountError">
          <el-input v-model="dataForm.account" placeholder="账户"></el-input>
        </el-form-item>

        <el-form-item label="密码" prop="password" :error="passwordError">
          <el-input v-model="dataForm.password" placeholder="密码"></el-input>
        </el-form-item>

        <el-form-item label="接入码" prop="extno" :error="extnoError">
          <el-input v-model="dataForm.extno" placeholder="接入码"></el-input>
        </el-form-item>

        <el-form-item label="响应数据类型" prop="rt" :error="rtError">
          <el-input
            v-model="dataForm.rt"
            placeholder="响应数据类型 例如：json"
          ></el-input>
        </el-form-item>
      </div>
      <el-form-item label="状态" prop="used" :error="usedError">
        <el-radio-group v-model="dataForm.used">
          <el-radio :label="true">启用</el-radio>
          <el-radio :label="false">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: null,
        version: null,
        name: '',
        code: '',
        template: '',
        host: '',
        userId: '',
        account: '',
        password: '',
        extno: '',
        type: '1',
        rt: '',
        used: ''
      },
      dataRule: {
        code: [
          { required: true, message: 'code值 唯一性不能为空', trigger: 'blur' }
        ],
        template: [
          { required: true, message: '模板内容不能为空', trigger: 'blur' }
        ],
        host: [
          { required: true, message: 'ip地址（带http或https)不能为空', trigger: 'blur' }
        ],
        userId: [
          { required: true, message: '企业ID不能为空', trigger: 'blur' }
        ],
        account: [
          { required: true, message: '账户不能为空', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '密码不能为空', trigger: 'blur' }
        ],
        // extno: [
        //   { required: true, message: '接入码不能为空', trigger: 'blur' }
        // ],
        type: [
          { required: true, message: '类型 1-短信通知  2-消息通知不能为空', trigger: 'blur' }
        ],
        rt: [
          { required: true, message: '响应数据类型 例如：json不能为空', trigger: 'blur' }
        ],
        used: [
          { required: true, message: '是否使用中不能为空', trigger: 'blur' }
        ]
      },
      nameError: null,
      codeError: null,
      templateError: null,
      hostError: null,
      userIdError: null,
      accountError: null,
      passwordError: null,
      extnoError: null,
      typeError: null,
      rtError: null,
      usedError: null,
      isHide: false
    }
  },
  methods: {
    init (id) {
      this.isHide = true
      this.dataForm.id = id || null
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/noticetemplate`),
            method: 'get',
            params: this.$http.adornParams({ id: this.dataForm.id })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
              this.dataForm.type = this.dataForm.type + ''
            }
          })
        }
      })
    },
    messageHandChange () {
      if (this.dataForm.type + '' === 1 + '') {
        this.isHide = true
      } else {
        this.isHide = false
      }
    },
    // 表单提交
    dataFormSubmit () {
      if (this.dataForm.type + '' === '2') {
        this.dataForm.host = ''
        this.dataForm.password = ''
        this.dataForm.userId = ''
        this.dataForm.account = ''
        this.dataForm.extno = ''
      }
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/admin/noticetemplate/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>
