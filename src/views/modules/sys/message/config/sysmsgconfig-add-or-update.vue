<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
    >
      <el-row :gutter="20">
        <el-col :span="16">
          <el-form-item label="模板名称 " prop="name" :error="nameError">
            <el-input
              v-model="dataForm.name"
              placeholder="模板名称 "
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="16">
          <el-form-item label="code值 唯一性" prop="code" :error="codeError">
            <el-input
              v-model="dataForm.code"
              placeholder="code值 (唯一性)"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="16">
          <el-form-item label="模板内容" prop="template" :error="templateError">
            <el-input
              type="textarea"
              v-model="dataForm.template"
              placeholder="模板内容"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-tooltip class="item" effect="light" placement="top">
            <div slot="content">
              执行动作关键词：{operate} <br />
              审核结果关键词：{result} <br />
              单号关键词： {number} <br />
              名称关键词： {name} <br />
              积分关键词： {point} <br />
            </div>
            <i class="el-icon-info"></i>
          </el-tooltip>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="16">
          <el-form-item label="角色">
            <el-select
              v-model="dataForm.roleId"
              placeholder="请选择接收角色"
              clearable
            >
              <el-option
                v-for="item in roleList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="16">
          <el-form-item
            label="消息接收方式"
            prop="targetType"
            :error="targetTypeError"
          >
            <el-select
              v-model="dataForm.targetType"
              placeholder="请选择接收方式"
              clearable
            >
              <el-option
                v-for="item in targetList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="16">
          <el-form-item label="是否启用" prop="status" :error="statusError">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="true">启用</el-radio>
              <el-radio :label="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      roleList: [],
      targetList: [{
        value: 1,
        label: '后台接收'
      }, {
        value: 2,
        label: '小程序接收'
      }, {
        value: 4,
        label: '两者都接收'
      }],
      visible: false,
      dataForm: {
        id: null,
        version: null,
        name: '',
        code: '',
        template: '',
        roleId: '',
        targetType: '',
        status: true
      },
      dataRule: {
        name: [
          { required: true, message: '模板名称不能为空', trigger: 'blur' }
        ],
        code: [
          { required: true, message: 'code值不能为空', trigger: 'blur' }
        ],
        template: [
          { required: true, message: '模板内容不能为空', trigger: 'blur' }
        ],
        targetType: [
          { required: true, message: '接收方式未选择', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '状态未选择', trigger: 'blur' }
        ]
      },
      nameError: null,
      codeError: null,
      templateError: null,
      targetTypeError: null,
      statusError: null
    }
  },
  methods: {
    init (id) {
      this.dataForm.id = id || null
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/sys/msgConfig`),
            method: 'get',
            params: this.$http.adornParams({ id: this.dataForm.id })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
            }
          })
        }
        this.getAllRoles()
      })
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/admin/sys/msgConfig/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    // 获取所有角色
    getAllRoles () {
      this.$http({
        url: this.$http.adornUrl(`/admin/role/all`),
        method: 'get'
      }).then(({ data }) => {
        if (data != null) {
          this.roleList = data
        }
      })
    }
  }
}
</script>
