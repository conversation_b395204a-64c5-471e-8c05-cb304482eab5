<!-- 随手拍列表页面 -- 查询所有状态 -->
<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
       ref="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <el-form-item label="问题标题" prop="title">
        <el-input
          v-model="dataForm.title"
          placeholder="请输入问题标题"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="所属区域" prop="regionCode">
        <el-select
          v-model="dataForm.regionCode"
          clearable
          placeholder="请选择所属区域"
        >
          <el-option
            v-for="item in areaList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="责任局办" prop="relationName">
        <el-popover
          ref="orgListPopover"
          placement="bottom-start"
          trigger="click"
        >
          <div style="overflow: hidden; width: 320px">
            <div style="overflow-y: auto; max-height: 500px; min-width: 400px;max-width:600px">
              <el-scrollbar style="height: 100%; width: 100%">
                <el-tree
                  style="width: 550px"
                  :data="orgList"
                  :props="orgProps"
                  node-key="id"
                  ref="orgTree"
                  @current-change="orgTreeCurrentChangeHandle"
                  :default-expand-all="true"
                  :highlight-current="true"
                  :expand-on-click-node="false"
                >
                </el-tree>
              </el-scrollbar>
            </div>
          </div>
        </el-popover>
        <el-input
          v-model="dataForm.relationName"
          v-popover:orgListPopover
          placeholder="请选择责任局办"
          @input="change($event)"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="问题状态" prop="state">
        <el-select
          v-model="dataForm.state"
          clearable
          placeholder="请选择问题状态"
        >
          <el-option
            v-for="item in states"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" @click="getDataList()">查询</el-button>
        <el-button icon="el-icon-refresh" type="warning" @click="resetForm()">重置</el-button>
<!--        <el-button type="success" @click="importData()">导入</el-button>-->
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%"
    >
      <el-table-column
        prop="questionCode"
        header-align="center"
        align="center"
        label="问题编号"
      >
      </el-table-column>
      <el-table-column
        prop="title"
        header-align="center"
        align="center"
        label="问题标题"
      >
      </el-table-column>
      <el-table-column
        prop="regionName"
        header-align="center"
        align="center"
        label="所属街道"
      >
      </el-table-column>
      <el-table-column
        prop="communityName"
        header-align="center"
        align="center"
        label="所属社区"
      >
      </el-table-column>
      <el-table-column
        prop="gridName"
        header-align="center"
        align="center"
        label="所属网格"
      >
      </el-table-column>
      <el-table-column
        prop="positionName"
        header-align="center"
        align="center"
        label="所属点位"
      >
      </el-table-column>
      <el-table-column
        prop="positionCode"
        header-align="center"
        align="center"
        label="点位编码"
      >
      </el-table-column>
      <el-table-column
        prop="address"
        header-align="center"
        align="center"
        label="详细地址"
      >
      </el-table-column>


      <el-table-column
        prop="questionType.name"
        header-align="center"
        align="center"
        label="问题分类"
      >
      </el-table-column>
      <el-table-column
        prop="relation.relationName"
        header-align="center"
        align="center"
        label="责任局办"
      >
      </el-table-column>
      <el-table-column
        prop="questionType.disposalDuration"
        header-align="center"
        align="center"
        label="处置时长（天）"
      >
      </el-table-column>
      <el-table-column
        prop="subUserName"
        header-align="center"
        align="center"
        label="提交人姓名"
      >
      </el-table-column>
      <el-table-column
        prop="subPhone"
        header-align="center"
        align="center"
        label="提交人电话"
      >
      </el-table-column>
      <el-table-column
        prop="myself"
        header-align="center"
        align="center"
        label="是否自行解决"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.myself === 1">是</span>
          <span v-else>否</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="createDate"
        header-align="center"
        align="center"
        label="提交日期"
      >
      </el-table-column>
      <el-table-column
        prop="questionExamine"
        header-align="center"
        align="center"
        label="问题状态"
      >
        <template slot-scope="scope">
          <el-tag
            v-if="scope.row.questionExamine.state == 1"
            size="small"
            type="warning"
            >待审核</el-tag
          >
          <el-tag v-if="scope.row.questionExamine.state == 2" size="small"
            >已撤回</el-tag
          >
          <el-tag
            v-if="scope.row.questionExamine.state == 4"
            size="small"
            type="danger"
            >已退回</el-tag
          >
          <el-tag
            v-if="scope.row.questionExamine.state == 8"
            size="small"
            type="primary"
            >待回复</el-tag
          >
          <el-tag
            v-if="scope.row.questionExamine.state == 16"
            size="small"
            type="info"
            >已回复</el-tag
          >
          <el-tag
            v-if="scope.row.questionExamine.state == 32"
            size="small"
            type="success"
            >已完成</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            v-if="scope.row.questionExamine.state === 16"
            @click="finalJudgment(scope.row.id)"
            >终审</el-button
          >
          <el-button type="text" @click="getDetail(scope.row.id)"
            >详情</el-button
          >
          <!--          <el-button type="text" @click="edit(scope.row.id)">转发</el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 30, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination>
  </div>
</template>

<script>
export default {
  data () {
    return {
      startTime: '',
      endTime: '',
      dataForm: {
        id: null,
        title: '',
        state: '',
        regionCode: '',
        relationId: '',
        relationName: ''
      },
      options: [{
        value: '1',
        label: '随手拍工单'
      }, {
        value: '2',
        label: '点位测评工单'
      }],
      states: [{
        value: '1',
        label: '待审核'
      }, {
        value: '2',
        label: '已撤回'
      }, {
        value: '4',
        label: '已退回'
      }, {
        value: '8',
        label: '待回复'
      }, {
        value: '16',
        label: '已回复'
      }, {
        value: '32',
        label: '已终审'
      }],
      updateVisible: false,
      dataList: [],
      pageIndex: 1,
      pageSize: 30,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      areaList: [],
      orgList: [],
      orgProps: {
        key: 'id',
        label: 'name',
        children: 'children'
      }
    }
  },
  activated () {
    this.getRelationList()
    this.getAreaType()
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/position/position/question/getPages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'state': this.dataForm.state,
          'title': this.dataForm.title,
          'regionCode': this.dataForm.regionCode,
          'relationId': this.dataForm.relationId
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle (val) {
      this.dataListSelections = val
    },
    // 详情跳转页面
    getDetail (id) {
      this.$router.push({ name: 'clapDetail', query: { id: id } })
    },
    // 属性区域列表
    getAreaType () {
      this.$http({
        url: this.$http.adornUrl('/position/area/top')
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.areaList = data.obj
        } else {
          this.areaList = {}
        }
      })
    },
    // 获取数据列表
    getRelationList () {
      // this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/org/tree'),
        method: 'get',
        params: this.$http.adornParams({ level: 1 })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.orgList = data.obj
          this.orgList.forEach(data => {
            data.id = data.id + ''
          })
        } else {
          this.orgList = []
        }
        // this.orgListTreeSetCurrentNode()
      })
    },
    orgListTreeSetCurrentNode () {
      let key = this.dataForm.orgId
      if (key) {
        this.$refs.orgTree.setCurrentKey(key)
        this.orgName = (this.$refs.orgTree.getCurrentNode() || {})['name']
      } else {
        this.$refs.orgTree.setCurrentKey([])
        this.orgName = ''
      }
    },
    // 区域树选中
    orgTreeCurrentChangeHandle (data, node) {
      this.dataForm.relationId = data.id
      this.dataForm.relationName = data.name
      this.$refs[`orgListPopover`].doClose()
    },

    edit (id) {
    },

    change (e) {
      this.dataForm.relationId = ''
      this.$forceUpdate()
    },
    resetForm () {
      this.$nextTick(() => {
        this.dataForm.relationId = null
        this.pageIndex = 1
        this.pageSize = 30
        this.$refs['dataForm'].resetFields()
        this.getDataList()
      })
    },
    importData () {
      this.$router.push({
        path: '/evaluateImport',
        query: {
          type: 5
        }
      })
    },
    // 终审
    finalJudgment (id) {
      this.$confirm(`确定终审此随手拍?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/position/position/question/finalJudgment'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '终审成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    }
  }
}
</script>
