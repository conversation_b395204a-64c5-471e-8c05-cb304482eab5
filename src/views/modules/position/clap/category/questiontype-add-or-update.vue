<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
    >
      <el-form-item label="分类编码" prop="code" :error="codeError">
        <el-input
          v-model="dataForm.code"
          placeholder="请输入分类编码"
        ></el-input>
      </el-form-item>

      <el-form-item label="分类名称" prop="name" :error="nameError">
        <el-input v-model="dataForm.name" placeholder="请输入名称"></el-input>
      </el-form-item>

      <!-- <el-form-item label="上层ID" prop="parentId" :error="parentIdError">
        <el-input v-model="dataForm.parentId" placeholder="上层ID"></el-input>
      </el-form-item> -->

      <el-form-item label="排序" prop="sequence" :error="sequenceError">
        <el-input-number
          v-model="dataForm.sequence"
          controls-position="right"
          :min="0"
          style="width: 30%"
        ></el-input-number>
      </el-form-item>

      <el-form-item
        label="处置时长"
        prop="disposalDuration"
        :error="disposalDurationError"
      >
        <el-input-number
          v-model="dataForm.disposalDuration"
          controls-position="right"
          :min="0"
          style="width: 30%"
        ></el-input-number>
      </el-form-item>

      <el-form-item label="状态" prop="status" :error="statusError">
        <el-radio-group v-model="dataForm.status">
          <el-radio :label="true">启用</el-radio>
          <el-radio :label="false">禁用</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="责任局办">
        <el-popover
          ref="orgListPopover"
          placement="bottom-start"
          trigger="click"
        >
          <el-tree
            style="width: 550px"
            :data="orgList"
            :props="orgProps"
            node-key="id"
            ref="orgTree"
            @current-change="orgTreeCurrentChangeHandle"
            :default-expand-all="true"
            :highlight-current="true"
            :expand-on-click-node="false"
          >
          </el-tree>
        </el-popover>
        <el-input
          v-model="orgName"
          v-popover:orgListPopover
          :readonly="true"
          placeholder="点击选择组织架构"
          @input="change($event)"
        ></el-input>
      </el-form-item>

      <!-- <el-form-item label="责任人" prop="name">
        <el-select
          style="width: 100%"
          @change="getUserInfo(dataForm.userId)"
          v-model="dataForm.userId"
          placeholder="请选择责任人"
          multiple
          clearable
        >
          <el-option
            v-for="user in userList"
            :key="user.id"
            :label="user.name"
            :value="user.id"
          />
        </el-select>
      </el-form-item> -->
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: null,
        version: null,
        code: '',
        name: '',
        parentId: '',
        sequence: '',
        disposalDuration: 7,
        status: '',
        typeId: '',
        orgId: '',
        orgName: '',
        type: '',
        org: {}
      },
      dataRule: {
        name: [
          { required: true, message: '名称不能为空', trigger: 'blur' }
        ],
        parentId: [
          { required: true, message: '上层ID不能为空', trigger: 'blur' }
        ],
        sequence: [
          { required: true, message: '排序不能为空', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '状态(启用/禁用)不能为空', trigger: 'blur' }
        ],
        disposalDuration: [
          { required: true, message: '处置时长不能为空', trigger: 'blur' }
        ]
      },
      codeError: null,
      nameError: null,
      parentIdError: null,
      sequenceError: null,
      statusError: null,
      disposalDurationError: null,
      orgList: [],
      orgProps: {
        key: 'id',
        label: 'name',
        children: 'children'
      },
      userList: [],
      orgid: '',
      orgName: ''
    }
  },
  methods: {
    init (id) {
      this.dataForm.id = id || null
      this.dataForm.typeId = null
      this.dataForm.orgId = null
      this.dataForm.orgName = null
      this.orgId = null
      this.orgName = null
      this.dataForm.name = null
      this.dataForm.code = null
      this.dataForm.sequence = null
      this.dataForm.status = ''
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/position/question/type/getDetailById`),
            method: 'get',
            params: this.$http.adornParams({ id: this.dataForm.id })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
              if (data.obj.org) {
                this.dataForm.typeId = data.obj.org.typeId
                this.dataForm.orgId = data.obj.org.relationId + ''
                this.dataForm.orgName = data.obj.org.relationName
                this.orgId = data.obj.org.relationId + ''
                this.orgName = data.obj.org.relationName
              }
            }
          })
        }
      })
      this.getDataList()
    },

    getUserInfo (userIds) {
    },

    change (e) {
      this.$forceUpdate()
    },

    // 获取数据列表
    getDataList () {
      // this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/org/tree'),
        method: 'get',
        params: this.$http.adornParams({ level: 1 })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.orgList = data.obj
          this.orgList.forEach(data => {
            data.id = data.id + ''
          })
        } else {
          this.orgList = []
        }
        this.orgListTreeSetCurrentNode()
      })
    },

    getUserList (code) {
      this.$http({
        url: this.$http.adornUrl('/admin/user/getUserByOrgCode'),
        method: 'get',
        params: { code: code }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.userList = resp.data.obj
        } else {
          this.userList = []
        }
      })
    },

    // 区域树选中
    orgTreeCurrentChangeHandle (data, node) {
      this.orgId = data.id
      this.orgName = data.name
      this.$refs[`orgListPopover`].doClose()
    },
    orgListTreeSetCurrentNode () {
      let key = this.dataForm.orgId
      if (key) {
        this.$refs.orgTree.setCurrentKey(key)
        this.orgName = (this.$refs.orgTree.getCurrentNode() || {})['name']
      } else {
        this.$refs.orgTree.setCurrentKey([])
        this.orgName = ''
      }
    },
    // 表单提交
    dataFormSubmit () {
      const orgList = {
        typeId: this.typeId,
        relationId: this.orgId,
        relationName: this.orgName
      }
      this.dataForm.org = orgList
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/position/question/type/${!this.dataForm.id ? 'add' : 'edit'}`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
              this.dataForm.orgId = ''
              this.dataForm.orgName = ''
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  },
  watch: {
    'orgName': {
      handler (val) {
      }
    }
  }
}
</script>
