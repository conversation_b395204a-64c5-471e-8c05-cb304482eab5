<!-- 随手拍列表页面 -- 查询待审核状态 -->
<template>
  <div class="mod-report">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item label="提交日期">
        <el-date-picker
          type="date"
          value-format="yyyy-MM-dd"
          v-model="dataForm.startTime"
          placeholder="选择开始日期"
          align="right">
        </el-date-picker>
        <el-date-picker
          type="date"
          value-format="yyyy-MM-dd"
          v-model="dataForm.endTime"
          placeholder="选择结束日期"
          align="right">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="责任局办">
        <el-popover
          ref="orgListPopover"
          placement="bottom-start"
          trigger="click"
        >
          <el-tree
            style="width: 550px"
            :data="orgList"
            :props="orgProps"
            node-key="id"
            ref="orgTree"
            @current-change="orgTreeCurrentChangeHandle"
            :default-expand-all="true"
            :highlight-current="true"
            :expand-on-click-node="false"
          >
          </el-tree>
        </el-popover>
        <el-input
          v-model="dataForm.relationName"
          v-popover:orgListPopover
          placeholder="请选择责任局办"
          @input="change($event)"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button  @click="getDataList()">查询</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary"  @click="exportExcel()">导出数据</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column
        prop="name"
        header-align="center"
        align="center"
        label="问题分类">
      </el-table-column>
      <el-table-column
        prop="totalCount"
        header-align="center"
        align="center"
        label="收到问题">
      </el-table-column>
      <el-table-column
        prop="resolvedCount"
        header-align="center"
        align="center"
        label="已解决">
      </el-table-column>
      <el-table-column
        prop="notOverdueCount"
        header-align="center"
        align="center"
        label="未逾期">
      </el-table-column>
      <el-table-column
        prop="overdueTenCount"
        header-align="center"
        align="center"
        label="逾期超10天">
      </el-table-column>
      <el-table-column
        prop="overdueFifteenCount"
        header-align="center"
        align="center"
        label="逾期超15天">
      </el-table-column>
      <el-table-column
        prop="overdueThirtyCount"
        header-align="center"
        align="center"
        label="逾期超30天">
      </el-table-column>
    </el-table>
<!--    <el-pagination-->
<!--      @size-change="sizeChangeHandle"-->
<!--      @current-change="currentChangeHandle"-->
<!--      :current-page="pageIndex"-->
<!--      :page-sizes="[10, 20, 30, 50, 100]"-->
<!--      :page-size="pageSize"-->
<!--      :total="totalPage"-->
<!--      layout="total, sizes, prev, pager, next, jumper">-->
<!--    </el-pagination>-->
    <div class="report-tp">
      <div class="report-left" >
        <div  ref="circleEchart" style="width:100%;height: 400px; "></div>
      </div>
      <div class="report-right">
        <div ref="wireEchart" style="width:100%;height: 400px; "></div>
      </div>
    </div>
  </div>
</template>

<script>
  var echarts = require('echarts')
  export default {
    data () {
      return {
        dataForm: {
          startTime: '',
          endTime: '',
          // regionCode: '',
          relationId: '',
          relationName: ''
        },
        dataList: [],
        dataListLoading: false,
        dataListSelections: [],
        areaList: [],
        orgList: [],
        orgProps: {
          key: 'id',
          label: 'name',
          children: 'children'
        },
        circleData: [],
        circleName: [],
        circleColor: [],
        wireData: []
      }
    },
    created () {
      this.getRelationList()
      this.getDataList()
    },
    mounted () {
      this.initCircleEchart()
      this.initWireEchart()
    },
    methods: {
      // 获取数据列表
      getDataList () {
        this.dataListLoading = true
        this.circleData = []
        this.circleName = []
        this.circleColor = []
        this.wireData = []
        if (!this.dataForm.relationName) {
          this.dataForm.relationId = ''
        }
        this.$http({
          url: this.$http.adornUrl('/position/statistics/question/type'),
          method: 'post',
          data: this.$http.adornData({
            'relationId': this.dataForm.relationId,
            'startTime': this.dataForm.startTime,
            'endTime': this.dataForm.endTime
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataList = data.obj
            this.dataList.forEach(e => {
              this.circleData.push({value: e.totalCount, name: e.name})
              this.circleName.push(e.name)
              let data = []
              data.push(e.totalCount)
              data.push(e.resolvedCount)
              data.push(e.notOverdueCount)
              data.push(e.overdueTenCount)
              data.push(e.overdueFifteenCount)
              data.push(e.overdueThirtyCount)
              if (e.name === '道路交通') e.colorStyle = '#9dd3e8'
              if (e.name === '公共秩序') e.colorStyle = '#fbd88a'
              if (e.name === '道路交通事故') e.colorStyle = '#f39494'
              if (e.name === '其他问题') e.colorStyle = '#9487d7'
              if (e.name === '设施设备') e.colorStyle = '#b2db9e'
              if (e.name === '小区环境') e.colorStyle = '#879bd7'
              this.circleColor.push(e.colorStyle)
              this.wireData.push(
                {name: e.name,
                  type: 'line',
                  stack: 'Total',
                  areaStyle: {color: e.colorStyle},
                  lineStyle: {color: e.colorStyle},
                  emphasis: {focus: 'series'},
                  data: data})
            })
            this.initCircleEchart()
            this.initWireEchart()
          } else {
            this.dataList = []
          }
          this.dataListLoading = false
        })
      },
      // 多选
      selectionChangeHandle (val) {
        this.dataListSelections = val
      },
      // 获取数据列表
      getRelationList () {
        this.$http({
          url: this.$http.adornUrl('/admin/org/tree'),
          method: 'get',
          params: this.$http.adornParams({ level: 1 })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.orgList = data.obj
            this.orgList.forEach(data => {
              data.id = data.id + ''
            })
          } else {
            this.orgList = []
          }
        })
      },
      orgListTreeSetCurrentNode () {
        let key = this.dataForm.orgId
        if (key) {
          this.$refs.orgTree.setCurrentKey(key)
          this.orgName = (this.$refs.orgTree.getCurrentNode() || {})['name']
        } else {
          this.$refs.orgTree.setCurrentKey([])
          this.orgName = ''
        }
      },
      // 区域树选中
      orgTreeCurrentChangeHandle (data, node) {
        this.dataForm.relationId = data.id
        this.dataForm.relationName = data.name
        this.$refs[`orgListPopover`].doClose()
      },
      change (e) {
        this.$forceUpdate()
      },
      exportExcel () {
        if (!this.dataForm.relationName) {
          this.dataForm.relationId = ''
        }
        this.$http({
          url: this.$http.adornUrl('/position/statistics/question/type/export'),
          method: 'post',
          responseType: 'arraybuffer',
          data: this.$http.adornData({
            'relationId': this.dataForm.relationId,
            'startTime': this.dataForm.startTime,
            'endTime': this.dataForm.endTime
          })
        }).then(({data}) => {
          if (data.code && data.code !== 0) {
            this.$message.error('导出失败')
          } else {
            let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
            window.location.href = URL.createObjectURL(blob)
            this.$message({
              type: 'success',
              message: '导出数据成功'
            })
          }
        })
      },
      initCircleEchart () {
        let myChart = echarts.init(this.$refs.circleEchart)
        myChart.clear()
        myChart.setOption({
          tooltip: {
            trigger: 'item'
            // formatter: '{a} <br/>{b} : {c} ({d}%)'
          },
          legend: {
            orient: 'horizontal',
            x: 'left',
            data: this.circleName
          },
          color: this.circleColor,
          calculable: true,
          series: [
            {
              name: '访问来源',
              type: 'pie',
              radius: '50%',
              label: {
                normal: {
                  formatter: '{b}{d}%' // 线上文字
                }
              },
              // emphasis: {
                // itemStyle: {
                //   shadowBlur: 10,
                //   shadowOffsetX: 0,
                //   shadowColor: 'rgba(0, 0, 0, 0.5)'
                // }
              // },
              // itemStyle: {
              //   normal: {
              //     label: {
              //       show: false
              //     },
              //     labelLine: {
              //       show: false
              //     }
              //   },
              //   emphasis: {
              //     label: {
              //       show: true,
              //       position: 'center',
              //       textStyle: {
              //         fontSize: '20',
              //         fontWeight: 'bold'
              //       }
              //     }
              //   }
              // },
              data: this.circleData
            }
          ]
        }, true)
      },
      initWireEchart () {
        let myChart1 = echarts.init(this.$refs.wireEchart)
        myChart1.clear()
        myChart1.setOption({
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: '#6a7985'
              }
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          color: this.circleColor,
          legend: {
            data: this.circleName
          },
          // calculable: true,
          xAxis: [
            {
              type: 'category',
              boundaryGap: false,
              data: ['收到问题', '已解决', '未逾期', '逾期超10天', '逾期超15天', '逾期超30天']
            }
          ],
          yAxis: [
            {
              type: 'value',
              axisTick: {
                show: false
              },
              axisLine: {
                // y轴线的颜色以及宽度
                show: false // 控制线的显隐
              }
            }
          ],
          series: this.wireData
        }, true)
      }
    }
  }
</script>
<style lang="scss">
  .report-tp {
    margin-top: 100px;
    display: flex;
    justify-content: space-between;
    .report-left {
      width: 45%;
    }
    .report-right {
      width: 55%
    }
  }
</style>

