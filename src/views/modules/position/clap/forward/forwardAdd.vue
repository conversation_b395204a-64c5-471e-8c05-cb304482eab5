<template>
  <div>
    <el-dialog
      class="forward-dialog"
      title="IOC流转记录"
      :close-on-click-modal="false"
      :visible.sync="visible"
    >
      <el-form
        :model="dataForm"
        :rules="dataRule"
        ref="dataForm"
        @keyup.enter.native="dataFormSubmit()"
        label-width="150px"
        :disabled="false"
      >
        <el-form-item label="问题标题" prop="title" :error="titleError">
          <el-input
            type="textarea"
            :rows="2"
            v-model="dataForm.title"
            placeholder=""
          ></el-input>
        </el-form-item>
        <!-- <el-form-item label="问题描述" prop="title" :error="title">
          <el-input type="textarea" :rows="2" disabled v-model="title" placeholder=""></el-input>
        </el-form-item> -->
        <el-form-item label="问题图片">
          <el-row :gutter="20">
            <el-col
              :span="6"
              v-for="(item, index) in dataForm.fileUrlList"
              :key="index"
            >
              <img width="100%" :src="$http.adornUrl('/file' + item)" />
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="所属区域">
          <el-select v-model="dataForm.regionCode" clearable>
            <el-option
              v-for="dict in regionList"
              :key="dict.id"
              :label="dict.name"
              :value="dict.code"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="所属社区">
          <div>海悦社区</div>
        </el-form-item>
        <el-form-item label="属性网格">
         <div>海悦社区</div>
        </el-form-item> -->
        <el-form-item
          label="详细地址"
          prop="contentDesc"
          :error="contentDescError"
        >
          <el-input
            type="textarea"
            :rows="2"
            v-model="dataForm.address"
            placeholder=""
          ></el-input>
        </el-form-item>
        <el-form-item label="时间" prop="contentDesc" :error="contentDescError">
          <el-date-picker
            type="date"
            value-format="yyyy-MM-dd"
            v-model="dataForm.createDate"
            placeholder="选择日期"
            style="width: 100%"
          ></el-date-picker>
        </el-form-item>
        <el-form-item prop="contentDesc" :error="contentDescError">
          <el-button type="primary" @click="dataFormSubmit()">确认</el-button>
          <el-button @click="visible = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
s
<script>
import questionLogList from '../component/questionLogList.vue'
export default {
  components: {
    questionLogList
  },
  data () {
    return {
      visible: false,
      dialogVisible: false,
      dataForm: {
        id: null,
        orderName: '',
        contentDesc: '',
        orderAddress: '',
        startTime: null,
        endTime: null,
        orderFileUrlList: [],
        region: '',
        title: ''
      },
      dataRule: {
        orderName: [
          { required: true, message: '工单名称不能为空', trigger: 'blur' }
        ],
        title: [
          { required: true, message: '问题标题不能为空', trigger: 'blur' }
        ],
        orderAddress: [
          { required: true, message: '工单详细地址不能为空', trigger: 'blur' }
        ],
        startTime: [
          { required: true, message: '工单开始时间不能为空', trigger: 'blur' }
        ],
        endTime: [
          { required: true, message: '工单结束时间不能为空', trigger: 'blur' }
        ]
      },
      orderNameError: null,
      contentDescError: null,
      orderAddressError: null,
      startTimeError: null,
      endTimeError: null,
      titleError: null,
      regionList: []
    }
  },
  created () {
    this.getRegionList()
  },
  methods: {
    init (id) {
      this.dataForm.id = id || null
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/position/position/question/detail`),
            method: 'get',
            params: this.$http.adornParams({ id: this.dataForm.id })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
            }
          })
        }
      })
    },
    getRegionList () {
      this.$http({
        url: this.$http.adornUrl('/position/area/top'),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.regionList = data.obj
        } else {
          this.regionList = []
        }
      })
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.clearErrors()
          const files = []
          if (this.dataForm.fileUrlList) {
            this.dataForm.fileUrlList.forEach(data => {
              const file = {
                fileName: null,
                fileUrl: window.SITE_CONFIG.baseUrl + '/file' + data
              }
              files.push(file)
            })
          }
          this.$http({
            url: this.$http.adornUrl(`/position/position/question/forwardIoc`),
            method: 'post',
            data: this.$http.adornData({
              'relationId': this.$route.query.id,
              'messageId': this.$route.query.id,
              'workOrderNo': null,
              'files': files,
              'workTypeCode': null,
              'workType': null,
              'typeCode': null,
              'type': null,
              'orderTitle': this.dataForm.title,
              'orderContent': this.dataForm.title,
              'reportAddress': this.dataForm.address,
              'reportLongitude84': null,
              'reportLatitude84': null,
              'reportThumbnail': null,
              'regionName': this.dataForm.regionName
            })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    clearErrors () {
      this.orderNameError = null
      this.contentDescError = null
      this.orderAddressError = null
      this.startTimeError = null
      this.endTimeError = null
    }
  }
}
</script>

<style lang="scss"  scoped>
/deep/.forward-dialog {
  .el-select {
    width: 100%;
  }
  .el-date-editor {
    width: 100%;
  }
  .title-wd {
    width: 150px;
    text-align: right;
    padding-right: 12px;
    color: #606266;
  }
}
</style>

