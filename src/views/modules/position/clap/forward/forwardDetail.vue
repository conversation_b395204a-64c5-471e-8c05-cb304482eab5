<template>
  <div>
    <el-dialog
      class="forward-dialog"
      title="IOC流转记录"
      :close-on-click-modal="false"
      :visible.sync="visible"
    >
      <el-form
        :model="dataForm"
        :rules="dataRule"
        ref="dataForm"
        @keyup.enter.native="dataFormSubmit()"
        label-width="150px"
        :disabled="false"
      >
        <el-form-item label="问题标题" prop="title" :error="title">
          <el-input
            type="textarea"
            :rows="2"
            v-model="dataForm.title"
            placeholder=""
          ></el-input>
        </el-form-item>
        <!-- <el-form-item label="问题描述" prop="title" :error="title">
          <el-input type="textarea" :rows="2" disabled v-model="title" placeholder=""></el-input>
        </el-form-item> -->
        <el-form-item label="问题图片">
          <img
            v-for="(item, index) in dataForm.fileUrlList"
            :key="index"
            :src="$http.adornUrl('/file' + item)"
          />
        </el-form-item>
        <el-form-item label="所属区域">
          <el-select
            @change="handleQuery"
            v-model="dataForm.regionCode"
            clearable
          >
            <el-option
              v-for="dict in regionList"
              :key="dict.id"
              :label="dict.name"
              :value="dict.code"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="所属社区">
          <div>海悦社区</div>
        </el-form-item>
        <el-form-item label="属性网格">
         <div>海悦社区</div>
        </el-form-item> -->
        <el-form-item
          label="详细地址"
          prop="contentDesc"
          :error="contentDescError"
        >
          <div>金鸡湖大道</div>
        </el-form-item>
        <el-form-item label="时间" prop="contentDesc" :error="contentDescError">
          <el-date-picker
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择日期"
            style="width: 100%"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <div class="title-wd"><h4>流转记录</h4></div>
      <el-divider></el-divider>
      <questionLogList :dataLogList="dataList"></questionLogList>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
s
<script>
import questionLogList from '../component/questionLogList.vue'
export default {
  components: {
    questionLogList
  },
  data () {
    return {
      visible: false,
      dialogVisible: false,
      dataForm: {
        id: null,
        orderName: '',
        contentDesc: '',
        orderAddress: '',
        startTime: null,
        endTime: null,
        orderFileUrlList: [],
        region: ''
      },
      dataRule: {
        orderName: [
          { required: true, message: '工单名称不能为空', trigger: 'blur' }
        ],
        title: [
          { required: true, message: '问题标题不能为空', trigger: 'blur' }
        ],
        orderAddress: [
          { required: true, message: '工单详细地址不能为空', trigger: 'blur' }
        ],
        startTime: [
          { required: true, message: '工单开始时间不能为空', trigger: 'blur' }
        ],
        endTime: [
          { required: true, message: '工单结束时间不能为空', trigger: 'blur' }
        ]
      },
      orderNameError: null,
      contentDescError: null,
      orderAddressError: null,
      startTimeError: null,
      endTimeError: null,
      regionList: []
    }
  },
  created () {
    this.getRegionList()
  },
  methods: {
    init (id) {
      this.dataForm.id = id || null
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/position/position/question/detail`),
            method: 'get',
            params: this.$http.adornParams({ id: this.dataForm.id })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
            }
          })
        }
      })
    },
    getRegionList () {
      this.$http({
        url: this.$http.adornUrl('/position/area/top'),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.regionList = data.obj
        } else {
          this.regionList = []
        }
      })
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.clearErrors()
          this.$http({
            url: this.$http.adornUrl(`/position/order/edit`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    clearErrors () {
      this.orderNameError = null
      this.contentDescError = null
      this.orderAddressError = null
      this.startTimeError = null
      this.endTimeError = null
    }
  }
}
</script>

<style lang="scss"  scoped>
/deep/.forward-dialog {
  .el-select {
    width: 100%;
  }
  .el-date-editor {
    width: 100%;
  }
  .title-wd {
    width: 150px;
    text-align: right;
    padding-right: 12px;
    color: #606266;
  }
}
</style>

