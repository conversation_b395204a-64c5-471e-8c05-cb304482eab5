<template>
  <div>
    <h4>基本信息</h4>
    <el-divider></el-divider>
    <div class="detail">
      <div class="content">
        <!-- 基本信息/第一行数据 -->
        <div class="item-row">
          <div class="item-column">
            <div class="label">问题编号</div>
            <div class="text" v-text="obj.questionCode"></div>
          </div>
          <div class="item-column">
            <div class="label">所属街道</div>
            <div class="text" v-text="obj.regionName"></div>
          </div>
          <div class="item-column">
            <div class="label">所属社区</div>
            <div class="text" v-text="obj.communityName"></div>
          </div>
          <div class="item-column">
            <div class="label">所属网格</div>
            <div class="text" v-text="obj.gridName"></div>
          </div>
        </div>

        <!-- 基本信息/第二行数据 -->
        <div class="item-row">
          <div class="item-column">
            <div class="label">问题类别</div>
            <div
              class="text"
              v-text="obj.questionType ? obj.questionType.name : ''"
            ></div>
          </div>
          <div class="item-column">
            <div class="label">定位地点</div>
            <div class="text" v-text="obj.address"></div>
          </div>
          <div
            class="item-column"
            style="cursor: pointer"
            @click="openMap(obj.subLocation)"
          >
            <div class="label">
              上报时的经纬度<i class="el-icon-map-location icon-c"></i>
            </div>
            <div class="text" v-text="obj.subLocation"></div>
          </div>
          <div class="item-column">
            <div class="label">84坐标</div>
            <div class="text" v-text="obj.coordinate84"></div>
          </div>
        </div>

        <!-- 基本信息/第三行数据 -->
        <div class="item-row">
          <div class="item-column">
            <div class="label">苏州2000坐标</div>
            <div class="text" v-text="obj.coordinate84"></div>
          </div>
          <div class="item-column">
            <div class="label">提交人姓名</div>
            <div class="text" v-text="obj.subUserName"></div>
          </div>
          <div class="item-column">
            <div class="label">提交人电话</div>
            <div class="text" v-text="obj.subPhone"></div>
          </div>
          <div class="item-column">
            <div class="label">提交日期</div>
            <div class="text" v-text="obj.createDate"></div>
          </div>
        </div>

        <!-- 基本信息/第四行数据 -->
        <div class="item-row">
          <div class="item-column">
            <div class="label">责任局办</div>
            <div
              class="text"
              v-text="
                obj.questionTypeRelation
                  ? obj.questionTypeRelation.relationName
                  : ''
              "
            ></div>
          </div>
          <div class="item-column">
            <div class="label">处置时长（天）</div>
            <div
              class="text"
              v-text="obj.questionType ? obj.questionType.disposalDuration : ''"
            ></div>
          </div>
        </div>
      </div>
    </div>


    <h4>关联点位信息</h4>
    <el-divider></el-divider>
    <div class="detail">
      <div class="content">
        <div class="item-row">
          <div class="item-column">
            <div class="label">点位编号</div>
            <div
              class="text"
              v-text="
                obj.positionApplyInfo ? obj.positionApplyInfo.positionCode : ''
              "
            ></div>
          </div>
          <div class="item-column">
            <div class="label">点位名称</div>
            <div
              class="text"
              v-text="
                obj.positionApplyInfo ? obj.positionApplyInfo.positionName : ''
              "
            ></div>
          </div>
          <div class="item-column">
            <div class="label">点位类型</div>
            <div
              class="text"
              v-text="
                obj.positionApplyInfo ? obj.positionApplyInfo.typeName : ''
              "
            ></div>
          </div>
          <div
            class="item-column"
            style="cursor: pointer"
            @click="openMap(obj.chLocation)"
          >
            <div class="label">
              点位经纬度<i class="el-icon-map-location icon-c"></i>
            </div>
            <div class="text" v-text="obj.chLocation"></div>
          </div>
        </div>

        <div class="item-row">
          <div
            class="item-column"
            @click="openMap(obj.positionApplyInfo.positionMap)"
          >
            <div class="label">点位地址</div>
            <div class="text icon-c" style="cursor: pointer">
              {{
                obj.positionApplyInfo
                  ? obj.positionApplyInfo.positionAddress
                  : ""
              }}
              <span v-show="obj.positionApplyInfo"
              ><i class="el-icon-map-location icon-c"></i
              ></span>
            </div>
          </div>
        </div>
      </div>
    </div>


    <h4>问题信息</h4>
    <el-divider></el-divider>
    <div class="detail">
      <div class="content">
        <div class="item-row">
          <div class="item-column">
            <div class="label">问题描述</div>
            <div class="text" v-text="obj.title"></div>
          </div>
        </div>
        <div class="item-row">
          <div class="item-column">
            <div class="label">问题图片</div>
            <div class="text">
              <div
                class="question-img"
                v-for="(item, index) in obj.fileUrlList"
                :key="index"
              >
                <img :src="prefix + item" @click="openImage(prefix + item)" />
              </div>
              <el-dialog
                append-to-body
                :visible.sync="dialogImageDialogVisible"
              >
                <img width="100%" :src="dialogImageUrl1" alt="" />
              </el-dialog>
            </div>
          </div>
        </div>
        <div class="item-row">
          <div class="item-column">
            <div class="label">状态</div>
            <div class="text" v-text="obj.stateName"></div>
          </div>
        </div>
        <div class="item-row">
          <div class="item-column">
            <div class="label">派单单号</div>
            <div
              class="text icon-c"
              v-text="obj.order ? obj.order.orderCode : ''"
            ></div>
          </div>
          <div class="item-column">
            <div class="label">苏州工业园区智慧城市智能运行中心（IOC）</div>
            <div
              class="text icon-c"
              v-text="obj.iocInfo ? obj.iocInfo.id : ''"
            ></div>
          </div>
          <div class="item-column"></div>
          <div class="item-column"></div>
          <div class="item-column"></div>
        </div>
        <div class="item-row">
          <div class="item-column">
            <div class="label">问题回复</div>
            <div
              class="text"
              v-text="obj.reply ? obj.reply.replyDesc : ''"
            ></div>
          </div>
        </div>
        <div class="item-row" v-show="obj.replyUrlList">
          <div class="item-column">
            <div class="label">图片</div>
            <div class="text">
              <div
                class="question-img"
                v-for="(item, index) in obj.replyUrlList"
                :key="index"
              >
                <img :src="prefix + item" @click="openImage2(prefix + item)" />
              </div>
              <el-dialog
                append-to-body
                :visible.sync="dialogImageDialogVisible2"
              >
                <img width="100%" :src="dialogImageUrl2" alt="" />
              </el-dialog>
            </div>
          </div>
        </div>
        <div
          class="item-row"
          v-if="obj.questionExamine ? obj.questionExamine : ''"
        >
          <el-button
            type="primary"
            v-show="
              obj.questionExamine.state === 1 || obj.questionExamine.state === 8
            "
            @click="openDispatchDetail(obj.order.id)"
            v-if="obj.order"
            >派单详情</el-button
          >
          <el-button
            type="primary"
            v-show="
              obj.questionExamine.state === 1 || obj.questionExamine.state === 8
            "
            @click="openDispatchAdd(obj.id, obj.questionCode)"
            v-else
            >派单</el-button
          >
          <el-button
            type="primary"
            v-show="
              obj.questionExamine.state === 1 || obj.questionExamine.state === 8
            "
            @click="openProcessComplete(obj)"
            >处理完成</el-button
          >
          <el-button
            type="primary"
            v-show="
              obj.questionExamine.state === 1 || obj.questionExamine.state === 8
            "
            @click="openForwardAdd()"
            >转发</el-button
          >
          <el-button
            type="primary"
            v-show="
              obj.questionExamine.state === 1 || obj.questionExamine.state === 8
            "
            @click="sendEmail()"
            >短信邮件通知</el-button
          >
          <!-- <el-button
            type="primary"
            v-show="
              obj.questionExamine.state === 1 || obj.questionExamine.state === 8
            "
            @click="sendMessage()"
            >短信通知</el-button
          > -->
          <el-button
            type="primary"
            v-show="
              obj.questionExamine.state === 1 ||
              obj.questionExamine.state === 8 ||
              obj.questionExamine.state === 16 ||
              obj.questionExamine.state === 32
            "
            @click="collection()"
            >{{ obj.collected ? "取消收藏" : "收藏" }}</el-button
          >
          <el-button
            type="primary"
            v-show="obj.questionExamine.state === 1"
            @click="returnQuestion"
            >退回</el-button
          >
        </div>
      </div>
    </div>
    <div class="mg-100"></div>


    <h4>问题流转跟踪</h4>
    <el-divider></el-divider>
    <questionLogList :dataLogList="dataList"></questionLogList>
    <dispatchAdd
      v-if="dispatchAddVisible"
      ref="dispatchAdd"
      @refreshDataList="getDetail()"
    ></dispatchAdd>
    <dispatchDetail
      v-if="dispatchDetailVisible"
      ref="dispatchDetail"
    ></dispatchDetail>
    <processComplete
      v-if="processCompleteVisible"
      ref="processComplete"
      @refreshDataList="getDetail()"
    >
    </processComplete>
    <forwardAdd
      v-if="forwardAddVisible"
      ref="forwardAdd"
      @refreshDataList="getDetail()"
    ></forwardAdd>
    <forwardDetail
      v-if="forwardDetailVisible"
      ref="forwardDetail"
    ></forwardDetail>

    <!-- 引入地图 -->
    <mapDiaglog
      v-if="mapDataVisible"
      ref="mapDiaglog"
      @change="getMapData($event)"
    ></mapDiaglog>

    <!-- 邮件通知 -->
    <send-email
      v-if="sendEmailVisible"
      ref="sendEmail"
      @change="getMapData($event)"
    ></send-email>

    <!-- 发送短信 -->
    <send-message
      v-if="sendMessageVisible"
      ref="sendMessage"
      @change="getMapData($event)"
    ></send-message>
  </div>
</template>

<script>
import questionLogList from './component/questionLogList.vue'
import dispatchAdd from './dispatch/add.vue'
import dispatchDetail from './dispatch/detail.vue'
import processComplete from './replay/processComplete.vue'
import forwardAdd from './forward/forwardAdd.vue'
import forwardDetail from './forward/forwardDetail.vue'
import find from 'lodash/find'
import mapDiaglog from '../position/componet/mapDiaglogDetails.vue'
import SendEmail from '../component/sendEmail.vue'
import SendMessage from '../component/sendMessage.vue'
export default {
  components: {
    questionLogList,
    dispatchAdd,
    dispatchDetail,
    processComplete,
    forwardAdd,
    forwardDetail,
    mapDiaglog,
    SendEmail,
    SendMessage
  },
  data () {
    return {
      dialogImageDialogVisible: false,
      dialogImageDialogVisible2: false,
      dialogImageUrl1: '',
      dialogImageUrl2: '',
      dispatchAddVisible: false,
      dispatchDetailVisible: false,
      processCompleteVisible: false,
      forwardAddVisible: false,
      forwardDetailVisible: false,
      obj: {},
      id: '',
      states: [{
        value: 1,
        label: '待审核'
      }, {
        value: 2,
        label: '已撤回'
      }, {
        value: 4,
        label: '已退回'
      }, {
        value: 8,
        label: '待回复'
      }, {
        value: 16,
        label: '已回复'
      }, {
        value: 32,
        label: '已终审'
      }],
      dataList: [],
      mapDataVisible: false,
      mapData: {
        address: '',
        lng: '',
        lat: '',
        dialogVisible: true
      },
      sendEmailVisible: false,
      sendDataType: '1',
      sendMessageVisible: false
    }
  },
  activated () {
    this.id = this.$route.query.id
    this.prefix = window.SITE_CONFIG.baseUrl + '/file'
    this.getDetail()
  },
  methods: {
    // 获取数据列表
    getDetail () {
      let that = this
      this.$http({
        url: this.$http.adornUrl('/position/position/question/detail'),
        method: 'get',
        params: { id: this.id }
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.obj = data.obj
          let item = find(this.states, o => {
            return o.value === that.obj.questionExamine.state
          })
          this.obj.stateName = item.label
          this.dataList = this.obj.questionLogList ? this.obj.questionLogList : []
          const name = this.obj.questionTypeRelation
            ? this.obj.questionTypeRelation.relationName
            : ''
          // this.$notify({
          //   title: '指派成功',
          //   message: `该问题已指派到 ${name} 处置`,
          //   type: 'success'
          // })
        } else {
          this.obj = {}
        }
      })
    },
    openDispatchAdd (id, code) {
      this.dispatchAddVisible = true
      this.$nextTick(() => {
        this.$refs.dispatchAdd.init(id, code)
      })
    },
    openDispatchDetail (id) {
      this.dispatchDetailVisible = true
      this.$nextTick(() => {
        this.$refs.dispatchDetail.init(id)
      })
    },
    openProcessComplete (order) {
      this.processCompleteVisible = true
      this.$nextTick(() => {
        this.$refs.processComplete.init(order)
      })
    },
    openForwardAdd (id) {
      this.forwardAddVisible = true
      this.$nextTick(() => {
        this.$refs.forwardAdd.init(this.$route.query.id)
      })
    },
    openForwardDetail () {
      this.forwardDetailVisible = true
      this.$nextTick(() => {
        this.$refs.forwardDetail.init(this.$route.query.id)
      })
    },
    // 收藏/取消收藏
    collection () {
      var msg = this.obj.collected === 0 ? '收藏' : '取消收藏'
      this.$confirm(`确认是否${msg}？`, msg, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/position/position/question/collect'),
          method: 'get',
          params: {
            id: this.id,
            collected: this.obj.collected === 0 ? 1 : 0
          }
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.getDetail(this.id)
            this.$message({
              message: '收藏成功',
              type: 'success'
            })
          } else {
            this.$message(data.msg)
          }
        })
      })
    },
    // 退回
    returnQuestion () {
      this.$confirm(`确认是否退回？`, '退回操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/position/position/question/return'),
          method: 'get',
          params: {
            id: this.id
          }
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.getDetail(this.id)
            this.$message({
              message: '退回成功',
              type: 'success'
            })
          } else {
            this.$message(data.msg)
          }
        })
      })
    },
    // 图片预览
    openImage (url) {
      this.dialogImageDialogVisible = true
      this.dialogImageUrl1 = url
    },
    openImage2 (url) {
      this.dialogImageDialogVisible2 = true
      this.dialogImageUrl2 = url
    },
    openMap (data) {
      this.mapData.address = this.obj.address
      this.mapData.dialogVisible = false
      this.mapDataVisible = true
      if (data && data.indexOf(',') > 0) {
        this.mapData.lat = data.split(',')[1]
        this.mapData.lng = data.split(',')[0]
      }
      this.$nextTick(() => {
        this.$refs.mapDiaglog.visible = true
        this.$refs.mapDiaglog.initMap(this.mapData)
      })
    },
    async getMapData (value) {
    },
    sendEmail () {
      this.sendEmailVisible = true
      this.$nextTick(() => {
        this.$refs.sendEmail.visible = true
        this.$refs.sendEmail.title = '邮件通知'
        this.$refs.sendEmail.init(this.sendDataType, this.obj)
        this.$refs.sendEmail.dataForm.title = '【园区发布随手拍】随手拍问题："' + this.obj.title + '"，请尽快处理。'
        this.$refs.sendEmail.dataForm.personMessage = '【园区发布随手拍】随手拍问题："' + this.obj.title + '"，详见邮件，请尽快处理。'
      })
    },
    sendMessage () {
      this.sendMessageVisible = true
      this.$nextTick(() => {
        this.$refs.sendMessage.visible = true
        this.$refs.sendMessage.title = '短信通知'
        this.$refs.sendMessage.init(this.sendDataType, this.obj)
        this.$refs.sendMessage.dataForm.title = '【园区发布随手拍】随手拍问题:' + this.obj.title
      })
    }
  }
}
</script>


<style lang="scss" scoped>
.el-divider {
  margin: 8px 0;
  background: 0 0;
  border-top: 1px solid #e6ebf5;
}
.detail {
  .content {
    width: 100%;
    .item-row {
      line-height: 30px;
      margin-top: 25px;
      margin-bottom: 25px;
      display: flex;
      flex-direction: row;
      .item-column {
        margin-right: 20px;
        flex: 1;
        .label {
          color: #7f7f7fd8;
        }
        .text {
          display: flex;
          .question-img {
            margin-right: 20px;
            width: 148px;
            height: 148px;
            border-radius: 10px 10px;
            img {
              width: 100%;
              height: 100%;
            }
          }
        }
        &:last-child {
          margin-right: unset;
        }
      }
    }
  }
  .button {
    margin: 35px 0;
    text-align: center;
    .el-button {
      margin: 0 20px;
    }
  }
  .icon-c {
    color: #409eff;
  }
  .mg-100 {
    margin-bottom: 100px;
  }
}
</style>

