<template>
  <div>
    <el-dialog
      class="add-dialog"
      title="派单详情"
      :close-on-click-modal="false"
      :visible.sync="visible">
      <el-form :model="dataForm"  ref="dataForm" @keyup.enter.native="dataFormSubmit()"
               label-width="120px" :disabled="false" v-if="dataForm">
        <el-form-item label="单号" >
         <div v-text="dataForm.orderCode"></div>
        </el-form-item>
        <el-form-item label="任务名称" >
          <div v-text="dataForm.orderName"></div>
        </el-form-item>
        <el-form-item label="任务明细" >
          <div v-text="dataForm.contentDesc"></div>
        </el-form-item>
        <el-form-item label="任务图片">
          <div class="img-flex">
            <div class="question-img" v-for="(item,index ) in dataForm.orderFileUrlList" :key="index">
              <img :src="prefix + item">
            </div>
          </div>
        </el-form-item>
        <el-form-item label="所属区域">
          <div v-text="dataForm.regionName"></div>
        </el-form-item>
<!--        <el-form-item label="所属社区">-->
<!--          <div v-text="dataForm.communityName"></div>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="所属区域">-->
<!--          <div v-text="dataForm.gridName"></div>-->
<!--        </el-form-item>-->
        <el-form-item label="详细地址" >
          <div v-text="dataForm.orderAddress"></div>
        </el-form-item>
        <el-form-item label="开始时间" >
          <div v-text="dataForm.startTime"></div>
        </el-form-item>
        <el-form-item label="结束时间" >
        <div v-text="dataForm.endTime"></div>
      </el-form-item>
        <el-form-item label="参与人">
          <div v-text="dataForm.user"></div>
        </el-form-item>
        <el-form-item label="状态" >
          <div v-text="dataForm.stateName"></div>
        </el-form-item>
        <div class="title-wd"> <h4>处理结果</h4></div>
        <el-divider></el-divider>
        <el-form-item label="问题回复" >
          <div  v-text="dataForm.reply ? dataForm.reply.replyMsg : ''" ></div>
        </el-form-item>
        <el-form-item label="图片" >
          <div class="img-flex">
            <div class="question-img" v-for="(item,index ) in dataForm.replyFileUrlList" :key="index">
              <img :src="prefix + item">
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div class="title-wd"> <h4>流转记录</h4></div>
      <el-divider></el-divider>
      <questionLogList :dataLogList="dataList"></questionLogList>
    </el-dialog>
  </div>
</template>
s
<script>
  import questionLogList from '../component/questionLogList.vue'
  export default {
    components: {
      questionLogList
    },
    data () {
      return {
        selectUserDialogVisible: false,
        visible: false,
        dialogVisible: false,
        dataForm: {
          order: null
        },
        dataList: [],
        prefix: window.SITE_CONFIG.baseUrl + '/file'
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || null
        this.visible = true
        this.$nextTick(() => {
          // this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.getDetail()
          }
        })
      },
      getDetail () {
        this.$http({
          url: this.$http.adornUrl('/position/order/detail'),
          method: 'get',
          params: { id: this.dataForm.id }
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataForm = data.obj
            this.dataList = this.dataForm.orderLogList ? this.dataForm.orderLogList : []
            if (this.dataForm.relationList) {
              this.dataForm.user = this.dataForm.relationList.map(item => {
                return item.leader
              }).join(',')
            }
          } else {
            this.dataForm = {}
          }
        })
      }
    }
  }
</script>

<style lang="scss"  scoped>
  /deep/.add-dialog {
    .el-select {
      width: 100%;
    }
    .el-date-editor {
      width: 100%;
    }
    .el-divider{
      margin: 8px 0;
      background: 0 0;
      border-top: 1px solid #E6EBF5;
    }
    .title-wd {
      width: 120px;
      text-align:right;
      padding-right: 12px;
      color: #606266;
    }
    .text {
      margin-top: 10px;
      margin-bottom: 10px;
    }
    .img-flex {
      display: flex;
      .question-img {
        margin-right: 15px;
        width: 148px;
        height: 148px;
        border-radius: 10px 10px;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }

  }

</style>

