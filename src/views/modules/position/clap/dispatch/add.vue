<template>
  <div>
    <el-dialog
      class="add-dialog"
      title="新增派单"
      :close-on-click-modal="false"
      :before-close="resetDateForm"
      :visible.sync="visible"
    >
      <el-form
        :model="dataForm"
        :rules="dataRule"
        ref="dataForm"
        @keyup.enter.native="dataFormSubmit()"
        label-width="150px"
        :disabled="false"
      >
        <el-form-item label="任务名称" prop="orderName">
          <el-input
            v-model="dataForm.orderName"
            placeholder="请输入任务名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="任务明细" prop="contentDesc">
          <el-input
            type="textarea"
            :rows="2"
            v-model="dataForm.contentDesc"
            placeholder=""
          ></el-input>
        </el-form-item>
        <el-form-item label="任务图片">
          <el-upload
            :action="this.$http.adornUrl(`/file/oss/upload`)"
            :headers="myHeaders"
            :data="{ serverCode: this.serverCode, media: false }"
            list-type="picture-card"
            :file-list="fileUrlList"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
            :on-remove="handleRemove"
          >
            <i class="el-icon-plus"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="所属区域">
          <el-select
            @change="handleRegion($event)"
            v-model="dataForm.regionName"
            autocomplete
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in areaList"
              :key="item.code"
              :label="item.name"
              :value="{ value: item.code, label: item.name }"
            >
            </el-option>
          </el-select>
<!--                            <el-select v-model="dataForm.regionCode">-->
<!--                              <el-option label="区域一" value="shanghai"></el-option>-->
<!--                              <el-option label="区域二" value="beijing"></el-option>-->
<!--                            </el-select>-->
        </el-form-item>
                <el-form-item label="所属社区">
                          <el-select v-model="dataForm.communityName">
<!--                            <el-option label="社区一" value="shanghai"></el-option>-->
<!--                            <el-option label="社区二" value="beijing"></el-option>-->
                          </el-select>
                </el-form-item>
                <el-form-item label="所属网格">
                          <el-select v-model="dataForm.gridName">
<!--                            <el-option label="网格一" value="shanghai"></el-option>-->
<!--                            <el-option label="网格二" value="beijing"></el-option>-->
                          </el-select>
                </el-form-item>
        <el-form-item label="详细地址" prop="orderAddress">
          <el-input
            type="textarea"
            :rows="2"
            v-model="dataForm.orderAddress"
            placeholder="工单描述"
          ></el-input>
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime">
          <el-date-picker
            type="date"
            value-format="yyyy-MM-dd"
            v-model="dataForm.startTime"
            @change="timeChange"
            placeholder="选择开始时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime">
          <el-date-picker
            type="date"
            value-format="yyyy-MM-dd"
            v-model="dataForm.endTime"
            placeholder="选择结束时间"
            readonly
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="参与人" prop="user">
          <el-input
            v-model="dataForm.user"
            placeholder="请选择参与人"
            @focus="selectUser"
            :disabled="noEdit"
          >
            <!--  -->
            <i
              @click="selectUser"
              slot="suffix"
              style="margin-right: 5px"
              class="el-icon-arrow-down"
            ></i>
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="resetDateForm()">取消</el-button>
        <el-button
          type="primary"
          :disabled="submitDisable"
          @click="dataFormSubmit()"
          >派单</el-button
        >
      </span>
    </el-dialog>
    <!-- 选择部门，人员 -->
    <chooseExecutor
      v-if="selectUserDialogVisible"
      ref="selectUser"
    ></chooseExecutor>
  </div>
</template>

<script>
import chooseExecutor from '../component/choose-executor'
import Vue from 'vue'

export default {
  components: {
    chooseExecutor
  },
  data () {
    var validateUser = (rule, value, callback) => {
      if (!this.dataForm.user) {
        callback(new Error('参与人不能为空'))
      } else {
        callback()
      }
    }
    return {
      submitDisable: false,
      duration: null,
      selectUserDialogVisible: false,
      visible: false,
      dialogVisible: false,
      myHeaders: { Authorization: sessionStorage.getItem('Authorization') },
      serverCode: 'LocalServer',
      noEdit: false,
      id: '',
      relationCode: '',
      fileUrlList: [],
      dataForm: {
        orderName: '', // 工单名称
        contentDesc: '', // 任务明细
        fileUrlList: [], // 任务图片
        communityCode: '', // 所属社区
        communityName: '',
        regionCode: '', // 所属区域
        regionName: '',
        gridCode: '', // 所属网格
        gridName: '',
        orderAddress: '',
        orderLocation: '',
        startTime: '',
        endTime: '',
        relationType: 1,
        relationList: [],
        user: null,
        relationCode: ''
      },
      dataRule: {
        orderName: [
          { required: true, message: '工单名称不能为空', trigger: 'blur' }
        ],
        contentDesc: [
          { required: true, message: '任务明细不能为空', trigger: 'blur' }
        ],
        orderAddress: [
          { required: true, message: '详细地址不能为空', trigger: 'blur' }
        ],
        startTime: [
          { required: true, message: '开始时间不能为空', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '结束时间不能为空', trigger: 'change' }
        ],
        user: [
          { required: true, validator: validateUser, trigger: 'change' }
        ]
      },
      areaList: []
    }
  },
  methods: {
    init (id, code) {
      this.id = id || null
      this.visible = true
      this.submitDisable = false
      this.relationCode = code
      this.fileUrlList = []
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.id) {
          this.getDetail()
        } else {
        }
      })
    },
    // 获取当前日期
    getCurrentDate () {
      const now = new Date()
      let month = now.getMonth() + 1
      let day = now.getDate()
      if (month >= 1 && month <= 9) {
        month = '0' + month
      }
      if (day >= 0 && day <= 9) {
        day = '0' + day
      }
      return now.getFullYear() + '-' + month + '-' + day
    },
    // 开始时间与结束时间联动
    timeChange () {
      const start = new Date(this.dataForm.startTime)
      const end = new Date(start.setDate(start.getDate() + this.duration))
      let month = end.getMonth() + 1
      let day = end.getDate()
      if (month >= 1 && month <= 9) {
        month = '0' + month
      }
      if (day >= 0 && day <= 9) {
        day = '0' + day
      }
      this.dataForm.endTime = end.getFullYear() + '-' + month + '-' + day
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          // this.clearErrors()
          this.submitDisable = true
          this.dataForm.relationCode = this.relationCode
          this.$http({
            url: this.$http.adornUrl(`/position/order/create`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '派单成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.submitDisable = false
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              this.submitDisable = false
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.submitDisable = false
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    selectUser () {
      this.selectUserDialogVisible = true
      this.$nextTick(() => {
        this.$refs.selectUser.getOrgTreeList()
      })
    },
    // 获取数据列表
    getDetail () {
      this.$http({
        url: this.$http.adornUrl('/position/position/question/detail'),
        method: 'get',
        params: { id: this.id }
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.obj = data.obj
          this.duration = this.obj.questionType ? this.obj.questionType.disposalDuration : 0
          this.dataForm = {
            orderName: '', // 工单名称
            contentDesc: this.obj.title, // 任务明细
            fileUrlList: this.obj.fileUrlList ? this.obj.fileUrlList : [], // 任务图片
            communityCode: this.obj.communityCode, // 所属区域
            communityName: this.obj.communityName,
            regionCode: this.obj.regionCode, // 所属社区
            regionName: this.obj.regionName,
            gridCode: this.obj.gridCode, // 所属网格
            gridName: this.obj.gridName,
            orderAddress: this.obj.address,
            orderLocation: this.obj.subLocation,
            startTime: this.getCurrentDate(),
            endTime: '',
            relationType: 1,
            relationList: []
          }
          this.timeChange()
          this.fileUrlList = this.dataForm.fileUrlList.map(item => {
            return {
              name: this.$http.adornUrl('/file' + item),
              url: this.$http.adornUrl('/file' + item)
            }
          })
          this.getAreaType()
        } else {
          this.obj = {}
        }
      })
    },
    // 上传图片成功
    handleAvatarSuccess (res, file) {
      if (res.success) {
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 500
        })
        this.dataForm.fileUrlList.push(res.obj.path)
      } else {
        this.$message.error('上传失败')
      }
    },
    beforeAvatarUpload: function (file) {
      let isAccept = ['image/jpeg', 'image/png', 'image/bmp'].indexOf(file.type) !== -1
      let isLt2M = file.size / 1024 / 1024 < 2

      if (!isAccept) {
        this.$message.error('上传图片只能是图片!')
      }
      if (!isLt2M) {
        this.$message.error('上传文件大小不能超过 2MB!')
      }
      return isAccept && isLt2M
    },
    handleRemove (file, fileList) {
      this.dataForm.fileUrlList.splice(this.dataForm.fileUrlList.indexOf(file), 1)
    },
    // 属性区域列表
    getAreaType () {
      this.$http({
        url: this.$http.adornUrl('/position/area/top')
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.areaList = data.obj
        } else {
          this.areaList = {}
        }
      })
    },
    handleRegion (val) {
      const { value, label } = val
      this.dataForm.regionCode = value
      this.dataForm.regionName = label
    },
    resetDateForm () {
      this.fileUrlList = []
      this.visible = false
    }
  }
}
</script>

<style lang="scss"  scoped>
/deep/.add-dialog {
  .el-select {
    width: 100%;
  }
  .el-date-editor {
    width: 100%;
  }
}
</style>

