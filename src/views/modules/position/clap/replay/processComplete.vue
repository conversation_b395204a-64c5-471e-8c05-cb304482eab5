<template>
  <div>
    <el-dialog
      class="replay-dialog"
      title="处理完成"
      :close-on-click-modal="false"
      :visible.sync="visible"
    >
      <el-form
        :model="dataForm"
        :rules="dataRule"
        ref="dataForm"
        @keyup.enter.native="dataFormSubmit()"
        label-width="150px"
        :disabled="false"
      >
        <el-form-item label="快速选择回复" prop="replyMsg1">
          <el-select
            v-model="dataForm.replyMsg1"
            placeholder="请选择"
            @change="changeReplyMsg"
          >
            <el-option
              v-for="item in options"
              :key="item.code"
              :label="item.name"
              :value="item.name"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="回复" prop="replyDesc">
          <el-input
            type="textarea"
            :rows="3"
            v-model="dataForm.replyDesc"
            placeholder="请输入回复内容"
          ></el-input>
        </el-form-item>
        <el-form-item label="图片" prop="replyFileUrlList">
          <el-upload
            :action="this.$http.adornUrl(`/file/oss/upload`)"
            :headers="myHeaders"
            :data="{ serverCode: this.serverCode, media: false }"
            list-type="picture-card"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
            :on-remove="handleRemove"
          >
            <i class="el-icon-plus"></i>
          </el-upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="dataFormSubmit()">处理完成</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
export default {
  data () {
    return {
      visible: false,
      dialogVisible: false,
      dataForm: {
        id: null,
        examineId: null,
        replyMsg1: '',
        replyDesc: '',
        questionCode: '',
        replyFileUrlList: []
      },
      dataRule: {
        replyDesc: [
          { required: true, message: '回复内容不能为空', trigger: 'blur' }
        ]
      },
      options: [],
      myHeaders: { Authorization: sessionStorage.getItem('Authorization') },
      serverCode: 'LocalServer'
    }
  },
  methods: {
    init (order) {
      this.dataForm.examineId = order.questionExamine.id || null
      this.dataForm.questionCode = order.questionCode || null
      this.visible = true
      this.getOrderQuestionReply()
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        //   if (this.dataForm.id) {
        //     this.$http({
        //       url: this.$http.adornUrl(`/position/order/detail`),
        //       method: 'get',
        //       params: this.$http.adornParams({ id: this.dataForm.id })
        //     }).then(({data}) => {
        //       if (data && data.code === 0) {
        //         this.dataForm = data.obj
        //       }
        //     })
        //   }
      })
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        this.$confirm(`是否后台直接进行回复`, {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/position/position/question/reply`),
              method: 'post',
              data: this.$http.adornData(this.dataForm)
            }).then(({ data }) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else if (data && data.code === 303) {
                for (let it of data.obj) {
                  this[`${it.field}Error`] = it.message
                }
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      })
    },
    getOrderQuestionReply () {
      this.$http({
        url: this.$http.adornUrl('/admin/dict/parent'),
        method: 'get',
        params: { code: 'orderQuestionReply' }
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.options = data.obj || {}
        } else {
          this.obj = {}
        }
      })
    },
    changeReplyMsg (value) {
      this.dataForm.replyDesc = value
    },
    handleAvatarSuccess (res, file) {
      if (res.success) {
        // this.$message({
        //   message: '操作成功',
        //   type: 'success',
        //   duration: 500
        // })
        this.dataForm.replyFileUrlList.push(res.obj.path)
      } else {
        this.$message.error('上传失败')
      }
    },
    beforeAvatarUpload: function (file) {
      let isAccept = ['image/jpeg', 'image/png', 'image/bmp'].indexOf(file.type) !== -1
      let isLt2M = file.size / 1024 / 1024 < 5

      if (!isAccept) {
        this.$message.error('上传图片只能是图片!')
      }
      if (!isLt2M) {
        this.$message.error('上传文件大小不能超过 5MB!')
      }
      return isAccept && isLt2M
    },
    handleRemove (file, fileList) {
      if (this.dataForm.replyFileUrlList.indexOf(file) !== -1) {
        this.dataForm.replyFileUrlList.splice(this.dataForm.replyFileUrlList.indexOf(file), 1)
      }
    }
  }
}
</script>

<style lang="scss"  scoped>
/deep/.replay-dialog {
  .el-select {
    width: 100%;
  }
  .el-date-editor {
    width: 100%;
  }
}
</style>

