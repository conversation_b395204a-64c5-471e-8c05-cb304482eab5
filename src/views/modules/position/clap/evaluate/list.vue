<!-- 随手拍列表页面 -->
<template>
  <div class="mod-evaluate">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item label="问题标题">
        <el-input v-model="dataForm.title" placeholder="请输入问题标题" clearable></el-input>
      </el-form-item>
      <el-form-item label="问题状态">
        <el-select v-model="dataForm.state" clearable placeholder="请选择问题状态">
          <el-option
            v-for="item in states"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" @click="getDataList()">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column
        prop="questionCode"
        header-align="center"
        align="center"
        label="问题编号">
      </el-table-column>
      <el-table-column
        prop="title"
        header-align="center"
        align="center"
        label="问题标题">
      </el-table-column>
      <el-table-column
        header-align="center"
        align="center"
        label="评价星级">
        <template slot-scope="scope" >
        <el-rate v-model="scope.row.stars" disabled  :colors="color"></el-rate>
        </template>
      </el-table-column>
      <el-table-column
        prop="scoreMsg"
        header-align="center"
        align="center"
        label="评价内容">
      </el-table-column>
      <el-table-column
        prop="evaluator"
        header-align="center"
        align="center"
        label="评价人">
      </el-table-column>
      <el-table-column
        prop="createDate"
        header-align="center"
        align="center"
        label="评价时间">
      </el-table-column>
      <el-table-column
        prop="questionExamine"
        header-align="center"
        align="center"
        label="审核状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.state === 1" size="small" >待审核</el-tag>
          <el-tag v-if="scope.row.state === 2" size="small" type="warning">审核不通过</el-tag>
          <el-tag v-if="scope.row.state === 4" size="small" type="success">审核通过</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="openCheck(scope.row.id)" v-show="scope.row.state === 1">审核</el-button>
          <el-button type="text" @click="openEvaluateDetail(scope.row.id)" >详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 30, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <evaluateCheck v-if="evaluateCheckVisible" ref="evaluateCheck" @refreshDataList="getDataList"></evaluateCheck>
    <evaluateDetail v-if="evaluateDetailVisible" ref="evaluateDetail" ></evaluateDetail>
  </div>
</template>

<script>
  import evaluateCheck from './check.vue'
  import evaluateDetail from './detail.vue'
  export default {
    components: {
      evaluateCheck,
      evaluateDetail
    },
    data () {
      return {
        startTime: '',
        endTime: '',
        dataForm: {
          id: null,
          title: '',
          orderName: '',
          relationType: '',
          state: '',
          regionCode: '',
          leaderId: '',
          startTime: '',
          endTime: ''
        },
        color: ['#409EFF', '#409EFF', '#409EFF'],
        states: [{
          value: '1',
          label: '待审核'
        }, {
          value: '2',
          label: '审核不通过'
        }, {
          value: '4',
          label: '审核通过'
        }],
        updateVisible: false,
        dataList: [],
        pageIndex: 1,
        pageSize: 30,
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: [],
        evaluateCheckVisible: false,
        evaluateDetailVisible: false
      }
    },
    created () {
      this.getDataList()
    },
    // components: {
    //   Update
    // },
    methods: {
      // 获取数据列表
      getDataList () {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/position/question/score/page'),
          method: 'post',
          data: this.$http.adornData({
            'currentPage': this.pageIndex,
            'pageSize': this.pageSize,
            'title': this.dataForm.title,
            'state': this.dataForm.state
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataList = data.obj.records
            this.totalPage = data.obj.total
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 多选
      selectionChangeHandle (val) {
        this.dataListSelections = val
      },
      // 详情跳转页面
      openCheck (id) {
        // this.$router.push({name: 'clapDetail', query: {id: id}})
        this.$confirm(`审核通过后评价内容对外可见，审核不通过评价内容对外不可见`, '审核操作', {
          distinguishCancelAndClose: true,
          confirmButtonText: '审核通过',
          cancelButtonText: '审核不通过'
        }).then(() => {
           // 审核通过
          this.getPass(id)
        }).catch(action => {
          if (action === 'cancel') {
            this.openFailCheck(id)
          }
        })
      },
      // 审核不通过
      openFailCheck (id) {
        this.openEvaluateCheck(id)
        // this.$prompt('请输入审核结果（必填）', '审核结果', {
        //   confirmButtonText: '确定',
        //   cancelButtonText: '取消',
        //   inputPattern: /\S/,
        //   inputErrorMessage: '请输入审核结果'
        // }).then(({ value }) => {
        //   this.openEvaluateCheck(id)
        //   // this.$message({
        //   //   type: 'success',
        //   //   message: '你的邮箱是: ' + value
        //   // })
        // }).catch(() => {
        //   this.getDataList()
        // })
      },
      // 审核通过不通过
      getPass (id) {
        this.$http({
          url: this.$http.adornUrl('/position/question/score/pass'),
          method: 'get',
          params: {
            id: id
          }
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.getDataList()
            this.$message({
              message: '审核成功',
              type: 'success'
            })
          } else {
            this.$message(data.msg)
          }
        })
      },
      openEvaluateCheck (id) {
        this.evaluateCheckVisible = true
        this.$nextTick(() => {
          this.$refs.evaluateCheck.init(id)
        })
      },
      openEvaluateDetail (id) {
        this.evaluateDetailVisible = true
        this.$nextTick(() => {
          this.$refs.evaluateDetail.init(id)
        })
      }
    }
  }
</script>
<style lang="scss"  scoped>
</style>
