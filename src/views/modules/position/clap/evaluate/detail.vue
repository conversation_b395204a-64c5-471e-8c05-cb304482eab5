<template>
  <div>
    <el-dialog
      class="check-dialog"
      title="评价详情"
      :close-on-click-modal="false"
      :visible.sync="visible">
      <h4>评价</h4>
      <el-divider></el-divider>
      <div class="detail">
        <div class="content" >
          <div class="item-row">
            <div class="item-column">
              <div class="label">问题编号</div>
              <div class="text" v-text="obj.questionCode"></div>
            </div>
            <div class="item-column">
              <div class="label">问题标题</div>
              <div class="text" v-text="obj.title"></div>
            </div>
            <div class="item-column">
              <div class="label">评价星级</div>
              <el-rate v-model="obj.questionScore.stars" disabled  :colors="color" v-if="obj.questionScore"></el-rate>
            </div>
          </div>
          <div class="item-row">
            <div class="item-column">
              <div class="label">评价内容</div>
              <div class="text" v-text="obj.questionScore.scoreMsg"></div>
            </div>
            <div class="item-column">
              <div class="label">评价人</div>
              <div class="text" v-text="obj.questionScore.evaluator"></div>
            </div>
            <div class="item-column">
              <div class="label">评价时间</div>
              <div class="text" v-text="obj.questionScore.createDate"></div>
            </div>
          </div>
          <div class="item-row">
            <div class="item-column">
              <div class="label">审核状态</div>
              <div class="text" v-text="obj.stateName" >
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="mg-100"></div>
      <h4>问题流转跟踪</h4>
      <el-divider></el-divider>
      <questionLogList :dataLogList="dataList"></questionLogList>
    </el-dialog>
  </div>
</template>

<script>
  import questionLogList from '../component/questionLogList.vue'
  export default {
    components: {
      questionLogList
    },
    data () {
      return {
        visible: false,
        dialogVisible: false,
        dataForm: {
          id: null
        },
        dataList: [],
        obj: {},
        color: ['#409EFF', '#409EFF', '#409EFF'],
        states: [{
          value: '1',
          label: '待审核'
        }, {
          value: '2',
          label: '审核不通过'
        }, {
          value: '4',
          label: '审核通过'
        }]
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || null
        this.visible = true
        this.getDetail()
      },
      getDetail () {
        this.$http({
          url: this.$http.adornUrl('/position/question/score/getDetail'),
          method: 'get',
          params: { id: this.dataForm.id }
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.obj = data.obj
            let item = find(this.states, o => {
              return o.value === this.obj.questionScore.state
            })
            this.obj.stateName = item.label
            this.dataList = this.obj.questionLogList ? this.obj.questionLogList : []
          } else {
            this.obj = {}
          }
        })
      }
    }
  }
</script>

<style lang="scss"  scoped>
  .el-divider{
    margin: 8px 0;
    background: 0 0;
    border-top: 1px solid #E6EBF5;
  }
  .detail{
    .content{
      width: 100%;
      .item-row {
        line-height: 30px;
        margin-top: 25px;
        margin-bottom: 25px;
        display: flex;
        flex-direction: row;
        .item-column {
          margin-right: 20px;
          flex: 1;
          .label {
            color: #7f7f7fd8;
          }
          .text {
            .question-img {
              display: flex;
              margin-right: 20px;
              width: 150px;
              height: 100px;
              border-radius: 10px 10px;
              img {
                width: 100%;
                height: 100%;
              }
            }
          }
          &:last-child {
            margin-right: unset;
          }
        }
      }
    }
    .button{
      margin: 35px 0;
      text-align: center;
      .el-button{
        margin: 0 20px;
      }
    }
    .icon-c {
      color: #409eff;
    }
    .mg-100 {
      margin-bottom: 100px;
    }
  }

</style>

