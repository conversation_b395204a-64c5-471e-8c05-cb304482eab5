<template>
  <div>
    <el-dialog
      class="check-dialog"
      title="审核结果"
      :close-on-click-modal="false"
      :visible.sync="visible"
    >
      <el-form
        :model="dataForm"
        :rules="dataRule"
        ref="dataForm"
        @keyup.enter.native="dataFormSubmit()"
      >
        <el-form-item label="" prop="fbDesc">
          <el-input
            type="textarea"
            :rows="4"
            v-model="dataForm.fbDesc"
            placeholder="请输入审核结果（必填）"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="dataFormSubmit()">确认</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      dialogVisible: false,
      dataForm: {
        id: null,
        fbDesc: ''
      },
      dataRule: {
        fbDesc: [
          { required: true, message: '请输入审核结果', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init (id) {
      this.dataForm.id = id || null
      this.visible = true
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl('/position/question/score/notPass'),
            method: 'get',
            params: this.dataForm
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="scss"  scoped>
/deep/.add-dialog {
  .el-select {
    width: 100%;
  }
  .el-date-editor {
    width: 100%;
  }
}
</style>

