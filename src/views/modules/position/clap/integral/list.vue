<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="姓名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.volunteerName" placeholder="志愿者姓名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.volunteerId" placeholder="志愿者身份证号" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.mobilePhone" placeholder="手机号" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" @click="queryPage()">查询</el-button>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-download" type="success" @click="exportExcel()">导出</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
<!--      <el-table-column-->
<!--        type="selection"-->
<!--        header-align="center"-->
<!--        align="center"-->
<!--        width="50">-->
<!--      </el-table-column>-->
       <el-table-column
        prop="name"
        header-align="center"
        align="center"
        label="姓名">
      </el-table-column>
         <el-table-column
        prop="mobilePhone"
        header-align="center"
        align="center"
        label="手机号码">
      </el-table-column>
         <el-table-column
        prop="volunteerName"
        header-align="center"
        align="center"
        label="志愿者姓名">
      </el-table-column>
         <el-table-column
        prop="volunteerId"
        header-align="center"
        align="center"
        label="身份证号">
      </el-table-column>
         <el-table-column
        prop="questionCode"
        header-align="center"
        align="center"
        label="问题编号">
      </el-table-column>
         <el-table-column
        prop="questionName"
        header-align="center"
        align="center"
        label="问题标题">
      </el-table-column>
         <el-table-column
        prop="questionDate"
        header-align="center"
        align="center"
        label="问题时间">
      </el-table-column>
         <el-table-column
        prop="questionAddress"
        header-align="center"
        align="center"
        label="定位地址">
      </el-table-column>
         <el-table-column
        prop="point"
        header-align="center"
        align="center"
        label="积分数">
      </el-table-column>
         <el-table-column
        prop="createDate"
        header-align="center"
        align="center"
        label="记录时间">
      </el-table-column>
         <el-table-column
        prop="activityName"
        header-align="center"
        align="center"
        label="关联活动名称">
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 30, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
  </div>
</template>

<script>
  export default {
    data () {
      return {
        dataForm: {
          name: '',
          volunteerName: '',
          volunteerId: '',
          mobilePhone: ''
        },
        dataList: [],
        pageIndex: 1,
        pageSize: 30,
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: [],
        addOrUpdateVisible: false
      }
    },
    activated () {
      this.queryPage()
    },
    methods: {
      queryPage () {
        this.pageIndex = 1
        this.getDataList()
      },
      // 获取数据列表
      getDataList () {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/position/question/integral/pages'),
          method: 'post',
          data: this.$http.adornData({
            'currentPage': this.pageIndex,
            'pageSize': this.pageSize,
            'name': this.dataForm.name,
            'volunteerName': this.dataForm.volunteerName,
            'volunteerId': this.dataForm.volunteerId,
            'mobilePhone': this.dataForm.mobilePhone
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataList = data.obj.records
            this.totalPage = data.obj.total
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 多选
      selectionChangeHandle (val) {
        this.dataListSelections = val
      },
      // var ids = id ? [id] : this.dataListSelections.map(item => {return item.id})
      exportExcel () {
        this.$http({
          url: this.$http.adornUrl('/position/question/integral/excelExport'),
          method: 'post',
          responseType: 'arraybuffer',
          data: this.$http.adornData({
            'name': this.dataForm.name,
            'volunteerName': this.dataForm.volunteerName,
            'volunteerId': this.dataForm.volunteerId,
            'mobilePhone': this.dataForm.mobilePhone
          })
        }).then(({data}) => {
          if (data.code && data.code !== 0) {
            this.$message.error('导出失败')
          } else {
            let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
            window.location.href = URL.createObjectURL(blob)
            this.$message({
              type: 'success',
              message: '导出数据成功'
            })
          }
        })
      }
    }
  }
</script>
