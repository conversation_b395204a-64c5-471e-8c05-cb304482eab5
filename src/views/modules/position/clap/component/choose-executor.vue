<template>
  <el-dialog
    title="选择参与人"
    class="user-dialog"
    append-to-body
    :visible.sync="selectUserDialogVisible"
  >
    <el-row :gutter="12">
      <el-col :span="12">
        <el-card shadow="never">
          <div slot="header" class="clearfix">
            <span>已选</span
            ><span style="margin-left: 15px; color: blue">{{ userNum }}</span>
          </div>
          <div>
            <el-tree
              :data="orgList"
              :props="props"
              node-key="id"
              ref="dictTree"
              @current-change="currentChangeHandle"
              :default-expand-all="true"
              :highlight-current="true"
              :expand-on-click-node="false"
              style="height: 350px; overflow: auto"
            >
            </el-tree>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="never">
          <div slot="header" class="clearfix">
            <span>全部</span
            ><span style="margin-left: 15px; color: blue">{{ allNum }}</span>
          </div>
          <div style="height: 100%">
            <el-scrollbar style="height: 350px">
              <el-checkbox-group v-model="list" @change="getSelectUser">
                <el-checkbox
                  style="margin-top: 5px; display: block"
                  v-for="item in userList"
                  :label="item.id"
                  :key="item.id"
                  >{{ item.name }}</el-checkbox
                >
              </el-checkbox-group>
            </el-scrollbar>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitUser">确 定</el-button>
      <el-button @click="selectUserDialogVisible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      selectUserDialogVisible: false,
      // 已选
      userNum: 0,
      // 全部
      allNum: 0,
      orgList: [],
      props: {
        key: 'id',
        label: 'name',
        children: 'children'
      },
      userList: [],
      list: [],
      checkList: [],
      type: ''
    }
  },
  created () { },
  methods: {
    async getOrgTreeList () {
      await this.$http({
        url: this.$http.adornUrl('/admin/org/tree'),
        method: 'get',
        params: this.$http.adornParams({ level: 1 })
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.orgList = resp.data.obj
          this.selectUserDialogVisible = true
        }
      })
      this.getUser(this.orgList ? this.orgList[0].code : '')
    },
    currentChangeHandle (data, node) {
      this.getUser(data.code)
    },
    getUser (code) {
      if (code) {
        this.$http({
          url: this.$http.adornUrl('/admin/user/getUserByOrgCode'),
          method: 'get',
          params: { code: code }
        }).then((resp) => {
          if (resp.data && resp.data.code === 0) {
            this.userList = resp.data.obj
            this.allNum = this.userList.length
            this.getSelectUser()
          }
        })
      }
    },
    getSelectUser (value) {
      this.userNum = this.list.length
      this.userList.forEach(e => {
        var index = this.checkList.findIndex(item => {
          return item.id === e.id
        })
        if (this.list.indexOf(e.id) > -1 && index === -1) {
          this.checkList.push(e)
        }
        if (this.list.indexOf(e.id) === -1 && index > -1) {
          this.checkList.splice(index, 1)
        }
      })
    },
    submitUser () {
      this.selectUserDialogVisible = false
      if (this.list) {
        if (!this.type) {
          this.$parent.dataForm.relationList = this.checkList.length > 0 ? this.checkList.map((item) => {
            return {
              leaderId: item.id,
              leader: item.name,
              orgId: item.org_id,
              orgName: item.orgName
            }
          }) : []
          this.$parent.dataForm.user = this.checkList.length > 0 ? this.checkList.map((item) => {
            return item.name
          }).join(',') : ''
          this.$parent.$forceUpdate()
        } else {
          const userList = this.checkList.length > 0 ? this.checkList.map((item) => {
            return {
              id: item.id,
              email: item.email,
              name: item.name,
              orgId: item.orgId,
              orgName: item.orgName,
              mobile: item.mobile
            }
          }) : []
          if (this.type + '' === '1' || this.type + '' === '2') {
            // 随手拍收件人邮箱
            this.$emit('change', this.type, userList)
            // this.$parent.$forceUpdate()
          }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.user-dialog {
  /deep/.el-card {
    .el-scrollbar {
      .el-scrollbar__wrap {
        overflow: hidden scroll !important;
      }
    }
    .el-tree-node__content {
      height: 30px;
      line-height: 30px;
    }
    .el-checkbox-group {
      display: flex;
      flex-direction: column;
      .el-checkbox {
        line-height: 30px;
      }
    }
  }
}
</style>
