<template>
  <div>
    <el-table
      :data="dataList"
      border
      style="width: 100%;">
      <el-table-column
        prop="operator"
        header-align="center"
        align="center"
        label="用户">
      </el-table-column>
      <el-table-column
        prop="operateDes"
        header-align="center"
        align="center"
        label="操作记录">
      </el-table-column>
      <el-table-column
        prop="remark"
        header-align="center"
        align="center"
        label="说明">
      </el-table-column>
      <el-table-column
                        prop="createDate"
                        header-align="center"
                        align="center"
                        label="操作时间">
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  props: {
    dataLogList: Array
  },
  data () {
    return {
      dataList: []
    }
  },
  created () {
    this.dataList = this.dataLogList
  },
  watch: {
    dataLogList: function (val) {
      this.dataList = val
    }
  }
}
</script>

<style  scoped>
</style>
