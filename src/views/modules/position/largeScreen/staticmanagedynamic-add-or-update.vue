<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
      <el-row :gutter="20">
        <el-col :span="12">
           <el-form-item label="动态标题" prop="dynamicTitle" :error="dynamicTitleError">
                <el-input v-model="dataForm.dynamicTitle" placeholder="动态标题"></el-input>
            </el-form-item>
        </el-col>
            <el-col :span="12">
           <el-form-item label="序号" prop="sequence" :error="sequenceError">
                <el-input-number v-model="dataForm.sequence" controls-position="right" :min="0"></el-input-number>
            </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
           <el-form-item label="动态类型   1-随手拍 2-点位测评" prop="dynamicType" :error="dynamicTypeError">
                <el-input-number v-model="dataForm.dynamicType" controls-position="right" :min="0"></el-input-number>
            </el-form-item>
        </el-col>
            <el-col :span="12">
           <el-form-item label="区域编码值" prop="regionCode" :error="regionCodeError">
                <el-input v-model="dataForm.regionCode" placeholder="区域编码值"></el-input>
            </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: null,
          version: null,
          dynamicTitle: '',
          sequence: '',
          dynamicType: '',
          regionCode: ''
        },
        dataRule: {
          sequence: [
            { required: true, message: '序号不能为空', trigger: 'blur' }
          ],
          dynamicType: [
            { required: true, message: '动态类型   1-随手拍 2-点位测评不能为空', trigger: 'blur' }
          ],
          regionCode: [
            { required: true, message: '区域编码值不能为空', trigger: 'blur' }
          ]
        },
        dynamicTitleError: null,
        sequenceError: null,
        dynamicTypeError: null,
        regionCodeError: null
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || null
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/largeScreen/static/manage/dynamic`),
              method: 'get',
              params: this.$http.adornParams({ id: this.dataForm.id })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm = data.obj
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/largeScreen/static/manage/dynamic/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData(this.dataForm)
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else if (data && data.code === 303) {
                for (let it of data.obj) {
                  this[`${it.field}Error`] = it.message
                }
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
