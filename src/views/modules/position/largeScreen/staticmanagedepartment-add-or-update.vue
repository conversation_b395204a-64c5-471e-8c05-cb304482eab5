<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
      <el-row :gutter="20">
        <el-col :span="12">
           <el-form-item label="部门ID" prop="deptId" :error="deptIdError">
                <el-input v-model="dataForm.deptId" placeholder="部门ID"></el-input>
            </el-form-item>
        </el-col>
            <el-col :span="12">
           <el-form-item label="处置部门名称" prop="deptName" :error="deptNameError">
                <el-input v-model="dataForm.deptName" placeholder="处置部门名称"></el-input>
            </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
           <el-form-item label="处置的数量" prop="counts" :error="countsError">
                <el-input-number v-model="dataForm.counts" controls-position="right" :min="0"></el-input-number>
            </el-form-item>
        </el-col>
            <el-col :span="12">
           <el-form-item label="区域编码" prop="regionCode" :error="regionCodeError">
                <el-input v-model="dataForm.regionCode" placeholder="区域编码"></el-input>
            </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: null,
          version: null,
          deptId: '',
          deptName: '',
          counts: '',
          regionCode: ''
        },
        dataRule: {
          deptName: [
            { required: true, message: '处置部门名称不能为空', trigger: 'blur' }
          ],
          counts: [
            { required: true, message: '处置的数量不能为空', trigger: 'blur' }
          ],
          regionCode: [
            { required: true, message: '区域编码不能为空', trigger: 'blur' }
          ]
        },
        deptIdError: null,
        deptNameError: null,
        countsError: null,
        regionCodeError: null
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || null
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/largeScreen/static/manage/department`),
              method: 'get',
              params: this.$http.adornParams({ id: this.dataForm.id })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm = data.obj
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/largeScreen/static/manage/department/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData(this.dataForm)
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else if (data && data.code === 303) {
                for (let it of data.obj) {
                  this[`${it.field}Error`] = it.message
                }
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
