<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()" label-width="80px">
      <el-row :gutter="20">
        <el-col :span="12">
           <el-form-item label="年份" prop="year" :error="yearError">
                <el-input-number v-model="dataForm.year" controls-position="right" :min="0"></el-input-number>
            </el-form-item>
        </el-col>
            <el-col :span="12">
           <el-form-item label="一月份问题数量" prop="january" :error="januaryError">
                <el-input-number v-model="dataForm.january" controls-position="right" :min="0"></el-input-number>
            </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
           <el-form-item label="二月份问题数量" prop="february" :error="februaryError">
                <el-input-number v-model="dataForm.february" controls-position="right" :min="0"></el-input-number>
            </el-form-item>
        </el-col>
            <el-col :span="12">
           <el-form-item label="三月份问题数量" prop="march" :error="marchError">
                <el-input-number v-model="dataForm.march" controls-position="right" :min="0"></el-input-number>
            </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
           <el-form-item label="四月份问题数量" prop="april" :error="aprilError">
                <el-input-number v-model="dataForm.april" controls-position="right" :min="0"></el-input-number>
            </el-form-item>
        </el-col>
            <el-col :span="12">
           <el-form-item label="五月份问题数量" prop="may" :error="mayError">
                <el-input-number v-model="dataForm.may" controls-position="right" :min="0"></el-input-number>
            </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
           <el-form-item label="六月份问题数量" prop="june" :error="juneError">
                <el-input-number v-model="dataForm.june" controls-position="right" :min="0"></el-input-number>
            </el-form-item>
        </el-col>
            <el-col :span="12">
           <el-form-item label="七月份问题数量" prop="july" :error="julyError">
                <el-input-number v-model="dataForm.july" controls-position="right" :min="0"></el-input-number>
            </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
           <el-form-item label="八月份问题数量" prop="august" :error="augustError">
                <el-input-number v-model="dataForm.august" controls-position="right" :min="0"></el-input-number>
            </el-form-item>
        </el-col>
            <el-col :span="12">
           <el-form-item label="九月份问题数量" prop="september" :error="septemberError">
                <el-input-number v-model="dataForm.september" controls-position="right" :min="0"></el-input-number>
            </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
           <el-form-item label="十月份问题数量" prop="october" :error="octoberError">
                <el-input-number v-model="dataForm.october" controls-position="right" :min="0"></el-input-number>
            </el-form-item>
        </el-col>
            <el-col :span="12">
           <el-form-item label="十一月份问题数量" prop="november" :error="novemberError">
                <el-input-number v-model="dataForm.november" controls-position="right" :min="0"></el-input-number>
            </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
           <el-form-item label="十二月份问题数量" prop="december" :error="decemberError">
                <el-input-number v-model="dataForm.december" controls-position="right" :min="0"></el-input-number>
            </el-form-item>
        </el-col>
            <el-col :span="12">
           <el-form-item label="问题类型 1-随手拍 2-点位测评" prop="questionType" :error="questionTypeError">
                <el-input-number v-model="dataForm.questionType" controls-position="right" :min="0"></el-input-number>
            </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
           <el-form-item label="区域编码值" prop="regionCode" :error="regionCodeError">
                <el-input v-model="dataForm.regionCode" placeholder="区域编码值"></el-input>
            </el-form-item>
        </el-col>
        </el-row>
        </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: null,
          version: null,
          year: '',
          january: '',
          february: '',
          march: '',
          april: '',
          may: '',
          june: '',
          july: '',
          august: '',
          september: '',
          october: '',
          november: '',
          december: '',
          questionType: '',
          regionCode: ''
        },
        dataRule: {
          january: [
            { required: true, message: '一月份问题数量不能为空', trigger: 'blur' }
          ],
          february: [
            { required: true, message: '二月份问题数量不能为空', trigger: 'blur' }
          ],
          march: [
            { required: true, message: '三月份问题数量不能为空', trigger: 'blur' }
          ],
          april: [
            { required: true, message: '四月份问题数量不能为空', trigger: 'blur' }
          ],
          may: [
            { required: true, message: '五月份问题数量不能为空', trigger: 'blur' }
          ],
          june: [
            { required: true, message: '六月份问题数量不能为空', trigger: 'blur' }
          ],
          july: [
            { required: true, message: '七月份问题数量不能为空', trigger: 'blur' }
          ],
          august: [
            { required: true, message: '八月份问题数量不能为空', trigger: 'blur' }
          ],
          september: [
            { required: true, message: '九月份问题数量不能为空', trigger: 'blur' }
          ],
          october: [
            { required: true, message: '十月份问题数量不能为空', trigger: 'blur' }
          ],
          november: [
            { required: true, message: '十一月份问题数量不能为空', trigger: 'blur' }
          ],
          december: [
            { required: true, message: '十二月份问题数量不能为空', trigger: 'blur' }
          ],
          questionType: [
            { required: true, message: '问题类型 1-随手拍 2-点位测评不能为空', trigger: 'blur' }
          ],
          regionCode: [
            { required: true, message: '区域编码值不能为空', trigger: 'blur' }
          ]
        },
        yearError: null,
        januaryError: null,
        februaryError: null,
        marchError: null,
        aprilError: null,
        mayError: null,
        juneError: null,
        julyError: null,
        augustError: null,
        septemberError: null,
        octoberError: null,
        novemberError: null,
        decemberError: null,
        questionTypeError: null,
        regionCodeError: null
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || null
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            this.$http({
              url: this.$http.adornUrl(`/largeScreen/static/question/trend`),
              method: 'get',
              params: this.$http.adornParams({ id: this.dataForm.id })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.dataForm = data.obj
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/largeScreen/static/question/trend/${!this.dataForm.id ? 'save' : 'update'}`),
              method: 'post',
              data: this.$http.adornData(this.dataForm)
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else if (data && data.code === 303) {
                for (let it of data.obj) {
                  this[`${it.field}Error`] = it.message
                }
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
