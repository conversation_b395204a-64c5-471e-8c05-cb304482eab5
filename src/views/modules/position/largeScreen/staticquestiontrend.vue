<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item>
        <el-input v-model="dataForm.key" placeholder="参数名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="queryPage()">查询</el-button>
        <el-button v-if="isAuth('business:staticquestiontrend:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('business:staticquestiontrend:delete')" type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50">
      </el-table-column>
       <el-table-column
        prop="year"
        header-align="center"
        align="center"
        label="年份">
      </el-table-column>
         <el-table-column
        prop="january"
        header-align="center"
        align="center"
        label="一月份问题数量">
      </el-table-column>
         <el-table-column
        prop="february"
        header-align="center"
        align="center"
        label="二月份问题数量">
      </el-table-column>
         <el-table-column
        prop="march"
        header-align="center"
        align="center"
        label="三月份问题数量">
      </el-table-column>
         <el-table-column
        prop="april"
        header-align="center"
        align="center"
        label="四月份问题数量">
      </el-table-column>
         <el-table-column
        prop="may"
        header-align="center"
        align="center"
        label="五月份问题数量">
      </el-table-column>
         <el-table-column
        prop="june"
        header-align="center"
        align="center"
        label="六月份问题数量">
      </el-table-column>
         <el-table-column
        prop="july"
        header-align="center"
        align="center"
        label="七月份问题数量">
      </el-table-column>
         <el-table-column
        prop="august"
        header-align="center"
        align="center"
        label="八月份问题数量">
      </el-table-column>
         <el-table-column
        prop="september"
        header-align="center"
        align="center"
        label="九月份问题数量">
      </el-table-column>
         <el-table-column
        prop="october"
        header-align="center"
        align="center"
        label="十月份问题数量">
      </el-table-column>
         <el-table-column
        prop="november"
        header-align="center"
        align="center"
        label="十一月份问题数量">
      </el-table-column>
         <el-table-column
        prop="december"
        header-align="center"
        align="center"
        label="十二月份问题数量">
      </el-table-column>
         <el-table-column
        prop="questionType"
        header-align="center"
        align="center"
        label="问题类型 1-随手拍 2-点位测评">
      </el-table-column>
         <el-table-column
        prop="regionCode"
        header-align="center"
        align="center"
        label="区域编码值">
      </el-table-column>
        <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
  import AddOrUpdate from './staticquestiontrend-add-or-update'
  export default {
    data () {
      return {
        dataForm: {
          key: ''
        },
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: [],
        addOrUpdateVisible: false
      }
    },
    components: {
      AddOrUpdate
    },
    activated () {
      this.queryPage()
    },
    methods: {
      queryPage () {
        this.pageIndex = 1
        this.getDataList()
      },
      // 获取数据列表
      getDataList () {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/largeScreen/static/question/trend/pages'),
          method: 'post',
          data: this.$http.adornData({
            'currentPage': this.pageIndex,
            'pageSize': this.pageSize,
            'key': this.dataForm.key
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataList = data.obj.records
            this.totalPage = data.obj.total
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 多选
      selectionChangeHandle (val) {
        this.dataListSelections = val
      },
      // 新增 / 修改
      addOrUpdateHandle (id) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(id)
        })
      },
      // 删除
      deleteHandle (id) {
        var ids = id ? [id] : this.dataListSelections.map(item => {
          return item.id
        })
        this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl('/largeScreen/static/question/trend/removeByIds'),
            method: 'get',
            params: this.$http.adornParams({
              'ids': ids.join(',')
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        })
      }
    }
  }
</script>
