<template>
  <el-form-item label="生码拓展区域" prop="areaDescDoList">
    <div class="flex-c pr-100">
<!--      <div class="txt-c" style="width:110px;">节点序号</div>-->
      <div class="txt-c" style="width:150px;padding-right: 20px">节点（key值）</div>
      <div class="txt-c" style="width:150px;">节点（value值）</div>
<!--      <div class="txt-c" style="width:110px;">长度</div>-->
<!--      <div class="txt-c" style="width:110px;">中文名</div>-->
<!--      <div class="txt-c" style="width:110px;">是否变化</div>-->
      <div class="txt-c" style="width:60px;" />
    </div>
    <div
      v-for="(item, index) in expandList"
      :key="index"
      class="flex-c pr-100"
    >
<!--      <el-input-->
<!--        v-model="item.number"-->
<!--        :placeholder="item.number=index+1"-->
<!--        type="text"-->
<!--        style="width:110px;"-->
<!--        disabled-->
<!--      />-->
      <el-input v-model="item.key" type="text" style="width:150px;padding-right: 20px;" />
      <el-input v-model="item.value" type="text" style="width:150px;" />
<!--      <el-input v-model="item.areaLength" type="number" style="width:110px;" />-->
<!--      <el-input v-model="item.areaName" type="text" style="width:110px;" />-->
<!--      <el-select v-model="item.areaChange" style="width:110px;">-->
<!--        <el-option-->
<!--          v-for="itemOption in yesOrNo"-->
<!--          :key="itemOption.key"-->
<!--          :label="itemOption.value"-->
<!--          :value="itemOption.key"-->
<!--        />-->
<!--      </el-select>-->
      <div v-if="index==0" style="width:60px;" @click="addQrcodeArea()">添加</div>
      <div v-if="index!=0" style="width:60px;" @click="delQrcodeArea(index)">删除</div>
    </div>
  </el-form-item>

</template>

<script>
export default {
  mounted () {
    this.getExpandData()
  },
  data () {
    return {
      expandList: [{
        // 自定义区域
        // number: '',
        key: '',
        value: ''
      }
      ]
    }
  },
  methods: {
    init () {
      this.expandList = [{
        // 自定义区域
        // number: '',
        key: '',
        value: ''
      }
      ]
    },
    // 添加自定义区域
    addQrcodeArea () {
      const obj = {
        key: '',
        value: ''
      }
      this.expandList.push(obj)
      this.getExpandData()
    },
    // 删除自定义区域
    delQrcodeArea (index) {
      this.expandList.splice(index, 1)
      this.getExpandData()
    },
    getExpandData () {
      this.$emit('expand', this.expandList)
    }
  }
}
</script>

<style scoped>
.flex-c{
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 30px;
  margin-bottom: 13px;
}
.el-form-item__label{
  color: #333;
  font-weight: 500;
// margin-right:20px;
  width: 120px;
}
.pr-100 {
  padding-right: 200px;
}
.txt-c {
  text-align: center;
}

</style>
