<!-- 工单详情页面 -->
<template>
  <div>
    <div>详情</div>
    <el-divider></el-divider>
    <br />
    <el-row class="count">
      <el-col :span="8"><div>二维码编号</div></el-col>
      <el-col :span="8"><div>二维码类型</div></el-col>
<!--      <el-col :span="8"><div>生成时间</div></el-col>-->
    </el-row>
    <el-row>
      <el-col :span="8"
        ><div class="grid-content bg-purple-light">
          {{ dataForm.code }}
        </div></el-col
      >
      <el-col :span="8"
        ><div class="grid-content bg-purple-light">
          {{ dataForm.codeTypeName }}
        </div></el-col
      >

<!--      <el-col :span="8"-->
<!--        ><div class="grid-content bg-purple-light">-->
<!--          {{ dataForm.classifyName }}-->
<!--        </div></el-col-->
<!--      >-->
    </el-row>
    <el-row class="count">
      <el-col :span="8"><div>创建人</div></el-col>
      <el-col :span="8"><div>生成时间</div></el-col>
      <el-col :span="8"><div>二维码图片</div></el-col>
    </el-row>
    <el-row>
      <el-col :span="8"
        ><div class="grid-content bg-purple-light">
          {{ dataForm.creatorName }}
        </div></el-col
      >
      <el-col :span="8"
      ><div class="grid-content bg-purple-light">
        {{ dataForm.createDate }}
      </div></el-col
      >
      <el-col :span="8"
        ><div class="grid-content bg-purple-light">
          <span class="getimage" @click="getImage(dataForm.filePath)"
            >下载</span
          >
        </div></el-col
      >
    </el-row>
    <el-row class="count" v-if="dataForm.categoryParentId">
      <el-col :span="8"><div>测评父级分类名称</div></el-col>
      <el-col :span="8"><div>测评子级分类名称</div></el-col>
    </el-row>
    <el-row v-if="dataForm.categoryParentId">
      <el-col :span="8"
      ><div class="grid-content bg-purple-light">
        {{ dataForm.categoryParentName }}
      </div></el-col
      >
      <el-col :span="8"
      ><div class="grid-content bg-purple-light">
        {{ dataForm.categoryName }}
      </div></el-col
      >
    </el-row>

    <el-row class="count" v-if="!dataForm.categoryParentId">
      <el-col :span="8"><div>点位分类名称</div></el-col>
      <el-col :span="8"><div>点位类型名称</div></el-col>
    </el-row>
    <el-row v-if="!dataForm.categoryParentId">
      <el-col :span="8"
      ><div class="grid-content bg-purple-light">
        {{ dataForm.classifyName }}
      </div></el-col
      >
      <el-col :span="8"
      ><div class="grid-content bg-purple-light">
        {{ dataForm.typeName }}
      </div></el-col
      >
    </el-row>


    <el-row class="count" v-if="dataForm.positionCode">
      <el-col :span="8"><div>绑定的点位编码</div></el-col>
      <el-col :span="8"><div>状态</div></el-col>
    </el-row>
    <el-row v-if="dataForm.positionCode">
      <el-col :span="8"
      ><div class="grid-content bg-purple-light">
        {{ dataForm.positionCode }}
      </div></el-col
      >
      <el-col :span="8"
        ><div class="grid-content bg-purple-light">
          {{ dataForm.positionCode === null ? "未绑定" : "已绑定" }}
        </div></el-col
      >
    </el-row>
    <div v-if="dataList">绑定信息如下</div>
    <el-divider v-if="dataList"></el-divider>
    <div v-for="item in dataList" key="item">
      <h4>{{ item.key }} : {{ item.value }}</h4>
    </div>
  </div>
</template>
<style scoped>
.count {
  color: #767676;
}
</style>
<script>
export default {
  activated () {
    this.getDetail()
  },
  data () {
    return {
      dataForm: {},
      dataList: []
    }
  },
  methods: {
    getDetail () {
      this.$http({
        url: this.$http.adornUrl(`/position/qrCode/info/getDetail`),
        method: 'get',
        params: this.$http.adornParams({ id: this.$route.query.id })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataForm = data.obj
          // eslint-disable-next-line no-eval
          this.dataList = eval('(' + data.obj.expand + ')')
        }
      })
    },
    getImage (url) {
      let urls = `${window.SITE_CONFIG['baseUrl']}/file${url}`
      this.downloadPicture(urls, '二维码.jpg')
    },
    downloadPicture (imgSrc, name) {
      if (imgSrc) {
        const image = new Image()
        // 解决跨域 Canvas 污染问题
        image.setAttribute('crossOrigin', 'anonymous')
        image.src = imgSrc
        image.onload = () => {
          this.$message({
            message: '下载成功',
            type: 'success',
            duration: 500
          })
          const canvas = document.createElement('canvas')
          canvas.width = image.width
          canvas.height = image.height
          const context = canvas.getContext('2d')
          context.drawImage(image, 0, 0, image.width, image.height)
          canvas.toBlob((blob) => {
            const url = URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.download = name || 'photo'
            a.href = url
            a.click()
            a.remove()
            URL.revokeObjectURL(url)
          })
        }
      } else {
        this.$message.error('下载失败')
      }
    }
  }
}
</script>

<style scoped>
.el-divider {
  margin: 8px 0;
  background: 0 0;
  border-top: 1px solid #e6ebf5;
}
.getimage {
  color: #1890ff;
}
.getimage:hover {
  cursor: pointer;
}
.el-row {
  margin-bottom: 40px;
  &:last-child {
    margin-bottom: 20px;
  }
}
.el-col {
  border-radius: 4px;
}
.el-image {
  margin-left: 10px;
}
.grid-content {
  /* width: 260px; */
  padding-right: 50px;
  border-radius: 4px;
  min-height: 36px;
  word-break: break-all;
}
</style>
