<template>
  <el-dialog title="新增" :visible.sync="dialogVisible" :close-on-click-modal="false" width="40%">
    <el-form :model="dataForm" ref="dataForm" label-width="120px" style="width: 100%"   :rules="dataRule" @keyup.enter.native="dataFormSubmit()">
      <el-row>
        <el-col :span="24">
<!--          <label for="name" class="el-form-item__label">二维码类型</label>-->
          <el-form-item label="二维码类型">
            <el-select class="elSelect"
              @change="selectQrType"
              v-model="dataForm.codeType"
              placeholder="测评分类"
            >
              <el-option
                v-for="item in qrTypeList"
                :key="item.id"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
<!--      <template >-->
      <el-row v-if="isDianWei == 'dianweiqrcode'" key="1">
<!--        <el-row v-if="isDianWei == 'dianweiqrcode'" key="1">-->
          <el-col :span="24">
<!--            <label for="name" class="el-form-item__label">二维码点位类型</label>-->
            <el-form-item prop="typeCode"  required label="点位类型">
<!--              <el-dict v-model="dataForm.typeCode" :code="value" @click="typeCodeChange">-->
<!--              </el-dict>-->
              <el-select class="elSelect" @change="typeCodeChange"
                         v-model="dataForm.typeCode"
                         placeholder="点位类型"
                         clearable
              >
                <el-option
                  v-for="dict in typeCodeList"
                  :key="dict.id"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row >
        <el-row  v-if="isDianWei === 'dianweiqrcode'" key="2">
          <el-col :span="24">
<!--            <label for="name" class="el-form-item__label">二维码点位分类</label>-->
            <el-form-item prop="classfiyCode" label="点位分类">
<!--              <el-dict v-model="dataForm.classfiyCode" :code="value2">-->
<!--              </el-dict>-->
              <el-select class="elSelect" @change="classfiyCodeChange"
                         v-model="dataForm.classfiyCode"
                         placeholder="点位分类"
                         clearable
              >
                <el-option
                  v-for="dict in classfiyCodeList"
                  :key="dict.id"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
<!--      </template>-->
<!--      <template v-else>-->
<!--      <template v-else key="2">-->
        <el-row v-if="isDianWei === 'cepingqrcode'" key="3">
          <el-col :span="24">
<!--            <label for="name" class="el-form-item__label">测评卡分类</label>-->
            <el-form-item prop="categoryParentId" label="测评卡分类">
              <el-select class="elSelect"
                @change="getCategoryList(dataForm.categoryParentId)"
                v-model="dataForm.categoryParentId"
                placeholder="请选择测评卡分类"
                clearable
              >
                <el-option
                  v-for="dict in typeList"
                  :key="dict.id"
                  :label="dict.categoryName"
                  :value="dict.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="isDianWei === 'cepingqrcode'" key="4">
          <el-col :span="24">
<!--            <label for="name" class="el-form-item__label">测评卡子分类</label>-->
            <el-form-item prop="categoryId" :error="categoryIdError" label="测评卡子分类">
              <el-select class="elSelect" @change="categoryIdChange"
                v-model="dataForm.categoryId"
                placeholder="请选择测评卡子分类"
                clearable
              >
                <el-option
                  v-for="dict in categoryList"
                  :key="dict.id"
                  :label="dict.categoryName"
                  :value="dict.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
<!--      </template>-->
      <el-row>
        <el-col :span="24">
<!--          <label for="name" class="el-form-item__label">数量</label>-->
          <el-form-item label="数量">
            <el-input-number v-model="dataForm.num" :min="1" :max="100" label="描述文字"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 拓展信息 -->
      <expand ref="expandMain" @expand="getExpandList"/>
      <el-row>
        <el-col :span="24">
          <el-form-item>
            <el-button type="primary" @click="dataFormSubmit()">生成图片并下载</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-dialog>
</template>
<style scoped>
</style>
<script>
  import elDict from '@/components/el-dict/index'
  import Expand from './expand'
  export default {
    data () {
      return {
        dialogVisible: false,
        dianWeiCode: 'dianweiqrcode',
        expand: [],
        dataForm: {
          typeCode: '',
          classfiyCode: '',
          num: 1,
          codeType: '',
          categoryParentId: '',
          categoryId: '',
          prefixName: '',
          suffixName: ''
        },
        value: 'QRCodeTypeCode',
        value2: 'QRCodeClassificationCode',
        qrType: 'QRCodeTypeOne',
        qrTypeList: [],
        typeCodeList: [],
        classfiyCodeList: [],
        isDianWei: 'dianweiqrcode',
        typeList: [],
        categoryList: [],
        queryParams: {
          currentPage: 1,
          pageSize: 30,
          flag: true,
          codeStatus: 'eva_submit'
        },
        dataRule: {
          typeCode: [
            { required: true, message: '请选择点位类型', trigger: 'change' }
          ],
          classfiyCode: [
            { required: true, message: '请选择点位分类', trigger: 'change' }
          ],
          categoryParentId: [
            { required: true, message: '请选择测评卡分类', trigger: 'change' }
          ],
          categoryId: [
            { required: true, message: '请选择测评卡子分类', trigger: 'change' }
          ]
        },
        categoryIdError: null
      }
    },
    components: {
      Expand,
      elDict
    },
    methods: {
      init () {
        // this.$nextTick(() => {
        //   this.$refs['dataForm'].resetFields()
        // })
        this.dialogVisible = true
        this.dataForm.typeCode = ''
        this.dataForm.classfiyCode = ''
        this.dataForm.categoryParentId = ''
        this.dataForm.categoryId = ''
        this.dataForm.num = 1
        this.dataForm.prefixName = ''
        this.dataForm.suffixName = ''
        this.getQrtTpeList(this.qrType)
        this.getTypeCodeList(this.value)
        this.getClassificationCodeList(this.value2)
        this.getTypeList()
        this.$nextTick(e => {
          this.$refs.expandMain.init()
        })
      },
      getExpandList (value) {
        // eslint-disable-next-line no-undef
        this.expand = value
      },
      checkExpandList (arr) {
        for (let i = 0; i < arr.length; i++) {
          if (arr[i].key === '') {
            arr.splice(i, 1)
            i = i - 1
          }
        }
      },
      // 参数校验
      checkDateForm () {
        if (this.dataForm.codeType === this.dianWeiCode) {
          this.dataForm.categoryParentId = ''
          this.dataForm.categoryId = ''
        } else {
          this.dataForm.classfiyCode = ''
          this.dataForm.typeCode = ''
        }
      },
      dataFormSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.checkExpandList(this.expand)
            this.checkDateForm()
            this.$http({
              url: this.$http.adornUrl(`/position/qrCode/info/create`),
              method: 'post',
              responseType: 'blob',
              data: this.$http.adornData({
                'classifyCode': this.dataForm.classfiyCode,
                'num': this.dataForm.num,
                'typeCode': this.dataForm.typeCode,
                'expand': JSON.stringify(this.expand).toString(),
                'categoryParentId': this.dataForm.categoryParentId,
                'categoryId': this.dataForm.categoryId,
                'codeType': this.dataForm.codeType,
                'prefixName': this.dataForm.prefixName,
                'suffixName': this.dataForm.suffixName
              })
            }).then(({data}) => {
              if (data.code && data.code !== 0) {
                this.$message.error('导出失败')
                this.dialogVisible = false
                this.$parent.getDataList()
              } else {
                const blob = new Blob([data])
                const fileName = this.dataForm.num > 1 ? '二维码.zip' : '二维码.jpg'
                const dlink = document.createElement('a')
                if ('download' in document.createElement('a')) { // 非IE下载
                  const dlink = document.createElement('a')
                  dlink.download = fileName
                  dlink.style.display = 'none'
                  dlink.href = URL.createObjectURL(blob)
                  document.body.appendChild(dlink)
                  dlink.click()
                  URL.revokeObjectURL(dlink.href) // 释放URL 对象
                  document.body.removeChild(dlink)
                  this.dialogVisible = false
                  // this.dataForm.orderCode = ''
                  // this.dataForm.classfiyCode = ''
                  // this.dataForm.num = 1
                  this.$parent.getDataList()
                } else { // IE10+下载
                  navigator.msSaveBlob(blob, fileName)
                  this.dialogVisible = false
                  // this.dataForm.orderCode = ''
                  // this.dataForm.classfiyCode = ''
                  // this.dataForm.num = 1
                  this.$parent.getDataList()
                }
              }
            })
          }
        })
      },
      getQrtTpeList (code) {
        this.$http({
          url: this.$http.adornUrl('/admin/dict/parent'),
          method: 'get',
          params: { code: code }
        }).then((resp) => {
          if (resp.data && resp.data.code === 0) {
            this.qrTypeList = resp.data.obj
            if (this.qrTypeList) {
              this.qrTypeList.forEach(e => {
                if (e.code === 'dianweiqrcode') {
                  this.dataForm.codeType = e.code
                  this.isDianWei = e.code
                }
              })
            }
          } else {
            this.qrTypeList = []
          }
        })
      },
      getTypeCodeList (code) {
        this.$http({
          url: this.$http.adornUrl('/admin/dict/parent'),
          method: 'get',
          params: { code: code }
        }).then((resp) => {
          if (resp.data && resp.data.code === 0) {
            this.typeCodeList = resp.data.obj
          } else {
            this.typeCodeList = []
          }
        })
      },
      getClassificationCodeList (code) {
        this.$http({
          url: this.$http.adornUrl('/admin/dict/parent'),
          method: 'get',
          params: { code: code }
        }).then((resp) => {
          if (resp.data && resp.data.code === 0) {
            this.classfiyCodeList = resp.data.obj
          } else {
            this.classfiyCodeList = []
          }
        })
      },
      selectQrType (value) {
        this.isDianWei = value
      },
      getTypeList () {
        this.$http({
          url: this.$http.adornUrl('/evaluate/cardCategory/getType'),
          method: 'get',
          params: this.queryParams
        }).then((resp) => {
          if (resp.data && resp.data.code === 0) {
            this.typeList = resp.data.obj
          } else {
            this.typeList = []
          }
        })
      },
      getCategoryList (parentId) {
        this.dataForm.categoryId = ''
        this.typeList.forEach(e => {
          if (e.id.toString() === parentId) {
            this.dataForm.prefixName = e.categoryName
          }
        })
        this.$http({
          url: this.$http.adornUrl('/evaluate/cardCategory/getListByParentId'),
          method: 'get',
          params: { parentId: parentId }
        }).then((resp) => {
          if (resp.data && resp.data.code === 0) {
            this.categoryList = resp.data.obj
          } else {
            this.categoryList = []
          }
        })
      },
      typeCodeChange (value) {
        this.typeCodeList.forEach(e => {
          if (e.code === value) {
            this.dataForm.prefixName = e.name
          }
        })
      },
      classfiyCodeChange (value) {
        this.classfiyCodeList.forEach(e => {
          if (e.code === value) {
            this.dataForm.suffixName = e.name
          }
        })
      },
      categoryIdChange (id) {
        this.categoryList.forEach(e => {
          if (e.id.toString() === id) {
            this.dataForm.suffixName = e.categoryName
          }
        })
      }
    }
  }
</script>
<style lang="scss" scoped>
  .elSelect {
    width: 100% !important;
  }
    ::v-deep .el-select {
      width: 100% !important;
    }
  </style>
