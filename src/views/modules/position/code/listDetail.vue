<!-- 二维码列表页面 -->
<template>
  <div class="code-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <label for="name" class="el-form-item__label">二维码编号</label>
      <el-form-item>
        <el-input
          clearable
          v-model="dataForm.code"
          placeholder="请输入二维码编号"
        >
        </el-input>
      </el-form-item>
      <label for="name" class="el-form-item__label">绑定点位状态</label>
      <el-form-item>
        <el-select
          v-model="dataForm.state"
          clearable
          placeholder="请您选择绑定状态"
        >
          <el-option
            v-for="item in channelList"
            :label="item.name"
            :value="item.isShow"
            :key="item.name"
          ></el-option>
        </el-select>
      </el-form-item>
      <label for="name" class="el-form-item__label">绑定点位编码</label>
      <el-form-item>
        <el-input
          clearable
          v-model="dataForm.positionCode"
          placeholder="请输入绑定点位编码"
        >
        </el-input>
      </el-form-item>

      <label for="name" class="el-form-item__label">点位分类</label>
      <el-form-item>
        <el-dict v-model="dataForm.classifyCode" :code="value2"> </el-dict>
      </el-form-item>

      <label for="name" class="el-form-item__label">点位类型</label>
      <el-form-item>
        <el-dict v-model="dataForm.typeCode" :code="value"> </el-dict>
      </el-form-item>

      <el-form-item>
        <!-- <el-button>查询</el-button> -->
        <el-button icon="el-icon-search" @click="getDataList()">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border style="width: 100%">
      <!--      <el-table-column-->
      <!--        type="selection"-->
      <!--        header-align="center"-->
      <!--        align="center"-->
      <!--        width="50">-->
      <!--      </el-table-column>-->
      <el-table-column
        width="50"
        type="index"
        :index="indexAdd"
        align="center"
        header-align="center"
        label="序号"
      >
      </el-table-column>
      <el-table-column
        prop="code"
        width="250"
        align="center"
        label="二维码编号"
      >
      </el-table-column>
      <el-table-column prop="codeTypeName" align="center" label="二维码类型">
      </el-table-column>
      <el-table-column prop="categoryParentName" align="center" label="测评父类名称">
      </el-table-column>
      <el-table-column prop="categoryName" align="center" label="测评子类名称">
      </el-table-column>
      <el-table-column prop="classifyName" align="center" label="点位分类">
      </el-table-column>
      <el-table-column prop="typeName" align="center" label="点位类型">
      </el-table-column>
      <el-table-column prop="bound" align="center" label="绑定点位状态">
        <template slot-scope="scope">
          {{ scope.row.bound === false ? "未绑定" : "已绑定" }}
        </template>
      </el-table-column>
      <el-table-column prop="positionName" align="center" label="绑定点位名称">
      </el-table-column>
      <el-table-column prop="positionCode" align="center" label="绑定点位编码">
      </el-table-column>
      <el-table-column prop="creatorName" align="center" label="创建人">
      </el-table-column>
      <el-table-column align="center" label="生成时间">
        <template slot-scope="scope">
          {{ scope.row.createDate.substring(0, 16) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="isShow"
        align="center"
        header-align="center"
        width="200"
        label="操作"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button type="text" @click="getDetail(scope.row.id)"
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 30, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination>
    <!-- 工单编辑页面 -->
    <plsc-update
      v-if="plscUpdateVisible"
      ref="plscUpdate"
      @refreshDataList="getDataList"
    ></plsc-update>
    <!-- <update></update> -->
  </div>
</template>
<script>
import PlscUpdate from './plsc-update'
import elDict from '@/components/el-dict/index'
export default {
  data () {
    return {
      channel: '',
      value: 'QRCodeTypeCode',
      value2: 'QRCodeClassificationCode',
      dataList: [],
      channelList: [
        {
          name: '未绑定',
          isShow: 0
        },
        {
          name: '已绑定',
          isShow: 1
        }
      ],
      dataForm: {
        id: null,
        title: '',
        orderName: '',
        relationType: '',
        state: '',
        code: '',
        classifyCode: '',
        leaderId: '',
        startTime: '',
        endTime: '',
        typeCode: '',
        positionCode: ''
      },
      plscUpdateVisible: false,
      pageIndex: 1,
      pageSize: 30,
      totalPage: 0
    }
  },
  activated () {
    this.getDataList()
  },
  components: {
    PlscUpdate,
    elDict
  },
  methods: {
    // 获取数据列表
    getDataList () {
      this.$http({
        url: this.$http.adornUrl('/position/qrCode/info/page'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'code': this.dataForm.code,
          'bound': this.dataForm.state,
          'typeCode': this.dataForm.typeCode,
          'classifyCode': this.dataForm.classifyCode,
          'positionCode': this.dataForm.positionCode
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.totalPage = data.obj.total
          this.dataList = data.obj.records
        } else {
          this.dataList = []
          this.totalPage = 0
        }
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    plscUpdateHandle () {
      this.plscUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.plscUpdate.init()
      })
    },
    indexAdd (index) {
      return index + 1
    },
    // 删除
    remove (id) {
      this.$confirm(`确定要删除此二维码?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/position/qrCode/info/removeByIds'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': id
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '删除成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error('删除失败')
          }
        })
      })
    },
    // 解除绑定
    jiechu (id) {
      this.$confirm(`确定要解除绑定?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/position/qrCode/info/Unbound'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '解绑成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error('解绑失败')
          }
        })
      })
    },
    getDetail (id) {
      this.$router.push({ name: 'codeDetail', query: { id: id } })
    }
  }
}
</script>
