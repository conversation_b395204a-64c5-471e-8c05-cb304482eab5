<!-- 工单编辑页面 -->
<template>
  <div>
  <el-dialog
     append-to-body
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="100px"
    >
      <el-form-item label="工单名称" prop="orderName" :error="orderNameError">
        <el-input
          v-model="dataForm.orderName"
          placeholder="工单名称"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="工单描述"
        prop="contentDesc"
        :error="contentDescError"
      >
        <el-input
          type="textarea"
          :rows="2"
          v-model="dataForm.contentDesc"
          placeholder="工单描述"
        ></el-input>
      </el-form-item>
      <el-form-item label="任务图片">
        <el-upload
          :action="this.$http.adornUrl(`/file/oss/upload`)"
          :headers="myHeaders"
          :data="{ serverCode: this.serverCode, media: false }"
          list-type="picture-card"
          :file-list="fileList"
          :on-success="handleAvatarSuccess"
          :before-upload="beforeAvatarUpload"
          :on-remove="handleRemove"
        >
          <i class="el-icon-plus"></i>
        </el-upload>
      </el-form-item>
      <el-form-item label="所属区域">
        <el-select
          v-model="dataForm.regionCode"
          clearable
          placeholder="选择工单区域"
        >
          <el-option
            v-for="item in regionList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="详细地址"
        prop="orderAddress"
        :error="orderAddressError"
      >
        <el-input
          type="textarea"
          :rows="2"
          v-model="dataForm.orderAddress"
          placeholder="工单描述"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="工单开始时间"
        prop="startTime"
        :error="startTimeError"
      >
        <el-date-picker
          type="date"
          value-format="yyyy-MM-dd"
          v-model="dataForm.startTime"
          placeholder="选择工单开始时间"
          align="right"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="工单结束时间" prop="endTime" :error="endTimeError">
        <el-date-picker
          type="date"
          value-format="yyyy-MM-dd"
          v-model="dataForm.endTime"
          placeholder="选择工单结束时间"
          align="right"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="参与人">
        <el-input
          v-model="dataForm.user"
          placeholder="请选择参与人"
          @focus="selectUser"
          :disabled="noEdit"
        >
          <i
            @click="selectUser"
            slot="suffix"
            style="margin-right: 5px"
            class="el-icon-arrow-down"
          ></i>
        </el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">保存</el-button>
    </span>
  </el-dialog>
  <chooseExecutor
    v-if="selectUserDialogVisible"
    ref="selectUser"
  ></chooseExecutor>
  </div>
</template>

<script>
import chooseExecutor from '../clap/component/choose-executor'
import Vue from 'vue'
export default {
  components: {
    chooseExecutor
  },
  data () {
    return {
      visible: false,
      fileList: [],
      fileUrlList: [],
      myHeaders: { Authorization: sessionStorage.getItem('Authorization') },
      serverCode: 'LocalServer',
      dialogVisible: false,
      selectUserDialogVisible: false,
      dataForm: {
        id: null,
        orderName: '',
        contentDesc: '',
        orderAddress: '',
        startTime: null,
        endTime: null,
        orderFileUrlList: [],
        fileUrlList: []
      },
      noEdit: false,
      regionList: null,
      dataRule: {
        orderName: [
          { required: true, message: '任务名称不能为空', trigger: 'blur' }
        ],
        contentDesc: [
          { required: true, message: '任务明细不能为空', trigger: 'blur' }
        ],
        orderAddress: [
          { required: true, message: '详细地址不能为空', trigger: 'blur' }
        ],
        startTime: [
          { required: true, message: '开始时间不能为空', trigger: 'blur' }
        ],
        endTime: [
          { required: true, message: '结束时间不能为空', trigger: 'blur' }
        ]
      },
      orderNameError: null,
      contentDescError: null,
      orderAddressError: null,
      startTimeError: null,
      endTimeError: null
    }
  },
  methods: {
    init (id) {
      this.dataForm.id = id || null
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/position/order/detail`),
            method: 'get',
            params: this.$http.adornParams({ id: this.dataForm.id })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
              this.fileList = data.obj.orderFileUrlList ? data.obj.orderFileUrlList.map(item => {
                return {
                  name: this.$http.adornUrl('/file' + item),
                  url: this.$http.adornUrl('/file' + item)
                }
              }) : []
              this.fileUrlList = data.obj.orderFileUrlList ? data.obj.orderFileUrlList.map(item => item) : []
              this.dataForm.user = data.obj.relationList ? data.obj.relationList.map((item) => {
                return item.leader
              }).join(',') : ''
            }
          })
        }
      })
    },
    // 获取区域
    getRegions () {
      this.$http({
        url: this.$http.adornUrl('/position/area/top'),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.regionList = data.obj
        }
      })
    },
    handleAvatarSuccess (res, file) {
      if (res.success) {
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 500
        })
        const item = {}
        item['name'] = res.obj.path
        item['url'] = this.$http.adornUrl('/file' + res.obj.path)
        this.fileList.push(item)
        this.fileUrlList.push(res.obj.path)
      } else {
        this.$message.error('上传失败')
      }
    },
    beforeAvatarUpload: function (file) {
      let isAccept = ['image/jpeg', 'image/png', 'image/bmp'].indexOf(file.type) !== -1
      let isLt2M = file.size / 1024 / 1024 < 2

      if (!isAccept) {
        this.$message.error('上传图片只能是图片!')
      }
      if (!isLt2M) {
        this.$message.error('上传文件大小不能超过 2MB!')
      }
      return isAccept && isLt2M
    },
    handleRemove (file, fileList) {
      this.fileUrlList.splice(this.fileList.indexOf(file), 1)
      this.fileList.splice(this.fileList.indexOf(file), 1)
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.clearErrors()
          this.dataForm.fileUrlList = this.fileUrlList
          this.$http({
            url: this.$http.adornUrl(`/position/order/edit`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    clearErrors () {
      this.orderNameError = null
      this.contentDescError = null
      this.orderAddressError = null
      this.startTimeError = null
      this.endTimeError = null
    },
    selectUser () {
      this.selectUserDialogVisible = true
      this.$nextTick(() => {
        this.$refs.selectUser.getOrgTreeList()
      })
    }
  }
}
</script>
