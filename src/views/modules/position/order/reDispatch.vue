<!-- 工单编辑页面 -->
<template>
  <div>
    <el-dialog
      append-to-body
      title="重新派单"
      :close-on-click-modal="false"
      :visible.sync="visible"
    >
      <el-form
        :model="dataForm"
        :rules="dataRule"
        ref="dataForm"
        @keyup.enter.native="dataFormSubmit()"
        label-width="100px"
      >
        <el-form-item label="参与人">
          <el-input
            v-model="dataForm.user"
            placeholder="请选择参与人"
            @focus="selectUser"
            :disabled="noEdit"
          >
            <i
              @click="selectUser"
              slot="suffix"
              style="margin-right: 5px"
              class="el-icon-arrow-down"
            ></i>
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">保存</el-button>
    </span>
    </el-dialog>
    <chooseExecutor
      v-if="selectUserDialogVisible"
      ref="selectUser"
    ></chooseExecutor>
  </div>
</template>

<script>
import chooseExecutor from '../clap/component/choose-executor'
import Vue from 'vue'
export default {
  components: {
    chooseExecutor
  },
  data () {
    return {
      visible: false,
      dialogVisible: false,
      selectUserDialogVisible: false,
      dataForm: {
        id: null,
        orderName: '',
        contentDesc: '',
        orderAddress: '',
        startTime: null,
        endTime: null,
        orderFileUrlList: [],
        fileUrlList: []
      }
    }
  },
  methods: {
    init (id) {
      this.dataForm.id = id || null
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/position/order/detail`),
            method: 'get',
            params: this.$http.adornParams({ id: this.dataForm.id })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
              this.dataForm.user = data.obj.relationList ? data.obj.relationList.map((item) => {
                return item.leader
              }).join(',') : ''
            }
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/position/order/reDispatch`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    selectUser () {
      this.selectUserDialogVisible = true
      this.$nextTick(() => {
        this.$refs.selectUser.getOrgTreeList()
      })
    }
  }
}
</script>
