<!-- 工单详情页面 -->
<template>
  <div>
    <h4>详情</h4>
    <el-divider></el-divider>
    <div class="detail">
      <div class="content">
        <div class="item-row">
          <div class="item-column">
            <div class="label">单号</div>
            <div class="text" v-text="dataForm.orderCode"></div>
          </div>
          <div class="item-column">
            <div class="label">任务名称</div>
            <div class="text" v-text="dataForm.orderName"></div>
          </div>
          <div class="item-column">
            <div class="label">所属街道</div>
            <div class="text" v-text="dataForm.regionName"></div>
          </div>
          <div class="item-column">
            <div class="label">所属社区</div>
            <div class="text" v-text="dataForm.communityName"></div>
          </div>
        </div>

        <div class="item-row">
          <div class="item-column">
            <div class="label">所属网格</div>
            <div class="text" v-text="dataForm.gridName"></div>
          </div>
          <div class="item-column">
            <div class="label">详细地址</div>
            <div class="text" v-text="dataForm.orderAddress"></div>
          </div>
          <div class="item-column">
            <div class="label">开始日期</div>
            <div class="text" v-text="dataForm.startTime"></div>
          </div>
          <div class="item-column">
            <div class="label">结束日期</div>
            <div class="text" v-text="dataForm.endTime"></div>
          </div>
        </div>

        <div class="item-row">
          <div class="item-column">
            <div class="label">参与人</div>
            <div
              class="text"
              v-for="item in dataForm.relationList"
              v-text="item.leader"
            ></div>
          </div>
          <div class="item-column">
            <div class="label">状态</div>
            <div class="text" v-text="dataForm.stateName"></div>
          </div>
        </div>
      </div>
    </div>
    <h4>处理结果</h4>
    <el-divider></el-divider>
    <div class="detail">
      <div class="content">
        <div class="item-row">
          <div class="item-column">
            <div class="label">任务明细</div>
            <div class="text" v-text="dataForm.contentDesc"></div>
          </div>
        </div>
        <div class="item-row">
          <div class="item-column">
            <div class="label">任务图片</div>
            <div class="text">
              <div
                class="question-img"
                v-for="(item, index) in dataForm.orderFileUrlList"
                :key="index"
              >
                <img :src="prefix + item" @click="openImage(prefix + item)" />
              </div>
              <el-dialog
                append-to-body
                :visible.sync="dialogImageDialogVisible"
              >
                <img width="100%" :src="dialogImageUrl1" alt="" />
              </el-dialog>
            </div>
          </div>
        </div>
        <div class="item-row">
          <div class="item-column">
            <div class="label">任务回复</div>
            <div
              class="text"
              v-text="dataForm.reply ? dataForm.reply.replyMsg : ''"
            ></div>
          </div>
        </div>
        <div class="item-row">
          <div class="item-column">
            <div class="label">回复图片</div>
            <div class="text">
              <div
                class="question-img"
                v-for="(item, index) in dataForm.replyFileUrlList"
                :key="index"
              >
                <img :src="prefix + item" @click="openImage(prefix + item)" />
              </div>
              <el-dialog
                append-to-body
                :visible.sync="dialogImageDialogVisible"
              >
                <img width="100%" :src="dialogImageUrl2" alt="" />
              </el-dialog>
            </div>
          </div>
        </div>
        <div>
          <el-button
            type="primary"
            v-show="dataForm.state === 1"
            @click="revoke()"
            >撤回</el-button
          >
          <el-button
            type="primary"
            v-show="dataForm.state < 8"
            @click="remove()"
            >删除</el-button
          >
        </div>
      </div>
    </div>
    <div class="mg-100"></div>
    <h4>流转记录</h4>
    <el-divider></el-divider>
    <order-log-list :dataLogList="dataList"></order-log-list>
  </div>
</template>

<script>
import OrderLogList from './orderLogList'
export default {
  components: { OrderLogList },
  activated () {
    this.prefix = window.SITE_CONFIG.baseUrl + '/file'
    this.getDetail()
  },
  data () {
    return {
      dataForm: {
        orderCode: '',
        orderName: '',
        contentDesc: '',
        regionName: '',
        communityName: '',
        gridName: '',
        orderAddress: '',
        startTime: null,
        endTime: null,
        stateName: null,
        reply: null,
        orderLogList: ''
      },
      dataList: [],
      fileUrlArr: [],
      orderFileUrlList: [],
      applyFileUrlArr: [],
      replyFileUrlList: [],
      replyMsg: '',
      dialogImageDialogVisible: false,
      dialogImageUrl1: '',
      dialogImageUrl2: ''
    }
  },
  methods: {
    getDetail () {
      this.$http({
        url: this.$http.adornUrl(`/position/order/detail`),
        method: 'get',
        params: this.$http.adornParams({ id: this.$route.query.id })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataForm = data.obj
          this.dataList = this.dataForm.orderLogList
        }
      })
    },
    // 删除
    remove () {
      this.$confirm(`确定要删除此工单?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/position/order/delete'),
          method: 'get',
          params: this.$http.adornParams({
            'id': this.$route.query.id
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '撤销成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 撤回
    revoke () {
      this.$confirm(`确定要撤回此工单?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/position/order/withdraw'),
          method: 'get',
          params: this.$http.adornParams({
            'id': this.$route.query.id
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '撤销成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 图片预览
    openImage (url) {
      this.dialogImageDialogVisible = true
      this.dialogImageUrl2 = url
    }
  }
}
</script>

<style lang="scss" scoped>
.el-divider {
  margin: 8px 0;
  background: 0 0;
  border-top: 1px solid #e6ebf5;
}
.detail {
  .content {
    width: 100%;
    .item-row {
      line-height: 30px;
      margin-top: 25px;
      margin-bottom: 25px;
      display: flex;
      flex-direction: row;
      .item-column {
        margin-right: 20px;
        flex: 1;
        .label {
          color: #7f7f7fd8;
        }
        .text {
          display: flex;
          .question-img {
            margin-right: 20px;
            width: 148px;
            height: 148px;
            border-radius: 10px 10px;
            img {
              width: 100%;
              height: 100%;
            }
          }
        }
        &:last-child {
          margin-right: unset;
        }
      }
    }
  }
  .button {
    margin: 35px 0;
    text-align: center;
    .el-button {
      margin: 0 20px;
    }
  }
  .icon-c {
    color: #409eff;
  }
  .mg-100 {
    margin-bottom: 100px;
  }
}
</style>
