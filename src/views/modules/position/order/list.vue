<!-- 工单列表页面 -->
<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <el-form-item>
        <el-input
          v-model="dataForm.orderCode"
          placeholder="单号"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="dataForm.orderName"
          placeholder="工单名称"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="dataForm.relationType"
          clearable
          placeholder="选择工单类型"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="dataForm.state"
          clearable
          placeholder="选择工单状态"
        >
          <el-option
            v-for="item in states"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="dataForm.regionCode"
          clearable
          placeholder="选择工单区域"
        >
          <el-option
            v-for="item in regions"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          type="date"
          value-format="yyyy-MM-dd"
          v-model="dataForm.startTime"
          placeholder="选择开始日期"
          align="right"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          type="date"
          value-format="yyyy-MM-dd"
          v-model="dataForm.endTime"
          placeholder="选择结束日期"
          align="right"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" @click="getDataList()">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      height="700px"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%"
    >
      <!--      <el-table-column-->
      <!--        type="selection"-->
      <!--        header-align="center"-->
      <!--        align="center"-->
      <!--        width="50">-->
      <!--      </el-table-column>-->
      <el-table-column
        prop="orderCode"
        header-align="center"
        align="center"
        label="工单号"
      >
      </el-table-column>
      <el-table-column
        prop="orderName"
        header-align="center"
        align="center"
        label="工单名称"
      >
      </el-table-column>
      <el-table-column
        prop="regionName"
        header-align="center"
        align="center"
        label="所属区域"
      >
      </el-table-column>
      <!--      <el-table-column-->
      <!--        prop="communityName"-->
      <!--        header-align="center"-->
      <!--        align="center"-->
      <!--        label="所属社区">-->
      <!--      </el-table-column>-->
      <!--      <el-table-column-->
      <!--        prop="gridName"-->
      <!--        header-align="center"-->
      <!--        align="center"-->
      <!--        label="所属网格">-->
      <!--      </el-table-column>-->
      <el-table-column
        prop="relationType"
        header-align="center"
        align="center"
        label="工单类型"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.relationType === 1" size="small"
            >随手拍工单</el-tag
          >
          <el-tag v-else size="small">点位测评工单</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="startTime"
        header-align="center"
        align="center"
        label="开始时间"
      >
      </el-table-column>
      <el-table-column
        prop="endTime"
        header-align="center"
        align="center"
        label="结束时间"
      >
      </el-table-column>
      <el-table-column
        prop="leaders"
        header-align="center"
        align="center"
        label="参与人"
      >
        <template slot-scope="scope">
          <el-tag
            v-for="item in scope.row.relationList"
            size="small"
            :key="item.leader"
            >{{ item.leader }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="state"
        header-align="center"
        align="center"
        label="状态"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.state === 1" size="small" type="warning"
            >待接收</el-tag
          >
          <el-tag v-if="scope.row.state === 2" size="small">已撤回</el-tag>
          <el-tag v-if="scope.row.state === 4" size="small" type="danger"
            >已退回</el-tag
          >
          <el-tag v-if="scope.row.state === 8" size="small" type="primary"
            >已接收</el-tag
          >
          <el-tag v-if="scope.row.state === 16" size="small" type="info"
            >已回复</el-tag
          >
          <el-tag v-if="scope.row.state === 32" size="small" type="success"
            >已完成</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            v-if="
              scope.row.state === 4
            "
            type="text"
            @click="reDispatch(scope.row.id)"
          >重新派单</el-button
          >
          <el-button
            v-if="
              scope.row.state === 1 ||
              scope.row.state === 2 ||
              scope.row.state === 4
            "
            type="text"
            @click="edit(scope.row.id)"
            >编辑</el-button
          >
          <el-button
            v-if="
              scope.row.state === 1 ||
              scope.row.state === 2 ||
              scope.row.state === 4
            "
            type="text"
            @click="remove(scope.row.id)"
            >删除</el-button
          >
          <el-button
            v-if="scope.row.state === 1"
            type="text"
            @click="revoke(scope.row.id)"
            >撤回</el-button
          >
          <el-button type="text" @click="getDetail(scope.row.id)"
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 30, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination>
    <!-- 工单编辑页面 -->
    <update
      v-if="updateVisible"
      ref="update"
      @refreshDataList="getDataList"
    ></update>
    <!-- 重新派单页面-->
    <re-dispatch
      v-if="reDispatchVisible"
      ref="reDispatch"
      @refreshDataList="getDataList"
    ></re-dispatch>
  </div>
</template>

<script>
import Update from './update'
import reDispatch from './reDispatch'
export default {
  data () {
    return {
      startTime: '',
      endTime: '',
      dataForm: {
        id: null,
        orderCode: '',
        orderName: '',
        relationType: '',
        state: '',
        regionCode: '',
        leaderId: '',
        startTime: '',
        endTime: ''
      },
      options: [{
        value: '1',
        label: '随手拍工单'
      }, {
        value: '2',
        label: '点位测评工单'
      }],
      states: [{
        value: '1',
        label: '待接收'
      }, {
        value: '2',
        label: '已撤回'
      }, {
        value: '4',
        label: '已退回'
      }, {
        value: '8',
        label: '待回复'
      }, {
        value: '16',
        label: '已回复'
      }, {
        value: '32',
        label: '已完成'
      }],
      updateVisible: false,
      reDispatchVisible: false,
      dataList: [],
      pageIndex: 1,
      pageSize: 30,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      regions: []
    }
  },
  activated () {
    this.getDataList()
    this.getRegions()
  },
  components: {
    Update,
    reDispatch
  },
  methods: {
    // 获取区域
    getRegions () {
      this.$http({
        url: this.$http.adornUrl('/position/area/top'),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.regions = data.obj
        }
      })
    },
    // 获取数据列表
    getDataList () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/position/order/pages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'regionCode': this.dataForm.regionCode,
          'orderCode': this.dataForm.orderCode,
          'orderName': this.dataForm.orderName,
          'relationType': this.dataForm.relationType,
          'state': this.dataForm.state,
          'startTime': this.dataForm.startTime,
          'endTime': this.dataForm.endTime
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle (val) {
      this.dataListSelections = val
    },
    // 删除
    remove (id) {
      this.$confirm(`确定要删除此工单?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/position/order/delete'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '撤销成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 撤回
    revoke (id) {
      this.$confirm(`确定要撤回此工单?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/position/order/withdraw'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '撤销成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 重新派单
    reDispatch (id) {
      this.reDispatchVisible = true
      this.$nextTick(() => {
        this.$refs.reDispatch.init(id)
      })
    },
    // 编辑页面
    edit (id) {
      this.updateVisible = true
      this.$nextTick(() => {
        this.$refs.update.init(id)
        this.$refs.update.getRegions()
      })
    },
    // 详情跳转页面
    getDetail (id) {
      this.$router.push({ path: '/orderDetail', query: { id: id } })
    }
  }
}
</script>
