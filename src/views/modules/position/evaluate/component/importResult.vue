<template>
  <div>
    <div class="module">
      <div></div>
      <div class="module_content">
        <p align="center">导入结果</p>
        <p align="center">
          <span style="color: red">{{ num }}</span> 条
          错误记录，你可以查看未导入成功的数据，修改后重新上传
        </p>
        <div style="text-align: center">
          <el-button
            v-if="num !== 0 && type + '' === '1'"
            type="primary"
            @click="exportFile"
            >下载错误数据</el-button
          >
        </div>
        <div style="text-align: center">
          <el-button
            v-if="type + '' === '6'"
            type="primary"
            @click="exportCeHuiFile"
          >下载测绘相关区域数据</el-button
          >
        </div>
      </div>
    </div>
    <div v-if="num !== 0">
      <p>错误详情</p>
      <el-table
        v-if="type + '' === '1'"
        :data="tableData"
        border
        style="width: 100%"
      >
        <el-table-column prop="rank" label="行数" width="60" align="center">
        </el-table-column>
        <el-table-column prop="evaluateName" label="测评内容"  header-align="center">
        </el-table-column>
        <el-table-column prop="contentName" label="测评项/测评明细" header-align="center">
        </el-table-column>
        <el-table-column prop="failReason" label="错误详情" width="200" align="center"> </el-table-column>
      </el-table>
      <el-table
        v-if="type + '' === '2' || type + '' === '3' || type + '' === '4'"
        :data="tableData"
        border
        style="width: 100%"
      >
        <el-table-column prop="index" label="行数" width="60" align="center">
        </el-table-column>
        <el-table-column prop="msg" label="错误详情"  align="center"></el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
          :current-page="pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      num: 0,
      tableData: [],
      list: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      url: '',
      type: ''
    }
  },
  activated () {
    this.type = this.$route.query.type
    this.handQueryData()
  },
  methods: {
    handQueryData () {
      if (this.$route.query.type + '' === 1 + '') {
        this.url = '/evaluate/itemDetail/getFailData'
      }
      if (this.$route.query.type + '' === 2 + '') {
        this.url = '/position/position/getListByUUId'
      }
      if (this.$route.query.type + '' === 3 + '') {
        this.url = '/position/sample/getListByUUId'
      }
      if (this.$route.query.type + '' === 4 + '') {
        this.url = '/admin/user/getListByUUId'
      }
      if (this.$route.query.type + '' === 6 + '') {
        this.url = '/position/area/getListByUUId'
      }
      this.handEvaluateData()
    },
    async handEvaluateData () {
      await this.$http({
        url: this.$http.adornUrl(this.url),
        method: 'get',
        params: {
          uuid: this.$route.query.uuid
        }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.list = resp.data.obj
          this.num = resp.data.obj.length
          this.total = resp.data.obj.length
        } else {
          this.list = []
          this.num = 0
        }
      })
      this.getTableData()
    },
    getTableData () {
      this.tableData = []
      const startPage = (this.pageNum - 1) * this.pageSize
      const endPage = this.pageNum * this.pageSize
      for (let i in this.list) {
        if (i >= startPage && i < endPage) {
          this.tableData.push(this.list[i])
        }
      }
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageNum = 1
      this.getTableData()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageNum = val
      this.getTableData()
    },
    exportFile () {
      this.$http({
        url: this.$http.adornUrl('/evaluate/itemDetail/exportExcelData'),
        method: 'get',
        responseType: 'arraybuffer',
        params: {
          uuid: this.$route.query.uuid
        }
      }).then(({ data }) => {
        let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8' })
        window.location.href = URL.createObjectURL(blob)
      })
    },
    exportCeHuiFile () {
      this.$http({
        url: this.$http.adornUrl('/position/area/export'),
        method: 'get',
        responseType: 'arraybuffer',
        params: {
          uuid: this.$route.query.uuid
        }
      }).then(({ data }) => {
        let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8' })
        window.location.href = URL.createObjectURL(blob)
      })
    }
  }

}
</script>

<style>
.module {
  width: 100%;
  height: 200px;
  background-color: rgb(242, 242, 242);
}
.module_content {
  width: 100%;
  margin-top: 25px;
  display: inline-block;
}
</style>
