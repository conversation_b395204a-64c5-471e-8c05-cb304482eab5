<template>
  <div>
    <p align="left" style="font-size: 20px">测评基本信息</p>
    <el-divider></el-divider>
    <br />
    <div>
      <div class="text_size">
        <span style="color: rgb(127, 127, 127)">测评单号</span>
        <br />
        <p style="margin-right: 40px">{{ formData.instanceCode }}</p>
      </div>
      <div class="text_size">
        <span style="color: rgb(127, 127, 127)">名称</span>
        <br />
        <p style="margin-right: 40px">{{ formData.instanceName }}</p>
      </div>
      <div class="text_size">
        <span style="color: rgb(127, 127, 127)">详细地址</span>
        <br />
        <p style="margin-right: 40px">
          {{ formData.address }}
          <i
            v-if="formData.address"
            @click="openMap(formData.locationLonLat)"
            style="color: blue"
            class="el-icon-location-outline"
          ></i>
        </p>
      </div>
      <div class="text_size">
        <span style="color: rgb(127, 127, 127)">系统定位地点</span>
        <br />
        <p style="margin-right: 40px">
          {{ formData.sysLocationAddress }}
          <i
            v-if="formData.sysLocationAddress"
            @click="openMap(formData.actualLonLat)"
            style="color: blue"
            class="el-icon-location-outline"
          ></i>
        </p>
      </div>
      <div class="text_size">
        <span style="color: rgb(127, 127, 127)">测评员</span>
        <br />
        <p style="margin-right: 40px">{{ formData.evaluatorName }}</p>
      </div>
      <div class="text_size">
        <span style="color: rgb(127, 127, 127)">测评卡版本</span>

        <br />
        <p style="margin-right: 40px">{{ formData.cardName }}</p>
      </div>
      <div class="text_size">
        <span style="color: rgb(127, 127, 127)">测评类型</span>

        <br />
        <p style="margin-right: 40px">{{ formData.categoryName }}</p>
      </div>
      <div class="text_size">
        <span style="color: rgb(127, 127, 127)">测评子类型</span>
        <br />
        <p style="margin-right: 40px">{{ formData.typeName }}</p>
      </div>
      <div class="text_size">
        <span style="color: rgb(127, 127, 127)">点位经纬度</span>

        <br />
        <p style="margin-right: 40px">
          {{ formData.locationLonLat
          }}<i
            v-if="formData.locationLonLat"
            @click="openMap(formData.locationLonLat)"
            style="color: blue"
            class="el-icon-location-outline"
          ></i>
        </p>
      </div>
      <div class="text_size">
        <span style="color: rgb(127, 127, 127)">实际经纬度</span>
        <br />
        <p style="margin-right: 40px">
          {{ formData.actualLonLat }}
          <i
            v-if="formData.actualLonLat"
            @click="openMap(formData.actualLonLat)"
            style="color: blue"
            class="el-icon-location-outline"
          ></i>
        </p>
      </div>

      <div class="text_size">
        <span style="color: rgb(127, 127, 127)">提交时间</span>
        <br />
        <p style="margin-right: 40px">{{ formData.evaluatorDate }}</p>
      </div>
      <div class="text_size">
        <span style="color: rgb(127, 127, 127)">所属区域</span>
        <br />
        <p style="margin-right: 40px">{{ formData.regionName }}</p>
      </div>
      <!-- <div class="text_size">
        <span style="color: rgb(127, 127, 127)">所属社区</span>
        <br />
        <p style="margin-right: 40px">{{ formData.areaName }}</p>
      </div> -->
      <!-- <div class="text_size">
        <span style="color: rgb(127, 127, 127)">所属网格</span>
        <br />
      </div> -->
    </div>

    <!-- 引入地图 -->
    <mapDiaglog
      v-if="mapDataVisible"
      ref="mapDiaglog"
      @change="getMapData($event)"
    ></mapDiaglog>
  </div>
</template>

<script>
import mapDiaglog from '../../position/componet/mapDiaglogDetails.vue'
export default {
  components: {
    mapDiaglog
  },
  data () {
    return {
      formData: {
        actualLonLat: null,
        address: '',
        areaName: '',
        cardName: '',
        categoryName: '',
        evaluateCore: null,
        evaluateNeed: '',
        evaluateProject: '',
        evaluatorDate: '',
        evaluatorId: '',
        evaluatorName: '',
        evaluatorPhone: '',
        instanceCode: '',
        instanceName: '',
        isGenerateRectificationBook: null,
        isRectification: null,
        lat: null,
        locationLonLat: null,
        lon: null,
        optionCode: null,
        optionId: null,
        optionName: null,
        questionId: null,
        rectificationComment: null,
        regionId: '',
        regionName: '',
        remark: null,
        resetDate: null,
        resetReason: null,
        sequence: null,
        status: '',
        statusName: null,
        sysLocationAddress: null,
        typeId: '',
        typeName: ''
      },
      mapData: {
        address: '',
        lng: '',
        lat: '',
        dialogVisible: true
      },
      mapDataVisible: false
    }
  },
  activated () {
  },
  methods: {
    init (id) {
      this.$http({
        url: this.$http.adornUrl('/evaluate/questionOpInstance'),
        method: 'get',
        params: { id: id }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.formData = resp.data.obj
        }
      })
    },
    openMap (data) {
      this.mapData.dialogVisible = false
      this.mapDataVisible = true
      if (data) {
        this.mapData.lat = data.split(',')[0]
        this.mapData.lng = data.split(',')[1]
      }
      this.$nextTick(() => {
        this.$refs.mapDiaglog.visible = true
        this.$refs.mapDiaglog.initMap(this.mapData)
      })
    }
  }

}
</script>

<style>
.item .el-form-item__label {
  /* color: wheat; */
  size: 20px;
}
.text_size {
  width: 20%;
  float: left;
  text-align: left;
  margin-top: 20px;
  word-wrap: break-word;
  word-break: normal;
  height: 80px;
}
.el-divider {
  margin: 8px 0;
  background: 0 0;
  border-top: 1px solid #e6ebf5;
}
</style>
