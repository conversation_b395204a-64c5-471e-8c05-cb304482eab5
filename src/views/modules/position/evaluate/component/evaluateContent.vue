<template>
  <div>
    <div>
      <el-table
        :row-style="{ height: '20px' }"
        :cell-style="{ padding: '0px' }"
        ref="multipleTable"
        border
        :span-method="objectSpanMethod"
        :cell-class-name="tableRowClassName"
        @cell-mouse-leave="cellMouseLeave"
        @cell-mouse-enter="cellMouseEnter"
        :data="tableData"
        style="width: 100%; margin: 0 auto; font-size: 6px"
      >
        <el-table-column
          label="编号"
          type="index"
          align="center"
          min-width="3%"
        ></el-table-column>
        <el-table-column
          prop="evaluateName"
          label="测评内容"
          align="center"
          min-width="24%"
          ><template slot-scope="scope">
            <!-- style="color: #409eff; cursor: pointer"
              @click="updateContentSequence(scope.row)" -->
            <span>{{ scope.row.evaluateName }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="contentName"
          label="测评项\测评明细"
          align="center"
          min-width="24%"
        ></el-table-column>
        <el-table-column
          prop="categoryName"
          label="测评分类"
          align="center"
          min-width="7%"
        ></el-table-column>
        <el-table-column
          prop="dutyName"
          label="责任局办"
          align="center"
          min-width="7%"
        ></el-table-column>

        <el-table-column
          prop="core"
          label="分值"
          align="center"
          min-width="4%"
        ></el-table-column>
        <el-table-column
          prop="reduceCore"
          label="扣分"
          align="center"
          min-width="4%"
        ></el-table-column>
        <el-table-column
          prop="handType"
          label="类型"
          align="center"
          min-width="4%"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.handType === "0" ? "静态" : "动态" }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="handTime"
          label="处理时长"
          align="center"
          min-width="6%"
        >
        </el-table-column>
        <el-table-column
          prop="reduceStandard"
          label="扣分标准"
          align="center"
          min-width="8%"
        >
        </el-table-column>
        <el-table-column
          prop="watchStandard"
          label="拍照要求"
          align="center"
          min-width="8%"
        >
        </el-table-column>
        <el-table-column
          prop="result"
          label="评判标准"
          align="center"
          min-width="6%"
        >
        </el-table-column>
        <el-table-column
          prop="resultCore"
          label="得分"
          align="center"
          min-width="5%"
        >
          <!-- <template slot-scope="scope">
            <span @click="hand(scope.row)">--</span>
          </template> -->
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    tableMessageData: Array
  },
  data () {
    return {
      tableData: [],
      rowIndex: '-1',
      OrderIndexArr: [],
      hoverOrderArr: []
    }
  },
  activated () {
    this.tableData = this.tableMessageData
    this.getOrderNumber()
  },
  methods: {
    init (id, detailQuestionId) {
      this.tableMessageData = []
      this.$http({
        url: this.$http.adornUrl('/evaluate/questionOpInstance/getEvaluateDetail'),
        method: 'get',
        params: { id: id, detailQuestionId: detailQuestionId }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.tableData = resp.data.obj
          this.getOrderNumber()
        } else {
          this.tableData = []
        }
      })
    },
    // 获取相同编号的数组
    getOrderNumber () {
      this.OrderIndexArr = []
      let OrderObj = {}
      if (this.tableData) {
        this.tableData.forEach((element, index) => {
          element.rowIndex = index
          if (OrderObj[element.evaluateName]) {
            OrderObj[element.evaluateName].push(index)
          } else {
            OrderObj[element.evaluateName] = []
            OrderObj[element.evaluateName].push(index)
          }
        })

        // 将数组长度大于1的值 存储到this.OrderIndexArr（也就是需要合并的项）
        for (let k in OrderObj) {
          if (OrderObj[k].length > 1) {
            this.OrderIndexArr.push(OrderObj[k])
          }
        }
      }
    },

    // 合并单元格
    objectSpanMethod ({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 1 ||
        columnIndex === 5 ||
        columnIndex === 6 ||
        columnIndex === 9 ||
        columnIndex === 10 ||
        columnIndex === 11 ||
        columnIndex === 12) {
        for (let i = 0; i < this.OrderIndexArr.length; i++) {
          let element = this.OrderIndexArr[i]
          for (let j = 0; j < element.length; j++) {
            let item = element[j]
            if (rowIndex === item) {
              if (j === 0) {
                return {
                  rowspan: element.length,
                  colspan: 1
                }
              } else if (j !== 0) {
                return {
                  rowspan: 0,
                  colspan: 0
                }
              }
            }
          }
        }
      }
    },

    tableRowClassName ({ row, rowIndex }) {
      let arr = this.hoverOrderArr
      for (let i = 0; i < arr.length; i++) {
        if (rowIndex === arr[i]) {
          return 'hovered-row'
        }
      }
    },

    cellMouseEnter (row, column, cell, event) {
      this.rowIndex = row.rowIndex
      this.hoverOrderArr = []
      this.OrderIndexArr.forEach((element) => {
        if (element.indexOf(this.rowIndex) >= 0) {
          this.hoverOrderArr = element
        }
      })
    },
    hand (row) { },

    cellMouseLeave (row, column, cell, event) {
      this.rowIndex = '-1'
      this.hoverOrderArr = []
    }
  },
  watch: {
    tableMessageData: function (val) {
      this.tableData = val
      this.getOrderNumber()
    }
  }
}
</script>

<style>
</style>