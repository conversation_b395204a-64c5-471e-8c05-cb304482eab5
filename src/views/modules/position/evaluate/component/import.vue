<template>
  <div>
    <div class="module">
      <div></div>
      <div class="module_content">
        <p>第一步：下载excel导入模板，按格式批量填写数据，点我下载</p>
        <p>根据提示信息完善表格内容</p>
        <el-button @click="openFile()">下载模板表格</el-button>
      </div>
    </div>
    <div class="import">
      <div class="import_content">
        <p>第一步：上传完善后的表格</p>
        <el-upload
          class="upload-demo"
          drag
          :auto-upload="false"
          :action="action"
          :headers="headers"
          :data="params"
          :file-list="fileList"
          :limit="1"
          ref="upload"
          :on-progress="onUpload"
          :on-success="uploadSuccess"
          :on-error="uploadErr"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        </el-upload>
        <!-- //进度条 -->
      </div>
    </div>
    <div>
      <el-button
        type="primary"
        style="margin-left: 10px; margin-top: 20px"
        @click="submitUpload"
      >
        导入
      </el-button>
      <el-button style="margin-left: 10px; margin-top: 20px"> 取消 </el-button>
    </div>
    <el-dialog append-to-body :visible.sync="dialogVisible" width="40%">
      <div v-if="loading">
        <el-progress
          :percentage="progressPercent"
          :text-inside="true"
          :stroke-width="24"
          status="success"
        >
        </el-progress>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data () {
    return {
      action: '',
      headers: {
        'Authorization': ''
      },
      fileList: [],
      params: {},
      progressPercent: 0,
      loading: false,
      timer: null,
      timer2: null,
      dialogVisible: false,
      isFinish: false

    }
  },
  activated () {
    this.getAction()
    this.headers.Authorization = this.getCookigetCookie('Authorization').replace('bearer%20', 'Bearer ')
  },
  methods: {
    getAction () {
      if (this.$route.query.type + '' === 1 + '') {
        this.action = window.SITE_CONFIG.baseUrl + '/evaluate/itemDetail/importExcelData?categoryId=' + this.$route.query.categoryId
      }
      if (this.$route.query.type + '' === 2 + '') {
        this.action = window.SITE_CONFIG.baseUrl + '/position/position/import'
      }
      if (this.$route.query.type + '' === 3 + '') {
        this.action = window.SITE_CONFIG.baseUrl + '/position/sample/import'
      }
      if (this.$route.query.type + '' === 4 + '') {
        this.action = window.SITE_CONFIG.baseUrl + '/admin/user/excel/import'
      }
      if (this.$route.query.type + '' === 5 + '') {
        this.action = window.SITE_CONFIG.baseUrl + '/position/position/question/import'
      }
      if (this.$route.query.type + '' === 6 + '') {
        this.action = window.SITE_CONFIG.baseUrl + '/position/area/import'
      }
    },
    getCookigetCookie (param) {
      var cParam = ''
      if (document.cookie.length > 0) {
        var arr = document.cookie.split('; ') // 这里显示的格式需要切割一下自己可输出看下
        for (var i = 0; i < arr.length; i++) {
          var arr2 = arr[i].split('=') // 再次切割
          // 判断查找相对应的值
          if (arr2[0] === param) {
            cParam = arr2[1]
            // 保存到保存数据的地方
          }
        }
        return cParam
      }
    },
    // 手动上传按钮
    submitUpload () {
      this.$refs.upload.submit()
    },
    // 上传时
    onUpload (event, file, fileList) {  // 三个参数看情况使用
      this.progressPercent = 0
      this.dialogVisible = true
      this.loading = true
      // 使用定时器来制作进度条
      this.timer = window.setInterval(() => {
        // progressPercent 进度条数字
        this.progressPercent = this.progressPercent + 10
        // if (this.isFinish) {
        //   this.progressPercent = this.progressPercent + 10
        // }
        // 这里之所以到99%就结束定时器，下面具体解释
        if (this.progressPercent >= 99) {
          this.progressPercent = 99
          window.clearInterval(this.timer)
          // 这里必须使用this.timer = null，否则清除定时器无效
          this.timer = null
        }
      }, 500)
    },
    // 上传成功
    async uploadSuccess (resp, file, fileList) {  // 三个参数看情况使用
      // // 清除上传文件列表
      this.$refs.upload.clearFiles()
      // 必须在上传成功之后 跳转页面
      if (resp && resp.code === 0) {
        this.isFinish = true
        this.timer2 = window.setInterval(() => {
          // progressPercent 进度条数字
          // 这里之所以到99%就结束定时器，下面具体解释
          if (this.progressPercent >= 99) {
            this.dialogVisible = false
            this.progressPercent = 0
            this.$router.push({ path: '/evaluateResult', query: { uuid: resp.obj, type: this.$route.query.type } })
            window.clearInterval(this.timer2)
            // 这里必须使用this.timer = null，否则清除定时器无效
            this.timer2 = null
          }
        }, 10)
      } else {
        this.$message.error(resp.msg)
        window.clearInterval(this.timer)
        // 这里必须使用this.timer = null，否则清除定时器无效
        this.timer = null
        this.dialogVisible = false
      }
    },

    // 上传失败
    uploadErr (err) {
      this.$message.error(err.msg)
      window.clearInterval(this.timer)
      // 这里必须使用this.timer = null，否则清除定时器无效
      this.timer = null
      this.dialogVisible = false
    },
    // 表格下载
    openFile () {
      if (this.$route.query.type + '' === 1 + '') {
        window.open('https://wmyq.sipac.gov.cn/template/文明城市测评卡导入模板.2022.xlsx')
      }
      if (this.$route.query.type + '' === 2 + '') {
        window.open('https://wmyq.sipac.gov.cn/template/文明点位导入模板.2022.11.30.xlsx')
      }
      if (this.$route.query.type + '' === 3 + '') {
        window.open('https://wmyq.sipac.gov.cn/template/文明点位导入模板.2022.04.29.xlsx')
      }
      if (this.$route.query.type + '' === 4 + '') {
        window.open('https://wmyq.sipac.gov.cn/template/文明园区一体化平台用户导入模板.xlsx')
      }
      if (this.$route.query.type + '' === 5 + '') {
        window.open('https://wmyq.sipac.gov.cn/template/随手拍导入模板.xlsx')
      }
      if (this.$route.query.type + '' === 6 + '') {
        window.open('https://wmyq.sipac.gov.cn/template/高德腾讯经纬度转网格模板.xlsx')
      }
    }

  }

}
</script>

<style>
.module {
  width: 100%;
  height: 200px;
  background-color: rgb(242, 242, 242);
}
.module_content {
  margin-top: 50px;
  margin-left: 50px;
  display: inline-block;
}

.import {
  width: 100%;
  background-color: rgb(242, 242, 242);
  margin-top: 15px;
}
.import_content {
  margin-top: 20px;
  margin-left: 50px;
  margin-bottom: 15px;
  display: inline-block;
}
.import_content_border {
  width: 400px;
  border: 2px dashed lightblue;
  display: inline-block;
}
</style>
