<template>
  <el-dialog
    title="编辑问题"
    append-to-body
    :close-on-click-modal="false"
    :visible.sync="visible"
    :before-close="resetDateForm"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      label-width="80px"
    >
      <el-form-item label="问题描述" prop="question" :error="questionError">
        <el-input
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 4 }"
          placeholder="请输入问题描述"
          v-model="dataForm.question"
        ></el-input>
      </el-form-item>
      <el-form-item label="图片">
        <el-upload
          :action="this.$http.adornUrl('/file/oss/upload')"
          multiple
          :data="{ serverCode: this.serverCode, media: false }"
          :file-list="fileUrlList"
          list-type="picture-card"
          :on-preview="handlePictureCardPreview"
          :on-remove="handleRemove"
          :on-success="handleAvatarSuccess"
          :headers="myHeaders"
        >
          <i class="el-icon-plus"></i>
        </el-upload>

        <el-dialog :visible.sync="dialogVisible" append-to-body>
          <img width="100%" :src="dialogImageUrl" alt="" />
        </el-dialog>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="dataFormSubmit">确 定</el-button>
        <el-button @click="resetDateForm()">取 消</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import Vue from 'vue'
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: null,
        question: null,
        fileUrlList: []
      },
      dataRule: {
        question: [
          { required: true, message: '问题描述不能为空', trigger: 'blur' }
        ]
      },
      questionError: null,
      fileUrlListError: null,
      serverCode: 'LocalServer',
      myHeaders: { Authorization: sessionStorage.getItem('Authorization') },
      fileUrlList: [],
      dialogVisible: false,
      dialogImageUrl: ''
    }
  },
  methods: {
    // 初始化方法
    init (question) {
      this.visible = true
      this.dataForm.id = question.id
      this.dataForm.question = question.question
      if (question.fileRelList) {
        question.fileRelList.forEach(data => {
          this.dataForm.fileUrlList.push(data.url)
          const file = {
            id: data.id,
            url: this.$http.adornUrl('/file' + data.url)
          }
          this.fileUrlList.push(file)
        })
      }
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl(`/evaluate/questionResult/handEdit`),
              method: 'post',
              data: this.$http.adornData(this.dataForm)
            }).then(({ data }) => {
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 500,
                  onClose: () => {
                    this.fileUrlList = []
                    this.dataForm.fileUrlList = []
                    this.visible = false
                    this.$emit('refreshDataList')
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        }
      })
    },
    handleAvatarSuccess (res, file) {
      if (res.success) {
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 500
        })
        this.dataForm.fileUrlList.push(res.obj.path)
      } else {
        this.$message.error('上传失败')
      }
    },
    beforeAvatarUpload: function (file) {
      let isAccept = ['image/jpeg', 'image/png', 'image/bmp'].indexOf(file.type) !== -1
      let isLt2M = file.size / 1024 / 1024 < 10

      if (!isAccept) {
        this.$message.error('上传图片只能是图片!')
      }
      if (!isLt2M) {
        this.$message.error('上传文件大小不能超过 10MB!')
      }
      return isAccept && isLt2M
    },
    handleRemove (file, fileList) {
      this.dataForm.fileUrlList.splice(this.dataForm.fileUrlList.indexOf(file), 1)
    },
    handlePictureCardPreview (file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    resetDateForm () {
      this.fileUrlList = []
      this.dataForm.fileUrlList = []
      this.$refs['dataForm'].validate()
      this.visible = false
    }
  }
}
</script>

<style>
</style>
