<template>
  <div>
    <div>
      <div style="margin-top: -10px">
        <el-button
          type="text"
          :disabled="idList.length === 0"
          @click="produceBook"
          >生成整改通知书</el-button
        >
        <el-button type="text" @click="catBook" :disabled="idList.length !== 1"
          >查看整改通知书</el-button
        >
        <!-- <el-button type="text" s>生成整改通知书</el-button> -->
      </div>
      <el-table
        :data="tableData"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="50"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="instanceCode"
          label="测评单号"
          width="150px"
          align="center"
        >
          <template slot-scope="scope">
            <span
              style="color: #409eff; cursor: pointer"
              @click="handDetail(scope.row)"
              >{{ scope.row.instanceCode }}</span
            >
          </template>
        </el-table-column>
        <el-table-column prop="instanceName" label="名称" align="center">
          <!-- <template slot-scope="scope">
            <span style="color: #409eff; cursor: pointer">{{
              scope.row.instanceName
            }}</span>
          </template> -->
        </el-table-column>
        <el-table-column prop="regionName" label="测评区域" align="center">
          <!-- <template slot-scope="scope">
            <span style="color: #409eff; cursor: pointer">{{
              scope.row.regionName
            }}</span>
          </template> -->
        </el-table-column>
        <!-- <el-table-column align="center" prop="areaName" label="测评社区" /> -->
        <el-table-column align="center" prop="typeName" label="测评类型" />
        <el-table-column
          align="center"
          prop="categoryName"
          label="测评子类型"
        />
        <el-table-column
          align="center"
          prop="evaCategoryName"
          label="测评分类"
        />
        <el-table-column align="center" prop="evaluatorName" label="测评员" />
        <el-table-column align="center" prop="evaluatorDate" label="测评时间" />
        <el-table-column
          align="center"
          prop="cardName"
          label="测评卡版本"
          show-overflow-tooltip
        />
        <el-table-column
          align="center"
          prop="isGenerateRectificationBook"
          label="整改通知书"
        >
          <template slot-scope="scope">
            <span>{{
              scope.row.isGenerateRectificationBook === "Y"
                ? "已生成"
                : "未生成"
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="isRectification"
          label="是否已整改"
        >
          <template slot-scope="scope">
            <span>{{
              scope.row.isGenerateRectificationBook === "1" ? "是" : "否"
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="status"
          label="状态"
          :formatter="statusFormat"
        />

        <el-table-column
          align="center"
          prop="cardName"
          label="操作"
          width="150"
        >
          <template slot-scope="scope">
            <el-button type="text" @click="handDetail(scope.row)"
              >测评明细</el-button
            >
            <el-button
              v-if="isAuth('sys:evaluate:delete')"
              type="text"
              @click="handReomove(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="block">
      <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="queryParams.currentPage"
        :page-sizes="[10, 20, 30, 50, 100]"
        :page-size="queryParams.pageSize"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
      >
      </el-pagination>
    </div>

    <!-- 查看整改通知书 -->
    <cat-book
      v-if="bookDialogVisible2"
      ref="catBook"
      @refreshDataList="catbookChange($event)"
    ></cat-book>

    <!-- 生成整改通知书 -->
    <produce-book
      v-if="bookDialogVisible"
      ref="produceBook"
      @refreshDataList="catbookChange($event)"
    ></produce-book>
  </div>
</template>

<script>
import catBook from '../book/catBook.vue'
import ProduceBook from '../book/produceBook.vue'
export default {
  components: { catBook, ProduceBook },
  props: ['queryParams'],
  data () {
    return {
      showSearch: true,
      questionStatusList: [],
      statusMap: {}, // 添加状态映射对象
      opDate: '',
      idList: [],
      total: 0,
      categoryList: [],
      typeList: [],
      tableData: [],
      flag: true,
      bookDialogVisible: false,
      bookModule: [
        {
          id: '1',
          label: '整改通知书自定义模板1',
          value: '1'
        },
        {
          id: '2',
          label: '整改通知书自定义模板2',
          value: '2'
        }
      ],
      value: '',
      book: {
        name: '',
        code: '',
        checkDate: null,
        dutyDept: '',
        corDept: '',
        question: '',
        result: '',
        replyDate: null,
        isSend: '',
        sendDate: '',
        leaderName: '',
        startDate: null,
        instanceIds: '',
        detailQuestionIds: '',
        questionList: [],
        type: ''
      },
      canVim: true,
      bookTitle: '',
      categoryId: '',
      questionList: [],
      prefix: '',
      bookDialogVisible2: false,
      submitDialogVisible: false
    }
  },
  async mounted () {
    await this.getQuestionStatus('evaluate_question_status')
    this.prefix = window.SITE_CONFIG.baseUrl + '/file'
  },
  async activated () {
    this.handleQuery()
  },
  methods: {
    init (queryParams) {
      this.queryParams = queryParams
      this.handleQuery()
    },

    changModule () { },
    handleQuery () {
      this.flag = true
      this.$http({
        url: this.$http.adornUrl('/evaluate/questionOpInstance/getQuestionList'),
        method: 'get',
        params: this.queryParams
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.tableData = resp.data.obj.records
          this.total = resp.data.obj.total
        } else {
          this.tableData = []
        }
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.queryParams.pageSize = val
      this.queryParams.currentPage = 1
      this.handleQuery()
    },
    // 当前页
    currentChangeHandle (val) {
      this.queryParams.currentPage = val
      this.handleQuery()
    },
    catbookChange (e) {
      this.handleQuery()
    },
    catBook () {
      if (this.idList[0].isGenerateRectificationBook === 'Y') {
        this.bookDialogVisible2 = true
        this.$nextTick(() => {
          this.$refs.catBook.bookDialogVisible2 = true
          this.$refs.catBook.catBook(this.idList[0].bookId)
        })
      } else {
        this.$message({
          type: 'info',
          message: '还没有生成整改通知书'
        })
      }
    },
    produceBook () {
      this.categoryId = ''
      this.book.instanceIds = ''
      this.book.detailQuestionIds = ''
      this.value = '1'
      this.book.startDate = (new Date().getFullYear()) + '-' + (new Date().getMonth() + 1) + '-' + (new Date().getDate())
      this.book.checkDate = this.idList[0].createDate
      this.idList.forEach(data => {
        this.categoryId += data.typeId
        if (data.id) {
          this.book.instanceIds += data.id + ','
        }
        if (data.detailQuestionId) {
          this.book.detailQuestionIds += data.detailQuestionId + ','
        }
      })

      this.bookDialogVisible = true
      this.$nextTick(() => {
        this.$refs.produceBook.bookDialogVisible = true
        this.$refs.produceBook.getQuestionList(this.book.instanceIds, this.book.detailQuestionIds, this.idList[0].createDate)
      })

      // this.bookDialogVisible = true
      // this.bookDialogVisible = '生成整改通知书'
    },
    async getQuestionStatus (code) {
      const { data } = await this.$http({
        url: this.$http.adornUrl('/admin/dict/parent'),
        method: 'get',
        params: { code: code }
      })
      if (data && data.code === 0) {
        this.questionStatusList = data.obj
        // 构建状态映射表
        this.statusMap = {}
        this.questionStatusList.forEach(item => {
          this.statusMap[item.value] = item.name
        })
      } else {
        this.questionStatusList = []
        this.statusMap = {}
      }
    },
    handleSelectionChange (val) {
      this.idList = val
    },
    statusFormat (row, column) {
      const status = row.status + ''
      if (this.statusMap[status]) {
        return this.statusMap[status]
      }
      return row.status
    },
    handReomove (row) {
      this.$confirm(`确认是否彻底删除？`, '删除操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl(`/evaluate/questionResult/removeByIds`),
          method: 'get',
          params: { ids: row.detailQuestionId }
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '删除成功',
              type: 'success'
            })
            this.tableData = []
            this.handleQuery()
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    handDetail (row) {
      this.$router.push({
        path: '/evaluateQuestionDetail',
        query: { id: row.id, detailQuestionId: row.detailQuestionId, categoryId: row.categoryId, positionId: row.optionId }
      })
    },
    handleClose (done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done()
        })
        .catch(_ => { })
    }
  }
  // ,
  // watch: {
  //   queryParams: {
  //     handler (newName, oldName) {
  //       this.queryParams.currentPage = 1
  //       this.queryParams.pageSize = 30
  //       this.handleQuery()
  //     },
  //     // immediate:true代表如果在 wacth 里声明了obj之后，就会立即先去执行里面的handler方法，
  //     immediate: true,
  //     deep: true // 开启深度监听，默认是false

  //   }
  // }

}
</script>

<style lang="scss" scoped>
.el-date-editor--daterange.el-input__inner {
  width: 450%;
}

.text_el-textarea__inner {
  display: block;
  resize: none;
  padding: 5px 15px;
  line-height: 1.5;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
  font-size: inherit;
  color: #606266;
  background-color: #fff;
  background-image: none;
  border: 0px solid #dcdfe6;
  border-radius: 4px;
  -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}
.test_el-input__inner {
  -webkit-appearance: none;
  background-color: #fff;
  background-image: none;
  border-radius: 4px;
  border: 0px solid #dcdfe6;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  color: #606266;
  display: inline-block;
  font-size: inherit;
  height: 40px;
  line-height: 40px;
  outline: 0;
  padding: 0 15px;
  -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  width: 100%;
}

/* 利用穿透，设置input边框隐藏 */
.inputDeep /deep/ .el-input__inner {
  border: 0;
}
/* 如果你的 el-input type 设置成textarea ，就要用这个了 */
.inputDeep /deep/ .el-textarea__inner {
  border: 0;
  resize: none; /* 这个是去掉 textarea 下面拉伸的那个标志，如下图 */
}
// .el-table {
//   width: 99.9% !important; //不让它到临界值，这样可避免自动计算
// }
// /deep/ .el-table__header,
// /deep/ .el-table__body {
//   overflow: hidden;
//   width: 99.9% !important;
// }
</style>
