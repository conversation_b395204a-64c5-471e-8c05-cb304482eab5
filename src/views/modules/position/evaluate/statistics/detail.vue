<template>
    <el-dialog append-to-body
               :visible.sync="viewDetailDialogVisible"
               width="70%"
               title="详情"
               :close-on-click-modal="false">
      <el-table
        :data="tableData"
        style="width: 100%; margin-bottom: 20px"
        border
      >
        <el-table-column prop="evaluateContent" label="测评内容" > </el-table-column>
        <el-table-column prop="score" label="得分" align="center" width="100px"> </el-table-column>
        <el-table-column prop="createTime" label="测评时间" align="center" width="200px">
        </el-table-column>
        <el-table-column prop="userName" label="测评创建人" align="center" width="200px">
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 30, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper"
      >
      </el-pagination>
    </el-dialog>
</template>

<script>
  export default {
    data () {
      return {
        viewDetailDialogVisible: false,
        tableData: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        queryParams: {
          regionCode: '',
          startDate: '',
          orgId: ''
        },
        type: ''
      }
    },
    methods: {
      init (item, type) {
        console.log(item)
        this.viewDetailDialogVisible = true
        this.type = type
        if (this.type === 'u') {
          this.queryParams.orgId = item.identification
        } else {
          this.queryParams.regionCode = item.identification
        }
        this.queryParams.startDate = item.timeliness
        this.pageSize = 10
        this.pageIndex = 1
        this.getDataList()
      },
      getDataList () {
        this.tableData = []
        this.viewDetailDialogVisible = true
        var url = this.$http.adornUrl('/evaluate/largeScreen/region/statistics/detail')
        if (this.type === 'u') {
          url = this.$http.adornUrl('/evaluate/largeScreen/organization/statistics/detail')
        }
        this.$http({
          url: url,
          method: 'post',
          data: this.queryParams
        }).then((resp) => {
          if (resp.data && resp.data.code === 0) {
            this.tableData = resp.data.obj.records
            this.totalPage = resp.data.obj.total
          } else {
            this.tableData = []
            this.totalPage = 0
          }
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val
        this.getDataList()
      }
    }
  }
</script>

<style scoped>
  .el-pagination {
    text-align: right
  }

</style>
