<template>
  <div>
    <div>
      <div class="searchParams">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          label-width="80px"
        >
          <el-form-item prop="orgId">
            <el-select
              @change="handleQuery"
              v-model="queryParams.orgId"
              placeholder="请选择责任单位"
              clearable
            >
              <el-option
                v-for="item in areaList"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" @click="handleQuery">查询</el-button>
            <el-button icon="el-icon-refresh" type="warning" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <!-- 表格列表数据 -->
    <div>
      <el-table
        :data="tableData"
        style="width: 100%; margin-bottom: 20px"
        row-key="identification"
        border
        :tree-props="{ children: 'children' }"
      >
        <el-table-column prop="identificationName" label="街道" > </el-table-column>
        <el-table-column prop="timeliness" label="月份" > </el-table-column>
        <el-table-column prop="totalScore" label="月累计得分" align="center" >
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope" v-if="scope.row.totalScore && scope.row.timeliness">
            <el-button type="text" @click="getDetail(scope.row)"
            >得分详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--
      </el-pagination> -->
    </div>
    <Detail ref="statisticDetail"></Detail>
  </div>
</template>

<script>
  import _ from 'lodash'
  import Detail from './detail'
  export default {
    data () {
      return {
        areaList: [],
        queryParams: {
          orgId: '',
          monthNum: 6
        },
        tableData: [],
        viewDetailDialogVisible: false,
        detailData: []
      }
    },
    components: {
      Detail
    },
    activated () {
      this.getAreaType()
      this.handleQuery()
    },
    methods: {
      // 属性区域列表
      getAreaType () {
        this.$http({
          url: this.$http.adornUrl('/admin/org/all')
        }).then(({ data }) => {
          if (data) {
            this.areaList = data
          } else {
            this.areaList = []
          }
          // if (data && data.code === 0) {
          //   this.areaList = data.obj
          // } else {
          //   this.areaList = []
          // }
        })
      },
      handleQuery () {
        this.tableData = []
        this.$http({
          url: this.$http.adornUrl('/evaluate/largeScreen/organization/statistics'),
          method: 'post',
          data: this.queryParams
        }).then((resp) => {
          if (resp.data && resp.data.code === 0) {
            var tableData = resp.data.obj
            var areaList = _.uniqBy(tableData, 'identificationName')
            this.tableData = areaList.map((item, index) => {
              var child = tableData.filter(e => e.identificationName === item.identificationName)
              var obj = {
                identification: index + 1 + 'n',
                identificationName: item.identificationName,
                children: child,
                totalScore: _.sum(child.map(e => e.totalScore))
              }
              return obj
            })
            // this.tableData = this.areaList.map((item) => {
            //   var child = tableData.filter(e => e.identificationName === item.name)
            //   var obj = {
            //     identification: item.id,
            //     identificationName: item.name,
            //     children: child,
            //     totalScore: _.sum(child.map(e => e.totalScore))
            //   }
            //   return obj
            // })
            console.log(this.tableData)
          } else {
            this.tableData = []
            this.total = 0
          }
        })
      },
      /** 重置按钮操作 */
      resetQuery () {
        this.queryParams.orgId = ''
        this.handleQuery()
      },
      getDetail (item) {
        this.$refs.statisticDetail.init(item, 'u')
      }
    }
  }
</script>

<style scoped>

</style>
