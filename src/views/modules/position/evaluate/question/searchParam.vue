<template>
  <div>
    <div>
      <el-form
        :model="queryParams"
        ref="queryForm"
        label-width="90px"
        v-show="showSearch"
        :inline="true"
      >
        <el-form-item prop="code">
          <el-input
            v-model="queryParams.instanceCode"
            placeholder="测评单号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item prop="instanceName">
          <el-input
            v-model="queryParams.instanceName"
            placeholder="请输入名称查询"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item prop="regionId">
          <el-select
            @change="handleQuery"
            v-model="queryParams.regionId"
            placeholder="请选择测评区域"
            clearable
          >
            <el-option
              v-for="dict in regionList"
              :key="dict.id"
              :label="dict.name"
              :value="dict.code"
            />
          </el-select>
        </el-form-item>

        <el-form-item prop="typeId">
          <el-select
            @change="getTypeList(queryParams.cardId)"
            v-model="queryParams.cardId"
            placeholder="请选择测评卡版本"
            clearable
          >
            <el-option
              v-for="dict in cardList"
              :key="dict.id"
              :label="dict.categoryName"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item prop="typeId">
          <el-select
            @change="getCategoryList(queryParams.typeId)"
            v-model="queryParams.typeId"
            placeholder="请选择测评类型"
            clearable
          >
            <el-option
              v-for="dict in typeList"
              :key="dict.id"
              :label="dict.categoryName"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item prop="categoryId">
          <el-select
            @change="handleQuery"
            v-model="queryParams.categoryId"
            placeholder="请选择测评子类型"
            clearable
          >
            <el-option
              v-for="dict in categoryList"
              :key="dict.id"
              :label="dict.categoryName"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item prop="evaluatorName">
          <el-input
            v-model="queryParams.evaluatorName"
            placeholder="请输入测评员"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item prop="isGenerateRectificationBook">
          <el-select
            @change="handleQuery"
            v-model="queryParams.isGenerateRectificationBook"
            placeholder="请选择整改通知书"
            clearable
          >
            <el-option label="已生成" value="Y" />
            <el-option label="未生成" value="N" />
          </el-select>
        </el-form-item>

        <!-- <el-form-item prop="isRectification">
          <el-select
            @change="handleQuery"
            v-model="queryParams.isRectification"
            placeholder="是否已整改"
            clearable
          >
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </el-form-item> -->

        <el-form-item prop="startTime">
          <el-date-picker
            v-model="queryParams.startTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="开始时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item prop="endTime">
          <el-date-picker
            v-model="queryParams.endTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="结束时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item prop="categoryCode">
          <el-select
            @change="handleQuery"
            v-model="queryParams.categoryCode"
            placeholder="测评分类"
            clearable
          >
            <el-option
              v-for="item in evaCategoryList"
              :key="item.id"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>

        <el-button icon="el-icon-search" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" type="warning" @click="resetForm('queryForm')">重置</el-button>
      </el-form>
    </div>
    <div>
      <evaluateCommon
        v-bind:queryParams="queryParams"
        v-if="flag"
        ref="evaluateCommon"
      ></evaluateCommon>
    </div>
  </div>
</template>

<script>
import evaluateCommon from '../component/evaluteCommon.vue'
export default {
  components: {
    evaluateCommon
  },
  data () {
    return {
      showSearch: true,
      queryParams: {
        instanceCode: '',
        instanceName: '',
        regionId: '',
        typeId: '',
        categoryId: '',
        isGenerateRectificationBook: '',
        isRectification: '',
        status: '',
        startTime: null,
        endTime: null,
        currentPage: 1,
        codeStatus: 'eva_submit',
        pageSize: 30
      },
      questionStatusList: [],
      opDate: '',
      tableData: [],
      idList: [],
      total: 0,
      categoryList: [],
      typeList: [],
      flag: false,
      regionList: [],
      evaCategoryList: [],
      cardList: []
    }
  },
  activated () {
    // this.getTypeList()
    this.getCardList()
    this.handleQuery()
    this.getRegionList()
    // 查询测评分类
    this.getEvaCategoryList('evaluate_detail_category')
    this.getQuestionStatus('evaluate_question_status')
  },
  methods: {
    // 获取测评卡列表
    getCardList () {
      this.$http({
        url: this.$http.adornUrl('/evaluate/cardCategory/cardList'),
        method: 'get'
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.cardList = resp.data.obj
        } else {
          this.cardList = []
        }
      })
    },
    // 根据父id查询下一级
    getCategoryList (parentId) {
      this.$http({
        url: this.$http.adornUrl('/evaluate/cardCategory/getListByParentId'),
        method: 'get',
        params: { parentId: parentId }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.categoryList = resp.data.obj
        } else {
          this.categoryList = []
        }
      })
      this.handleQuery()
    },
    // 获取区域列表
    getRegionList () {
      this.$http({
        url: this.$http.adornUrl('/position/area/top'),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.regionList = data.obj
        } else {
          this.regionList = []
        }
      })
    },
    // 查询按钮
    handleQuery () {
      this.flag = true
      this.$nextTick(() => {
        this.queryParams.currentPage = 1
        this.queryParams.pageSize = 30
        this.$refs.evaluateCommon.init(this.queryParams)
      })
    },
    // 更改页码
    handleSelectionChange (val) {
      this.idList = val
    },
    // 获取状态列表
    getQuestionStatus (code) {
      this.$http({
        url: this.$http.adornUrl('/admin/dict/parent'),
        method: 'get',
        params: { code: code }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.questionStatusList = resp.data.obj
        } else {
          this.questionStatusList = []
        }
      })
    },
    getEvaCategoryList (code) {
      this.$http({
        url: this.$http.adornUrl('/admin/dict/parent'),
        method: 'get',
        params: { code: code }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.evaCategoryList = resp.data.obj
        } else {
          this.evaCategoryList = []
        }
      })
    },
    // 获取一级分类
    getTypeList (parentId) {
      this.$http({
        url: this.$http.adornUrl('/evaluate/cardCategory/getListByParentId'),
        method: 'get',
        params: { parentId: parentId }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.typeList = resp.data.obj
        } else {
          this.typeList = []
        }
      })
    },
    // 重置表单
    resetForm (formName) {
      this.opDate = []
      let status = this.queryParams.status
      this.queryParams = {}
      console.log(status, '***********************************')
      this.$refs[formName].resetFields()
      this.queryParams.status = status
      this.queryParams.codeStatus = 'eva_submit'
      this.handleQuery()
    }
  }

}
</script>

<style>
</style>
