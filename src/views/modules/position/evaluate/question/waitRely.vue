<template>
  <search-param
    v-if="dialogVisible"
    ref="produceBook"
    @refreshDataList="change($event)"
  ></search-param>
</template>

<script>
import searchParam from './searchParam.vue'
export default {
  components: { searchParam },
  data () {
    return {
      dialogVisible: false
    }
  },
  created () {
    this.initData()
  },
  methods: {
    initData () {
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.produceBook.queryParams.status = 1
      })
    },
    change (e) {

    }
  }

}
</script>

<style>
</style>