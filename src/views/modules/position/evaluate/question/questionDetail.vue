<template>
  <div>
    <p align="left" style="font-size: 20px">测评卡明细</p>
    <el-divider></el-divider>

    <p align="left" style="color: rgb(127, 127, 127)">测评要求</p>
    <p align="left">{{ category.need }}</p>
    <br />
    <p align="left" style="margin-top: -5px; color: rgb(127, 127, 127)">
      测评项目
    </p>
    <p align="left">{{ category.remark }}</p>
    <br />

    <!-- 合并表格数据 -->
    <evaluateContent ref="evaluateContent"></evaluateContent>

    <br /><br />
    <div>
      <!-- 平铺的数据 -->
      <evaluateBaseInfo ref="evaluateBaseInfo"></evaluateBaseInfo>
    </div>

    <div style="clear: both"></div>
    <!-- 关联的点位 -->
    <div class="codeInfo">
      <br />
      <p align="left" style="font-size: 20px">关联点位信息</p>
      <el-divider></el-divider>
      <div class="text_size">
        <span style="color: rgb(127, 127, 127)">点位编码</span>
        <br />
        <p style="margin-right: 40px">{{ position.positionCode }}</p>
      </div>
      <div class="text_size">
        <span style="color: rgb(127, 127, 127)">点位名称</span>
        <br />
        <p style="margin-right: 40px">{{ position.positionName }}</p>
      </div>
      <div class="text_size">
        <span style="color: rgb(127, 127, 127)">点位类型</span>
        <br />
        <p style="margin-right: 40px">{{ position.typeName }}</p>
      </div>
      <div class="text_size" @click="openMap(position)">
        <span style="color: rgb(127, 127, 127)">点位地址</span>
        <br />
        <p style="margin-right: 40px; color: #409eff; cursor: pointer">
          {{ position.positionAddress }}<i class="el-icon-map-location"></i>
        </p>
      </div>
    </div>

    <div style="clear: both"></div>
    <div class="codeInfo">
      <p align="left" style="font-size: 20px">测评问题信息</p>
      <el-divider></el-divider>
      <br />

      <el-card
        class="box-card"
        shadow="never"
        v-for="(question, index) in questionList"
        :key="index"
      >
        <p align="left" style="margin-top: 0px"><b>测评内容</b></p>
        <p>{{ question.contentName }}</p>
        <br />
        <p align="left" style="margin-top: 0px"><b>测评项\测评明细</b></p>
        <p>{{ question.evaluateDetailName }}</p>
        <br /><br />
        <p align="left" style="margin-top: 0px"><b>问题描述</b></p>
        <p>{{ question.question }}</p>
        <br />
        <p align="left" style="margin-top: 0px"><b>问题图片</b></p>
        <div class="demo-image">
          <img
            style="width: 100px; margin-left: 10px; margin-top: 5px"
            @click="openImage2(prefix + item.url)"
            v-for="item in question.fileRelList"
            :key="item.id"
            :src="prefix + item.url"
            class="image"
          />
          <el-dialog append-to-body :visible.sync="dialogImageDialogVisible2">
            <img width="100%" :src="dialogImageUrl2" alt="" />
          </el-dialog>
        </div>
        <div>
          <div class="text_size">
            <span style="color: rgb(127, 127, 127)">状态</span>
            <br />
            <p style="margin-right: 40px">
              {{
                question.status === "3"
                  ? "已完成"
                  : question.status === "2"
                  ? "已退回"
                  : question.status === "1"
                  ? "待回复"
                  : "待处理"
              }}
            </p>
          </div>
          <div class="text_size">
            <span style="color: rgb(127, 127, 127)">派单单号</span>
            <br />
            <p
              style="margin-right: 40px; color: #409eff; cursor: pointer"
              @click="orderDetail(question)"
            >
              {{ question.orderCode }}
            </p>
          </div>
          <div class="text_size">
            <span style="color: rgb(127, 127, 127)">整改通知书编号</span>
            <br />
            <p
              v-if="question.bookCode"
              style="margin-right: 40px; color: #409eff; cursor: pointer"
              @click="catBook(question)"
            >
              {{ question.bookCode }}
            </p>
          </div>
          <div class="text_size">
            <span style="color: rgb(127, 127, 127)"
              >苏州工业园区智慧城市智能运行中心(IOC)</span
            >
            <br />
            <p style="margin-right: 40px; color: #409eff; cursor: pointer">
              20206522355
            </p>
          </div>
          <div class="codeInfo">
            <el-button
              v-if="question.status !== '3' && question.status !== '1'"
              @click="dispatch(question)"
              >派单</el-button
            >
            <el-button
              v-if="question.status !== '3'"
              @click="handFinished(question)"
              >处理完成</el-button
            >
            <el-button v-if="question.status !== '3'" @click="produceBook"
              >生成整改通知书</el-button
            >
            <!-- <el-button>删除</el-button> -->
            <el-button @click="questionEdit(question)">编辑</el-button>
            <el-button v-if="question.status !== '3'" @click="openIoc(question)"
              >ioc转发</el-button
            >
          </div>
        </div>
        <div style="clear: both"></div>
        <div v-if="question.status === '3'">
          <br />
          <p align="left" style="margin-top: 0px"><b>处理结果</b></p>

          <p align="left" style="margin-top: 0px"><b>问题回复</b></p>
          <p>{{ question.replyContent }}</p>

          <p align="left" style="margin-top: 0px"><b>回复图片</b></p>
          <div class="demo-image">
            <img
              style="width: 100px; margin-left: 10px; margin-top: 5px"
              @click="openImage(prefix + item.url)"
              v-for="item in question.resultFileList"
              :key="item.id"
              :src="prefix + item.url"
              class="image"
            />
            <el-dialog append-to-body :visible.sync="dialogImageDialogVisible">
              <img width="100%" :src="dialogImageUrl2" alt="" />
            </el-dialog>
          </div>
        </div>
      </el-card>

      <!-- 最下面问题流转记录 -->

      <div v-if="detailQuestionId">
        <br />
        <p align="left" style="font-size: 20px">问题流转跟踪</p>
        <el-divider></el-divider>
        <br />
        <el-table :data="recordData" border stripe style="width: 100%">
          <el-table-column align="center" prop="createName" label="用户" />
          <el-table-column align="center" prop="record" label="操作记录" />
          <el-table-column align="center" prop="remark" label="说明" />
          <el-table-column align="center" prop="createDate" label="操作时间" />
        </el-table>
      </div>
    </div>

    <!-- 派单对话框 -->
    <el-dialog
      title="派单"
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="dispatchDialogVisible"
    >
      <el-form :model="order" label-width="80px">
        <el-form-item label="任务名称">
          <el-input v-model="order.orderName" style="width: 100%"></el-input>
        </el-form-item>
        <el-form-item label="任务明细">
          <el-input v-model="order.contentDesc" style="width: 100%"></el-input>
        </el-form-item>
        <el-form-item label="任务图片">
          <img
            style="
              width: 100px;
              heigth: 100px;
              margin-left: 10px;
              margin-top: 10px;
            "
            v-for="(file, index) in order.fileUrlList"
            :key="index"
            :src="prefix + file"
            class="image"
            @click="openImage3(prefix + file)"
          />
          <el-dialog append-to-body :visible.sync="dialogImageDialogVisible3">
            <img width="100%" :src="dialogImageUrl2" alt="" />
          </el-dialog>
        </el-form-item>
        <el-form-item label="所属区域">
          <el-select
            @change="changeRegion(order.regionName)"
            v-model="order.regionName"
            placeholder="所属区域"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="dict in regionList"
              :key="dict.id"
              :label="dict.name"
              :value="dict.code"
            />
          </el-select>
          <!-- <el-input v-model="order.regionName" style="width: 90%"></el-input> -->
        </el-form-item>
        <!-- <el-form-item label="所属社区">
          <el-input v-model="order.communityName" style="width: 90%"></el-input>
        </el-form-item>
        <el-form-item label="所属网格">
          <el-input v-model="order.gridName" style="width: 90%"></el-input>
        </el-form-item> -->
        <el-form-item label="详细地址">
          <el-input v-model="order.orderAddress" style="width: 100%"></el-input>
        </el-form-item>
        <el-form-item label="开始时间">
          <el-date-picker
            @change="changeEndTime(order.startTime)"
            style="width: 100%"
            v-model="order.startTime"
            align="right"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择开始日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间">
          <el-date-picker
            style="width: 100%"
            v-model="order.endTime"
            align="right"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择结束日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="参与人">
          <!-- <div style="border: 1 solid">
            <p style="width: 90%; cursor: pointer" @click="selectUser">
              {{ order.contentDesc }}
            </p>
          </div> -->

          <el-input
            v-model="order.contentDesc2"
            @focus="selectUser"
            style="width: 100%"
          >
            <i
              @click="selectUser"
              slot="suffix"
              style="margin-right: 5px"
              class="el-icon-arrow-down"
            ></i>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :disabled="orderDisabled" @click="saveOrder"
            >确 定</el-button
          >
          <el-button @click="dispatchDialogVisible = false">取 消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 处理完成 -->
    <el-dialog
      title="处理完成"
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="handFinishedDialogVisible"
    >
      <el-form :model="order" label-width="120px">
        <el-form-item label="快速选择回复">
          <el-select
            v-model="successParams.replyContent2"
            placeholder="请选择状态"
            clearable
            style="width: 90%"
            size="small"
            @change="handChangeReply(successParams.replyContent2)"
          >
            <el-option
              v-for="item in questionDictList"
              :key="item.value"
              :label="item.name"
              :value="item.name"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="回复">
          <el-input
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
            v-model="successParams.replyContent"
            style="width: 90%"
          ></el-input>
        </el-form-item>
        <el-form-item label="图片">
          <el-upload
            :action="prefix + '/oss/upload?serverCode=LocalServer'"
            multiple
            :file-list="fileList"
            list-type="picture-card"
            :on-preview="handlePictureCardPreview"
            :on-remove="handleRemove"
            :on-success="handleAvatarSuccess"
            :headers="importToken"
          >
            <i class="el-icon-plus"></i>
          </el-upload>

          <el-dialog append-to-body :visible.sync="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="" />
          </el-dialog>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :disabled="handSuccessDisabled"
            @click="handSuccess"
            >确 定</el-button
          >
          <el-button @click="handFinishedDialogVisible = false"
            >取 消</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 选择部门，人员 -->
    <el-dialog
      append-to-body
      class="user-dialog"
      title="选择参与人"
      :close-on-click-modal="false"
      :visible.sync="selectUserDialogVisible"
    >
      <el-row :gutter="12">
        <el-col :span="12">
          <el-card shadow="never">
            <div slot="header" class="clearfix">
              <span>已选</span
              ><span style="margin-left: 15px; color: blue">{{ userNum }}</span>
            </div>
            <el-scrollbar style="height: 350px">
              <el-tree
                :data="treeData"
                :props="props"
                node-key="id"
                ref="dictTree"
                @current-change="currentChangeHandle"
                :default-expand-all="true"
                :highlight-current="true"
                :expand-on-click-node="false"
                style="height: 100%"
              >
              </el-tree>
            </el-scrollbar>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="never">
            <div slot="header" class="clearfix">
              <span>全部</span
              ><span style="margin-left: 15px; color: blue">{{ allNum }}</span>
            </div>
            <el-scrollbar style="height: 350px">
              <el-checkbox-group v-model="list" @change="getSelectUser">
                <el-checkbox
                  style="margin-top: 5px; display: block"
                  v-for="item in userList"
                  :label="item"
                  :key="item.id"
                  >{{ item.name }}</el-checkbox
                >
              </el-checkbox-group>
            </el-scrollbar>
          </el-card>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitUser">确 定</el-button>
        <el-button @click="selectUserDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- ioc转发 -->
    <el-dialog
      title="ioc转发"
      :close-on-click-modal="false"
      append-to-body
      :visible.sync="iocDialogVisible"
    >
      <el-form :model="order" label-width="80px">
        <el-form-item label="工单标题">
          <el-input v-model="iocInfo.orderTitle" style="width: 90%"></el-input>
        </el-form-item>
        <el-form-item label="工单内容">
          <el-input
            v-model="iocInfo.orderContent"
            style="width: 90%"
          ></el-input>
        </el-form-item>
        <el-form-item label="附件">
          <img
            style="
              width: 100px;
              heigth: 100px;
              margin-left: 10px;
              margin-top: 10px;
            "
            v-for="(file, index) in iocInfo.files"
            :key="index"
            :src="file.fileUrl"
            class="image"
          />
        </el-form-item>
        <el-form-item label="所属区域">
          <el-input v-model="iocInfo.regionName" style="width: 90%"></el-input>
        </el-form-item>
        <!-- <el-form-item label="所属社区">
          <el-input
            v-model="iocInfo.communityName"
            style="width: 90%"
          ></el-input>
        </el-form-item>
        <el-form-item label="所属网格">
          <el-input v-model="iocInfo.gridName" style="width: 90%"></el-input>
        </el-form-item> -->
        <el-form-item label="详细地址">
          <el-input
            v-model="iocInfo.reportAddress"
            style="width: 90%"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :disabled="iocDisabled" @click="ioc()"
            >确 定</el-button
          >
          <el-button @click="iocDialogVisible = false">取 消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 引入地图 -->
    <mapDiaglog
      v-if="mapDataVisible"
      ref="mapDiaglog"
      @change="getMapData($event)"
    ></mapDiaglog>

    <!-- 生成整改通知书 -->
    <produce-book
      v-if="bookDialogVisible"
      ref="produceBook"
      @refreshDataList="catbookChange"
    ></produce-book>

    <!-- 生成整改通知书 -->
    <!-- 查看整改通知书 -->
    <cat-book
      v-if="bookDialogVisible2"
      ref="catBook"
      @refreshDataList="catbookChange"
    ></cat-book>

    <!-- 编辑问题 -->
    <question-detail-edit
      v-if="questionEditVisible"
      ref="questionEdit"
      @refreshDataList="getEvaluateContent"
    ></question-detail-edit>
  </div>
</template>

<script>
import evaluateContent from '../component/evaluateContent.vue'
import evaluateBaseInfo from '../component/evaluateBaseInfo.vue'
import mapDiaglog from '../../position/componet/mapDiaglogDetails.vue'
import ProduceBook from '../book/produceBook.vue'
import CatBook from '../book/catBook.vue'
import QuestionDetailEdit from '../component/questionDetailEdit.vue'
export default {
  components: {
    evaluateContent,
    evaluateBaseInfo,
    mapDiaglog,
    ProduceBook,
    CatBook,
    QuestionDetailEdit
  },
  data () {
    return {
      flag: false,
      flag2: false,
      tableMessageData: [],
      category: {
        id: '',
        need: '',
        remark: ''
      },
      instanceData: {
        areaName: '',
        regionName: '',
        gridName: '',
        gridCode: '',
        address: ''
      },

      id: '',
      categoryId: '',
      detailQuestionId: '',
      questionList: [],
      recordData: [],
      dispatchDialogVisible: false,
      handFinishedDialogVisible: false,
      order: {
        orderName: '',
        contentDesc: '',
        contentDesc2: '',
        relationType: '2',
        regionCode: '',
        regionName: '',
        communityCode: '',
        communityName: '',
        gridCode: '',
        gridName: '',
        orderAddress: '',
        startTime: null,
        endTime: null,
        orgIdLinked: '',
        orgName: '',
        fileUrlList: [],

        relationList: [],

        instanceId: '',
        instanceCode: '',
        questionId: '',
        orderLocation: null
      },
      prefix: '',
      successParams: {
        id: '',
        instanceId: '',
        instanceCode: '',
        replyContent: '',
        replyContent2: '',
        fileList: []
      },
      fileList: [],
      uploadData: {
        serverCode: 'LocalServer'
      },
      importToken: {
        'Authorization': ''
      },
      questionDictList: [],
      dialogVisible: false,
      dialogImageUrl: '',
      selectUserDialogVisible: false,
      // 已选
      userNum: 0,
      // 全部
      allNum: 0,
      treeData: [],
      props: {
        key: 'id',
        label: 'name',
        children: 'children'
      },
      userList: [],
      list: [],
      bookDialogVisible: false,
      bookDialogVisible2: false,
      value: '1',
      book: {},
      fileUrlList: [],
      iocDialogVisible: false,
      iocInfo: {
        messageId: null,
        workOrderNo: null,
        files: [],
        categoryCode: null,
        category: null,
        sourceCode: null,
        source: null,
        workTypeCode: null,
        workType: null,
        typeCode: null,
        type: null,
        orderTitle: null,
        orderContent: null,
        reportWayCode: null,
        reportWay: null,
        reportPersonId: null,
        reportPersonName: null,
        reportPersonRoleId: null,
        reportPersonRoleName: null,
        reportPersonGenderCode: null,
        reportPersonGender: null,
        reportPersonPhone: null,
        reportPersonEmail: null,
        reportPersonIdentityCard: null,
        reportPersonIdentityIfSecret: null,
        reportAddress: null,
        reportLongitude84: null,
        reportLatitude84: null,
        reportThumbnail: null,
        instanceId: null,
        questionId: null,
        areaName: null,
        gridName: null,
        gridCode: null,
        regionName: null
      },
      Files: [],
      position: {

      },
      mapData: {
        address: '',
        lng: '',
        lat: '',
        dialogVisible: true
      },
      mapDataVisible: false,
      dialogImageDialogVisible: false,
      dialogImageDialogVisible2: false,
      dialogImageDialogVisible3: false,
      dialogImageUrl2: '',
      regionList: [],

      // 懒加载
      orderDisabled: false,
      handSuccessDisabled: false,
      iocDisabled: false,
      itemDetail: {
        handTime: null
      },
      questionEditVisible: false
    }
  },
  activated () {
    this.prefix = window.SITE_CONFIG.baseUrl + '/file'
    this.id = this.$route.query.id
    this.categoryId = this.$route.query.categoryId
    this.detailQuestionId = this.$route.query.detailQuestionId
    this.queryEvaluateData()
    this.queryCategory()
    this.queryInstanceData()
    this.getEvaluateContent()
    this.queryPosition()
    this.queryFlowList()
    this.queryQuestionDictList('orderQuestionReply')
    this.importToken.Authorization = this.getCookigetCookie('Authorization').replace('bearer%20', 'Bearer ')
    this.getRegionList()
    // this.dialogVisibleIsFalse()
  },
  methods: {
    getCookigetCookie (param) {
      var cParam = ''
      if (document.cookie.length > 0) {
        var arr = document.cookie.split('; ') // 这里显示的格式需要切割一下自己可输出看下
        for (var i = 0; i < arr.length; i++) {
          var arr2 = arr[i].split('=') // 再次切割
          // 判断查找相对应的值
          if (arr2[0] === param) {
            cParam = arr2[1]
            // 保存到保存数据的地方
          }
        }
        return cParam
      }
    },
    getRegionList () {
      this.$http({
        url: this.$http.adornUrl('/position/area/top'),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.regionList = data.obj
        } else {
          this.regionList = []
        }
      })
    },
    catbookChange () {
      this.getEvaluateContent()
    },
    // 查询点位信息
    queryPosition () {
      this.$http({
        url: this.$http.adornUrl('/position/position/detail'),
        method: 'get',
        params: {
          id: this.$route.query.positionId
        }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.position = resp.data.obj
        } else {
          this.position = {}
        }
      })
    },
    openMap (data) {
      this.mapData.address = data.positionAddress
      this.mapData.dialogVisible = false
      this.mapDataVisible = true
      if (data.positionMap) {
        this.mapData.lat = data.positionMap.split(',')[0]
        this.mapData.lng = data.positionMap.split(',')[1]
      }
      this.$nextTick(() => {
        this.$refs.mapDiaglog.visible = true
        this.$refs.mapDiaglog.initMap(this.mapData)
      })
    },

    queryQuestionDictList (code) {
      this.$http({
        url: this.$http.adornUrl('/admin/dict/parent'),
        method: 'get',
        params: {
          code
        }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.questionDictList = resp.data.obj
        } else {
          this.questionDictList = []
        }
      })
    },
    async queryEvaluateData () {
      this.$refs.evaluateContent.init(this.id, this.detailQuestionId)

      this.flag = true
    },
    queryCategory () {
      this.$http({
        url: this.$http.adornUrl('/evaluate/cardCategory/getById'),
        method: 'get',
        params: { id: this.categoryId }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.category = resp.data.obj
        }
      })
    },
    queryInstanceData () {
      this.$http({
        url: this.$http.adornUrl('/evaluate/questionOpInstance'),
        method: 'get',
        params: { id: this.id }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.instanceData = resp.data.obj
          this.instanceData.areaName = resp.data.obj.areaName
          this.instanceData.gridName = resp.data.obj.gridName
          this.instanceData.gridCode = resp.data.obj.gridCode
          this.instanceData.regionName = resp.data.obj.regionName
          this.flag2 = true
          this.$refs.evaluateBaseInfo.init(this.$route.query.id)
        }
      })
    },
    initBaseInfo () {
      // this.$nextTick(() => {
      //   this.$refs.evaluateBaseInfo.formData = this.instanceData
      // })
    },
    handChangeReply (row) {
      this.successParams.replyContent = row
    },
    getEvaluateContent () {
      this.$http({
        url: this.$http.adornUrl('/evaluate/questionOpInstance/getQuestionListAndFile'),
        method: 'get',
        params: { id: this.id, detailQuestionId: this.detailQuestionId }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.questionList = resp.data.obj
        }
      })
    },
    queryFlowList () {
      this.$http({
        url: this.$http.adornUrl('/evaluate/questionFlow/list'),
        method: 'get',
        params: { questionId: this.detailQuestionId }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.recordData = resp.data.obj
        } else {
          this.recordData = []
        }
      })
    },
    async dispatch (row) {
      this.orderDisabled = false
      if (row.fileRelList) {
        for (let index in row.fileRelList) {
          this.order.fileUrlList[index] = row.fileRelList[index].url
        }
      }
      this.order.questionId = row.id
      this.order.contentDesc2 = ''
      this.order.regionCode = this.instanceData.regionCode
      this.order.regionName = this.instanceData.regionName
      this.order.communityName = this.instanceData.areaName
      this.order.gridCode = this.instanceData.gridCode
      this.order.gridName = this.instanceData.gridName
      this.order.orderLocation = row.latLng
      if (row.address) {
        this.order.orderAddress = row.address
      } else if (this.instanceData.sysLocationAddress) {
        this.order.orderAddress = this.instanceData.sysLocationAddress
      } else if (this.instanceData.address) {
        this.order.orderAddress = this.instanceData.address
      }

      this.order.instanceId = this.$route.query.id
      this.order.instanceCode = this.instanceData.instanceCode
      this.order.orderName = row.contentName
      this.order.contentDesc = row.question

      this.order.startTime = new Date()
      this.dispatchDialogVisible = true
      this.getDetailInfoById(row.evaluateDetailId)
      this.getOrgTreeList()
    },
    getDetailInfoById (id) {
      this.itemDetail = {}
      this.$http({
        url: this.$http.adornUrl('/evaluate/itemDetail'),
        method: 'get',
        params: { id: id }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.itemDetail = resp.data.obj
        } else {
          this.itemDetail = {}
        }
        if (this.itemDetail && this.itemDetail.handTime) {
          this.order.endTime = new Date(new Date(this.order.startTime).getTime() + 1000 * 60 * 60 * 24 * this.itemDetail.handTime)
        } else {
          this.order.endTime = this.order.startTime
        }
      })
    },
    catBook (row) {
      this.bookDialogVisible2 = true
      this.$nextTick(() => {
        this.$refs.catBook.bookDialogVisible2 = true
        this.$refs.catBook.catBook(row.bookId)
      })
    },
    filterNode (value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    saveOrder () {
      this.orderDisabled = true
      this.$http({
        url: this.$http.adornUrl('/evaluate/questionFlow/dispatch'),
        method: 'post',
        data: this.order
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.dispatchDialogVisible = false
          this.getEvaluateContent()
          this.queryFlowList()
          this.$message({
            message: '派单成功',
            type: 'success'
          })
        } else {
          this.$message.error(resp.data.msg)
        }
        setTimeout(() => {
          this.orderDisabled = false
        }, 1000)
      })
    },
    handSuccess () {
      this.fileList.forEach(data => {
        data.path = data.url.replace(window.SITE_CONFIG.baseUrl + '/file', '')
        this.fileUrlList.push(data.path)
      })
      this.successParams.fileList = this.fileUrlList

      this.handSuccessDisabled = true
      this.$http({
        url: this.$http.adornUrl('/evaluate/questionFlow/success'),
        method: 'post',
        data: this.successParams
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.handFinishedDialogVisible = false
          this.getEvaluateContent()
          this.queryFlowList()
          this.$message({
            message: '操作成功',
            type: 'success'
          })
        } else {
          this.$message.error(resp.data.msg)
        }
        setTimeout(() => {
          this.handSuccessDisabled = false
        }, 1000)
      })
    },

    handFinished (row) {
      this.successParams.id = ''
      this.successParams.instanceId = ''
      this.successParams.instanceCode = ''
      this.handFinishedDialogVisible = true
      this.successParams.id = row.id
      this.successParams.instanceId = this.id
      this.successParams.instanceCode = this.instanceData.code
    },
    handleRemove (file, fileList) {
      this.fileList.splice(this.fileList.indexOf(file.url), 1)
    },
    handlePictureCardPreview (file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleAvatarSuccess (res, file) {
      const a = {
        name: res.obj.name,
        url: window.SITE_CONFIG.baseUrl + '/file' + res.obj.path
      }
      this.fileList.push(a)
      this.dialogImageUrl = window.SITE_CONFIG.baseUrl + '/file' + res.obj.path
    },
    selectUser () {
      this.selectUserDialogVisible = true
    },
    getOrgTreeList () {
      // this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/admin/org/tree'),
        method: 'get',
        params: this.$http.adornParams({ level: 1 })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.treeData = data.obj
        } else {
          this.treeData = []
        }
        this.data = {}
      })
    },
    currentChangeHandle (data, node) {
      this.$http({
        url: this.$http.adornUrl('/admin/user/getUserByOrgCode'),
        method: 'get',
        params: { code: data.code }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.userList = resp.data.obj
          this.allNum = this.userList.length
        }
      })
    },
    getSelectUser () {
      this.userNum = this.list.length
    },
    submitUser () {
      this.selectUserDialogVisible = false
      this.order.relationList = []
      if (this.list) {
        this.list.forEach(data => {
          const orderRelation = {
            leaderId: data.id,
            leader: data.name,
            orgId: data.org_id,
            orgName: data.orgName
          }
          this.order.relationList.push(orderRelation)
          this.order.contentDesc2 += data.name + ','
        })
      }
    },
    openIoc (row) {
      this.iocDialogVisible = true
      this.iocInfo = {}
      this.iocInfo.messageId = this.$route.query.id
      this.iocInfo.instanceId = this.$route.query.id
      this.iocInfo.questionId = row.id
      this.iocInfo.workOrderNo = row.id
      this.Files = []
      if (row.fileRelList) {
        row.fileRelList.forEach(data => {
          const file = {
            fileName: data.imageName,
            fileUrl: this.prefix + data.url
          }
          this.Files.push(file)
        })
      }
      this.iocInfo.files = this.Files
      this.iocInfo.orderTitle = row.contentName
      this.iocInfo.orderContent = row.question
      this.iocInfo.reportAddress = this.instanceData.address
      this.iocInfo.areaName = this.instanceData.areaName
      this.iocInfo.gridCode = this.instanceData.gridCode
      this.iocInfo.regionName = this.instanceData.regionName
    },
    ioc () {
      this.iocDisabled = true
      this.$http({
        url: this.$http.adornUrl('/evaluate/questionFlow/ioc'),
        method: 'post',
        data: this.iocInfo
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.dispatchDialogVisible = false
          this.getEvaluateContent()
          this.queryFlowList()
          this.$message({
            message: '派单成功',
            type: 'success'
          })
        } else {
          this.$message.error(resp.data.msg)
        }
        setTimeout(() => {
          this.iocDisabled = false
        }, 1000)
      })
    },
    orderDetail (row) {
      this.$router.push({
        path: '/orderDetail',
        query: { id: row.orderId }
      })
    },
    handleClose (done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done()
        })
        .catch(_ => { })
    },
    // 图片预览
    openImage (url) {
      this.dialogImageDialogVisible = true
      this.dialogImageUrl2 = url
    },
    openImage2 (url) {
      this.dialogImageDialogVisible2 = true
      this.dialogImageUrl2 = url
    },
    openImage3 (url) {
      this.dialogImageDialogVisible2 = true
      this.dialogImageUrl2 = url
    },
    changeRegion (regionName) {
      this.regionList.forEach(data => {
        if (data.regionName + '' === data.regionName + '') {
          this.order.regionCode = data.regionCode
        }
      })
    },
    produceBook () {
      this.bookDialogVisible = true
      this.$nextTick(() => {
        this.$refs.produceBook.bookDialogVisible = true
        this.$refs.produceBook.getQuestionList(this.$route.query.id, this.$route.query.detailQuestionIds, this.instanceData.createDate)
      })
    },
    // 派单开始时间修改后，结束时间也跟着修改
    changeEndTime (date) {
      if (date) {
        if (this.itemDetail && this.itemDetail.handTime) {
          this.order.endTime = new Date(new Date(this.order.startTime).getTime() + 1000 * 60 * 60 * 24 * this.itemDetail.handTime)
        } else {
          this.order.endTime = this.order.startTime
        }
      }
    },
    questionEdit (question) {
      this.questionEditVisible = true
      this.$nextTick(() => {
        this.$refs.questionEdit.init(question)
      })
    }
  }

}
</script>

<style lang="scss" scoped>
.codeInfo {
  width: 100%;
  clear: both;
}
.box-card {
  background-color: rgb(242, 242, 242);
  margin-top: 15px;
}
.user-dialog {
  /deep/.el-card {
    .el-scrollbar {
      .el-scrollbar__wrap {
        overflow: hidden scroll !important;
      }
    }
    .el-tree-node__content {
      height: 30px;
      line-height: 30px;
    }
    .el-checkbox-group {
      display: flex;
      flex-direction: column;
      .el-checkbox {
        line-height: 30px;
      }
    }
  }
}
.el-divider {
  margin: 8px 0;
  background: 0 0;
  border-top: 1px solid #e6ebf5;
}
</style>
