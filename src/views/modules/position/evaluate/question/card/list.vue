<template>
  <div>
    <div>
      <el-form
        :model="queryParams"
        ref="queryForm"
        label-width="90px"
        v-show="showSearch"
        :inline="true"
      >
        <el-form-item prop="instanceCode">
          <el-input
            v-model="queryParams.instanceCode"
            placeholder="测评单号"
            clearable
            @keyup.enter.native="search"
          />
        </el-form-item>

        <el-form-item prop="instanceName">
          <el-input
            v-model="queryParams.instanceName"
            placeholder="请输入名称查询"
            clearable
            @keyup.enter.native="search"
          />
        </el-form-item>

        <el-form-item prop="regionId">
          <el-select
            @change="search"
            v-model="queryParams.regionId"
            placeholder="请选择测评区域"
            clearable
          >
            <el-option
              v-for="dict in regionList"
              :key="dict.id"
              :label="dict.name"
              :value="dict.code"
            />
          </el-select>
        </el-form-item>

        <el-form-item prop="cardId">
          <el-select
            @change="getTypeList(queryParams.cardId)"
            v-model="queryParams.cardId"
            placeholder="请选择测评卡版本"
            clearable
          >
            <el-option
              v-for="dict in cardList"
              :key="dict.id"
              :label="dict.categoryName"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item prop="typeId">
          <el-select
            @change="getCategoryList(queryParams.typeId)"
            v-model="queryParams.typeId"
            placeholder="请选择测评类型"
            clearable
          >
            <el-option
              v-for="dict in typeList"
              :key="dict.id"
              :label="dict.categoryName"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item prop="categoryId">
          <el-select
            @change="search"
            v-model="queryParams.categoryId"
            placeholder="请选择测评子类型"
            clearable
          >
            <el-option
              v-for="dict in categoryList"
              :key="dict.id"
              :label="dict.categoryName"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item prop="evaluatorName">
          <el-input
            v-model="queryParams.evaluatorName"
            placeholder="请输入测评员"
            clearable
            @keyup.enter.native="search"
          />
        </el-form-item>

        <!-- <el-form-item prop="isRectification">
          <el-select
            @change="search"
            v-model="queryParams.isRectification"
            placeholder="请选择测评子类型"
            clearable
          >
            <el-option label="是" value="0" />
            <el-option label="否" value="1" />
          </el-select>
        </el-form-item> -->

        <el-form-item prop="status">
          <el-select
            @change="search"
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
          >
            <el-option
              v-for="item in questionStatusList"
              :key="item.id"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="startTime">
          <el-date-picker
            v-model="queryParams.startTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="开始时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item prop="endTime">
          <el-date-picker
            v-model="queryParams.endTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="结束时间"
          >
          </el-date-picker>
        </el-form-item>

        <el-button icon="el-icon-search" @click="search">查询</el-button>
        <el-button icon="el-icon-refresh" type="warning" @click="resetForm('queryForm')">重置</el-button>
      </el-form>
    </div>
    <div>
      <el-table
        :data="tableData"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="50"
          align="center"
        ></el-table-column>
        <el-table-column prop="instanceCode" label="测评单号" align="center">
          <template slot-scope="scope">
            <span style="color: #409eff; cursor: pointer" @click="handDetail(scope.row)">{{
              scope.row.instanceCode
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="instanceName" label="名称" align="center">
          <!-- <template slot-scope="scope">
            <span style="color: #409eff; cursor: pointer">{{
              scope.row.instanceName
            }}</span>
          </template> -->
        </el-table-column>
        <el-table-column prop="regionName" label="测评区域" align="center">
          <!-- <template slot-scope="scope">
            <span style="color: #409eff; cursor: pointer">{{
              scope.row.regionName
            }}</span>
          </template> -->
        </el-table-column>
        <!-- <el-table-column align="center" prop="areaName" label="测评社区" /> -->
        <el-table-column align="center" prop="typeName" label="测评类型" />
        <el-table-column
          align="center"
          prop="categoryName"
          label="测评子类型"
        />
        <el-table-column align="center" prop="evaluatorName" label="测评员" />
        <el-table-column align="center" prop="evaluatorDate" label="测评时间" />
        <el-table-column
          align="center"
          prop="cardName"
          label="测评卡版本"
          show-overflow-tooltip
        />
        <el-table-column
          align="center"
          prop="isGenerateRectificationBook"
          label="整改通知书"
        >
          <template slot-scope="scope">
            <span>{{
              scope.row.isGenerateRectificationBook === "Y"
                ? "已生成"
                : "未生成"
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="isRectification"
          label="是否已整改"
        >
          <template slot-scope="scope">
            <span>{{
              scope.row.isGenerateRectificationBook === "1" ? "是" : "否"
            }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column
          align="center"
          prop="status"
          label="状态"
          :formatter="statusFormat"
        /> -->
        <el-table-column
          align="center"
          prop="cardName"
          label="操作"
          width="125"
        >
          <template slot-scope="scope">
            <el-button type="text" @click="handDetail(scope.row)"
              >测评明细</el-button
            >
            <el-button type="text" @click="handReomove(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="block">
      <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="queryParams.currentPage"
        :page-sizes="[10, 20, 30, 50, 100]"
        :page-size="queryParams.pageSize"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      showSearch: true,
      queryParams: {
        instanceCode: '',
        instanceName: '',
        regionId: '',
        typeId: '',
        categoryId: '',
        codeStatus: 'eva_submit',
        isGenerateRectificationBook: '',
        isRectification: '',
        status: '',
        startTime: null,
        endTime: null,
        cardId: null,
        currentPage: 1,
        pageSize: 30
      },
      questionStatusList: [],
      opDate: '',
      tableData: [],
      idList: [],
      total: 0,
      categoryList: [],
      typeList: [],
      regionList: [],
      cardList: []
    }
  },
  created () {
    this.handleQuery()
    // this.getTypeList()
    this.getCardList()
    this.getRegionList()
    this.getQuestionStatus('evaluate_question_status')
  },
  methods: {
    // 获取测评卡列表
    getCardList () {
      this.$http({
        url: this.$http.adornUrl('/evaluate/cardCategory/cardList'),
        method: 'get'
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.cardList = resp.data.obj
        } else {
          this.cardList = []
        }
      })
    },
    search () {
      this.queryParams.currentPage = 1
      this.queryParams.pageSize = 30
      this.handleQuery()
    },
    // 获取区域列表
    getRegionList () {
      this.$http({
        url: this.$http.adornUrl('/position/area/top'),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.regionList = data.obj
        } else {
          this.regionList = []
        }
      })
    },
    // 查询
    handleQuery () {
      if (this.opDate) {
        this.queryParams.startTime = this.opDate[0]
        this.queryParams.endTime = this.opDate[1]
      }
      this.$http({
        url: this.$http.adornUrl('/evaluate/questionOpInstance/getList'),
        method: 'get',
        params: this.queryParams
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.tableData = resp.data.obj.records
          this.total = resp.data.obj.total
        } else {
          this.tableData = []
        }
      })
    },
    // 获取一级分类
    getTypeList (parentId) {
      this.$http({
        url: this.$http.adornUrl('/evaluate/cardCategory/getListByParentId'),
        method: 'get',
        params: { parentId: parentId }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.typeList = resp.data.obj
        } else {
          this.typeList = []
        }
      })
      this.search()
    },
    getCategoryList (parentId) {
      this.$http({
        url: this.$http.adornUrl('/evaluate/cardCategory/getListByParentId'),
        method: 'get',
        params: { parentId: parentId }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.categoryList = resp.data.obj
        } else {
          this.categoryList = []
        }
      })
      this.search()
    },
    handleSelectionChange (val) {
      this.idList = val
    },
    getQuestionStatus (code) {
      this.$http({
        url: this.$http.adornUrl('/admin/dict/parent'),
        method: 'get',
        params: { code: code }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.questionStatusList = resp.data.obj
        } else {
          this.questionStatusList = []
        }
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.queryParams.pageSize = val
      this.queryParams.currentPage = 1
      this.handleQuery()
    },
    // 当前页
    currentChangeHandle (val) {
      this.queryParams.currentPage = val
      this.handleQuery()
    },
    resetForm (formName) {
      this.$refs[formName].resetFields()
      this.handleQuery()
    },
    statusFormat (row, column) {
      this.questionStatusList.forEach(data => {
        if (data.value + '' === row.status + '') {
          row.status = data.name
        }
      })
      return row.status
    },
    handReomove (row) {
      this.$confirm(`确认是否彻底删除？`, '删除操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl(`/evaluate/questionOpInstance/removeByIds`),
          method: 'get',
          params: { ids: row.id }
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '删除成功',
              type: 'success'
            })
            this.tableData = []
            this.handleQuery()
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    handDetail (row) {
      this.$router.push({
        path: '/evaluateQuestionDetail',
        query: { id: row.id, detailQuestionId: row.detailQuestionId, categoryId: row.categoryId, positionId: row.optionId }
      })
    }
  }

}
</script>

<style lang="scss" scoped>
.el-date-editor--daterange.el-input__inner {
  width: 450%;
}
.el-table {
  width: 99.9% !important; //不让它到临界值，这样可避免自动计算
}
/deep/ .el-table__header,
/deep/ .el-table__body {
  overflow: hidden;
  width: 99.9% !important;
}
</style>
