<template>
  <div>
    <div>
      <!-- <h2 align="left">测评卡管理</h2> -->
      <div class="searchParams">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
          label-width="80px"
        >
          <el-form-item prop="name">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入名称"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item prop="status">
            <el-select
              @change="handleQuery"
              v-model="queryParams.status"
              placeholder="请选择状态"
              clearable
            >
              <el-option
                v-for="item in optionStatus"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" @click="handleQuery">查询</el-button>
            <el-button icon="el-icon-refresh" type="warning" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="addRight">
        <el-button type="text" @click="openAddCard">新增测评卡</el-button>
      </div>
    </div>
    <!-- 表格列表数据 -->
    <div>
      <el-table
        :data="tableData"
        style="width: 100%; margin-bottom: 20px"
        row-key="id"
        border
        default-expand-all
        :tree-props="{ children: 'children' }"
      >
        <el-table-column prop="name" label="分类" sortable> </el-table-column>
        <el-table-column prop="status" label="状态" align="center" sortable>
          <template slot-scope="scope">
            <span>{{ scope.row.status == 0 ? "启用中" : "停用中" }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="rank" label="排序" align="center" sortable>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button
              type="text"
              v-if="scope.row.isCard !== 'Y' && scope.row.level + '' !== '2'"
              @click="goDetail(scope.row)"
              >测评明细</el-button
            >
            <el-button
              type="text"
              v-if="scope.row.isCard !== 'Y' && scope.row.level + '' !== '3'"
              @click="openAddChildCategory(scope.row)"
              >添加子分类</el-button
            >
            <el-button
              size="text"
              v-if="scope.row.isCard === 'Y'"
              @click="openAddCategory(scope.row)"
              >新增大类</el-button
            >
            <el-button type="text" @click="editCategory(scope.row)"
              >编辑</el-button
            >
            <el-button type="text" @click="removeCard(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <!--
      </el-pagination> -->
    </div>
    <div class="block">
      <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="queryParams.currentPage"
        :page-sizes="[10, 20, 30, 50, 100]"
        :page-size="queryParams.pageSize"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
      >
      </el-pagination>
    </div>
    <!-- 新增/修改 测评卡 -->
    <el-dialog
    append-to-body
      :title="title"
      :visible.sync="addCardDialogVisible"
      width="30%"
      :close-on-click-modal="false"
    >
      <el-form :model="card" ref="cardForm" :rules="rules" label-width="100px">
        <el-form-item label="测评卡名称" prop="categoryName">
          <el-input
            v-model="card.categoryName"
            placeholder="请输入测评卡名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="card.status">
            <el-radio :label="'0'">开启</el-radio>
            <el-radio :label="'1'">关闭</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序" prop="code">
          <el-input type="number" v-model="card.sequence"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :disabled="addCardDisable"
            @click="addCard()"
            >确认</el-button
          >
          <el-button @click="addCardDialogVisible = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 新增修改分类 -->
    <el-dialog
    append-to-body
      :title="title"
      :visible.sync="addBigCategoryDialogVisible"
      width="30%"
      :close-on-click-modal="false"
    >
      <el-form
        :model="category"
        ref="categoryForm"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="分类名称" prop="categoryName">
          <el-input
            v-model="category.categoryName"
            placeholder="请输入分类名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="category.status">
            <el-radio :label="'0'">开启</el-radio>
            <el-radio :label="'1'">关闭</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序" prop="code">
          <el-input type="number" v-model="category.sequence"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :disabled="addBigCategoryDisabled"
            @click="addBigCategory()"
            >确认</el-button
          >
          <el-button @click="addBigCategoryDialogVisible = false"
            >取消</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 新增子分类 -->
    <el-dialog
    append-to-body
      :title="title"
      :visible.sync="addChildCategoryDialogVisible"
      width="30%"
      :close-on-click-modal="false"
    >
      <el-form
        :model="childCategory"
        ref="childCategoryForm"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="子分类名称" prop="categoryName">
          <el-input
            v-model="childCategory.categoryName"
            placeholder="请输入子分类名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="childCategory.status">
            <el-radio :label="'0'">开启</el-radio>
            <el-radio :label="'1'">关闭</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序" prop="code">
          <el-input type="number" v-model="childCategory.sequence"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :disabled="addChildCategoryDisabled"
            @click="addChildCategory()"
            >确认</el-button
          >
          <el-button @click="addChildCategoryDialogVisible = false"
            >取消</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data () {
    return {
      // 查询条件
      queryParams: {
        name: '',
        status: '',
        currentPage: 1,
        pageSize: 30
      },
      showSearch: true,
      tableData: [],
      total: 0,
      optionStatus: [],
      title: '',
      addCardDialogVisible: false,
      addBigCategoryDialogVisible: false,
      addChildCategoryDialogVisible: false,
      card: {
        id: '',
        parentId: '',
        categoryName: '',
        status: '',
        sequence: '',
        isCard: ''
      },
      category: {
        id: '',
        parentId: '',
        categoryName: '',
        status: '',
        sequence: '',
        isCard: ''
      },
      childCategory: {
        id: '',
        parentId: '',
        categoryName: '',
        status: '',
        sequence: '',
        isCard: ''
      },
      rules: {
        categoryName: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }]
      },
      maps: new Map(),
      addCardDisable: false,
      addBigCategoryDisabled: false,
      addChildCategoryDisabled: false
    }
  },
  activated () {
    this.getDictStatus('card_status')
    this.handleQuery()
  },
  methods: {
    getDictStatus (code) {
      this.$http({
        url: this.$http.adornUrl('/admin/dict/parent'),
        method: 'get',
        params: {
          code
        }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.optionStatus = resp.data.obj
        } else {
          this.optionStatus = []
        }
      })
    },
    handleQuery () {
      this.tableData = []
      this.$http({
        url: this.$http.adornUrl('/evaluate/cardCategory/getTree'),
        method: 'get',
        params: this.queryParams
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.tableData = resp.data.obj.records
          this.total = resp.data.obj.total
        } else {
          this.tableData = []
          this.total = 0
        }
      })
    },

    // 删除
    removeCard (row) {
      if (row.hasChildren) {
        this.$confirm(`分类下有工作事项，请删除工作事项后再删除分类?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      } else {
        this.$confirm(`确认是否彻底删除？`, '删除操作', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl(`/evaluate/cardCategory/delete`),
            method: 'get',
            params: { id: row.id }
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '删除成功',
                type: 'success'
              })
              this.tableData = []
              this.handleQuery()
            } else {
              this.$message.error(data.msg)
            }
          })
        })
      }
    },
    // 打开添加大类
    openAddCard () {
      this.title = '新增测评卡'
      this.card.categoryName = null
      this.card.status = '0'
      this.card.sequence = null
      this.card.id = null
      this.addCardDialogVisible = true
    },

    // 添加子分类
    openAddChildCategory (row) {
      this.title = '新增子分类'
      this.childCategory.categoryName = null
      this.childCategory.status = '0'
      this.childCategory.sequence = null
      this.childCategory.parentId = row.id
      this.childCategory.id = null
      this.addChildCategoryDialogVisible = true
    },
    addChildCategory () {
      this.$refs['childCategoryForm'].validate((valid) => {
        if (valid) {
          this.addChildCategoryDisabled = true
          if (this.childCategory.id) {
            this.$http({
              url: this.$http.adornUrl(
                '/evaluate/cardCategory/updateCategoryStatus'
              ),
              method: 'post',
              data: this.childCategory
            }).then((resp) => {
              if (resp.data && resp.data.code === 0) {
                this.$message({
                  message: '修改成功',
                  type: 'success'
                })
                this.addChildCategoryDialogVisible = false
                this.handleQuery()
              } else {
                this.$message({
                  message: '修改失败',
                  type: 'warning'
                })
              }
              setTimeout(() => {
                this.addChildCategoryDisabled = false
              }, 1000)
            })
          } else {
            this.card.isCard = 'N'
            this.$http({
              url: this.$http.adornUrl('/evaluate/cardCategory/addCategory'),
              method: 'post',
              data: this.childCategory
            }).then((resp) => {
              if (resp.data && resp.data.code === 0) {
                this.$message({
                  message: '添加成功',
                  type: 'success'
                })
                this.addChildCategoryDialogVisible = false
                this.handleQuery()
              } else {
                this.$message.error(resp.data.msg)
              }
              setTimeout(() => {
                this.addChildCategoryDisabled = false
              }, 1000)
            })
          }
        } else {
          return false
        }
      })
    },

    // 修改测评卡版本
    editCategory (row) {
      if (row.isCard === 'Y') {
        this.title = '修改测评卡版本'
        this.card.id = row.id
        this.card.categoryName = row.name
        this.card.status = row.status
        this.card.sequence = row.rank
        this.addCardDialogVisible = true
      } else if (row.level === 2) {
        this.title = '修改大类'
        this.category.id = row.id
        this.category.categoryName = row.name
        this.category.status = row.status
        this.category.sequence = row.rank
        this.addBigCategoryDialogVisible = true
      } else {
        this.childCategory.id = row.id
        this.childCategory.categoryName = row.name
        this.childCategory.status = row.status
        this.childCategory.sequence = row.rank
        this.title = '修改子分类'
        this.addChildCategoryDialogVisible = true
      }
    },

    // 添加或修改测评卡版本
    async addCard () {
      this.$refs['cardForm'].validate((valid) => {
        if (valid) {
          this.addCardDisable = true
          if (!this.card.id) {
            this.card.parentId = null
            this.card.parentId = 0
            this.$http({
              url: this.$http.adornUrl('/evaluate/cardCategory/addCategory'),
              method: 'post',
              data: this.card
            }).then((resp) => {
              if (resp.data && resp.data.code === 0) {
                this.$message({
                  message: '添加成功',
                  type: 'success'
                })
                this.addCardDialogVisible = false
                this.handleQuery()
              } else {
                this.$message.error(resp.data.msg)
              }
              setTimeout(() => {
                this.addCardDisable = false
              }, 1000)
            })
          } else {
            this.card.parentId = null
            this.$http({
              url: this.$http.adornUrl(
                '/evaluate/cardCategory/updateCardStatus'
              ),
              method: 'post',
              data: this.card
            }).then((resp) => {
              if (resp.data && resp.data.code === 0) {
                this.$message({
                  message: '修改成功',
                  type: 'success'
                })
                this.addCardDialogVisible = false
                this.handleQuery()
              } else {
                this.$message.error(resp.data.msg)
              }
              setTimeout(() => {
                this.addCardDisable = false
              }, 1000)
            })
          }
        } else {
          return false
        }
      })
    },
    openAddCategory (row) {
      this.category.categoryName = null
      this.category.status = '0'
      this.category.sequence = null
      this.category.parentId = row.id
      this.category.id = null
      this.title = '新增大类'
      this.addBigCategoryDialogVisible = true
    },
    // 添加大类
    addBigCategory () {
      this.$refs['categoryForm'].validate((valid) => {
        if (valid) {
          this.addBigCategoryDisabled = true
          if (this.category.id) {
            this.$http({
              url: this.$http.adornUrl(
                '/evaluate/cardCategory/updateCategoryStatus'
              ),
              method: 'post',
              data: this.category
            }).then((resp) => {
              if (resp.data && resp.data.code === 0) {
                this.$message({
                  message: '修改成功',
                  type: 'success'
                })
                this.addBigCategoryDialogVisible = false
                this.handleQuery()
              } else {
                this.$message({
                  message: '修改失败',
                  type: 'warning'
                })
              }
              setTimeout(() => {
                this.addBigCategoryDisabled = false
              }, 1000)
            })
          } else {
            this.card.isCard = 'N'
            this.$http({
              url: this.$http.adornUrl('/evaluate/cardCategory/addCategory'),
              method: 'post',
              data: this.category
            }).then((resp) => {
              if (resp.data && resp.data.code === 0) {
                this.$message({
                  message: '添加成功',
                  type: 'success'
                })
                this.addBigCategoryDialogVisible = false
                this.handleQuery()
              } else {
                this.$message.error(resp.data.msg)
              }
              setTimeout(() => {
                this.addBigCategoryDisabled = false
              }, 1000)
            })
          }
        } else {
          return false
        }
      })
    },

    handleClose (done) {
      this.$confirm('确认关闭？')
        .then((_) => {
          done()
        })
        .catch((_) => { })
    },

    // 子节点赋值
    async load (tree, treeNode, resolve) {
      this.maps.set(tree.id, { tree, treeNode, resolve })
      await setTimeout(() => {
        resolve(tree.children)
      }, 300)
      await this.setNewRowData(tree, tree.children)
    },
    setNewRowData (row, loadData) {
      row.children = loadData
    },
    /** 重置按钮操作 */
    resetQuery () {
      this.queryParams.status = ''
      this.queryParams.name = ''
      this.status = ''
      this.handleQuery()
    },
    // 每页数
    sizeChangeHandle (val) {
      this.queryParams.pageSize = val
      this.queryParams.currentPage = 1
      this.handleQuery()
    },
    // 当前页
    currentChangeHandle (val) {
      this.queryParams.currentPage = val
      this.handleQuery()
    },
    goDetail (row) {
      this.$router.push({ path: '/evaluateDetail', query: { id: row.id } })
    }
  }
}
</script>

<style>
.searchParams {
  width: 90%;
  float: left;
}
.addRight {
  float: right;
}
</style>
