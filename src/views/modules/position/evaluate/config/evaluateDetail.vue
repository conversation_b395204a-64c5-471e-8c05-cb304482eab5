<template>
  <div>
    <!-- <h1>{{ id }}</h1> -->
    <el-form ref="form" :model="category" label-width="80px">
      <el-form-item label="测评要求">
        <el-input
          type="textarea"
          v-model="category.need"
          @blur="updateCategory"
        ></el-input>
      </el-form-item>
      <el-form-item label="测评项目">
        <el-input
          type="textarea"
          v-model="category.remark"
          @blur="updateCategory"
        ></el-input>
      </el-form-item>
      <div style="width: 91.5%; float: left">
        <p align="left" style="margin-left: 80px; margin-top: 10px">
          采集项列表
        </p>
      </div>
      <div style="float: right">
        <el-button size="mini" type="primary" @click="addCollectData()"
          >添加采集项</el-button
        >
      </div>
      <div style="clear: both"></div>
      <el-form-item label="采集项">
        <el-table
          border
          ref="singleTable"
          :data="collectTable"
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column type="index" width="50" label="编号" align="center">
          </el-table-column>
          <el-table-column align="center" property="collectName" label="采集项">
          </el-table-column>
          <el-table-column
            align="center"
            property="sequence"
            label="排序"
          ></el-table-column>
          <el-table-column align="center" property="statusName" label="状态">
          </el-table-column>
          <el-table-column align="center" property="createName" label="创建人">
          </el-table-column>
          <el-table-column
            align="center"
            property="createDate"
            label="创建时间"
          >
          </el-table-column>
          <el-table-column align="center" property="remark" label="备注">
          </el-table-column>
          <el-table-column align="center" label="操作">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleCollectEdit(scope.row)"
                >编辑</el-button
              >
              <el-button
                size="mini"
                type="text"
                @click="handleCollectDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <br />
      <div style="width: 80%; float: left">
        <p align="left" style="margin-left: 80px; margin-top: 10px">
          测评项列表
        </p>
      </div>
      <div style="float: right">
        <el-button size="mini" type="text" @click="openUpload">导入</el-button>
        <el-button size="mini" type="primary" @click="openAddContent"
          >添加测评项</el-button
        >
        <el-button size="mini" type="danger" @click="clearEvaluateCard"
          >清空测评卡</el-button
        >
      </div>
      <br />
      <br />
      <div style="clear: both"></div>
      <div>
        <el-form-item label="测评项">
          <div>
            <!--

             -->
            <el-table
              :row-style="{ height: '20px' }"
              :cell-style="{ padding: '0px' }"
              style="width: 100%; margin: 0 auto; font-size: 8px"
              ref="multipleTable"
              border
              :span-method="objectSpanMethod"
              :cell-class-name="tableRowClassName"
              @cell-mouse-leave="cellMouseLeave"
              @cell-mouse-enter="cellMouseEnter"
              :data="tableData"
            >
              <el-table-column
                label="编号"
                type="index"
                align="center"
                min-width="3%"
              ></el-table-column>
              <el-table-column
                prop="evaluateName"
                label="测评内容"
                align="center"
                min-width="20%"
                ><template slot-scope="scope">
                  <span
                    style="color: #409eff; cursor: pointer"
                    @click="updateContentSequence(scope.row)"
                    >{{ scope.row.evaluateName }}</span
                  >
                </template>
              </el-table-column>
              <el-table-column
                prop="contentName"
                label="测评项\测评明细"
                align="center"
                min-width="20%"
              >
                <template slot-scope="scope">
                  <p align="left" @click="updateContentSequence(scope.row)">
                    {{ scope.row.contentName }}
                  </p>
                </template>
              </el-table-column>
              <el-table-column
                prop="categoryName"
                label="测评项分类"
                align="center"
                min-width="7%"
              ></el-table-column>
              <el-table-column
                prop="dutyName"
                label="责任局办"
                align="center"
                min-width="7%"
              ></el-table-column>

              <el-table-column
                prop="core"
                label="分值"
                align="center"
                min-width="4%"
              ></el-table-column>
              <el-table-column
                prop="reduceCore"
                label="扣分"
                align="center"
                min-width="4%"
              ></el-table-column>
              <el-table-column
                prop="handType"
                label="类型"
                align="center"
                min-width="4%"
              >
                <template slot-scope="scope">
                  <span>{{
                    scope.row.handType === "1" ? "动态" : "静态"
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="handTime"
                label="处理时长"
                align="center"
                min-width="6%"
              >
              </el-table-column>
              <el-table-column
                prop="reduceStandard"
                label="扣分标准"
                align="center"
                min-width="8%"
              >
              </el-table-column>
              <el-table-column
                prop="watchStandard"
                label="拍照要求"
                align="center"
                min-width="8%"
              >
              </el-table-column>
              <el-table-column
                prop="evaluateCriteria"
                label="评判标准"
                align="center"
                min-width="6%"
              >
              </el-table-column>
              <el-table-column
                label="测评结果配置"
                align="center"
                min-width="5%"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    @click="editConfig(scope.row)"
                    >配置</el-button
                  >
                </template>
              </el-table-column>
              <el-table-column
                prop="status"
                label="状态"
                align="center"
                min-width="4%"
              >
                <template slot-scope="scope">
                  <span style="color: #409eff">{{
                    scope.row.status == "0" ? "正常" : "停用"
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" min-width="10%">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    @click="handleContentEdit(scope.row)"
                    >编辑</el-button
                  >
                  <el-button
                    size="mini"
                    type="text"
                    @click="handleContentRemove(scope.row)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form-item>
      </div>
    </el-form>
    <el-dialog
      :title="title"
      :visible.sync="addCollectItemDialogVisible"
      width="30%"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        :model="collectData"
        ref="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="采集项名称" prop="collectName">
          <el-input v-model="collectData.collectName"></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="sequence">
          <el-input type="number" v-model="collectData.sequence"></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="collectData.status">
            <el-radio :label="'0'">开启(默认)</el-radio>
            <el-radio :label="'1'">关闭</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input type="textarea" v-model="collectData.remark"></el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" :disabled="collectDisabled" @click="collectDataHand('form')"
            >确认</el-button
          >
          <el-button @click="addCollectItemDialogVisible = false"
            >取消</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog
      append-to-body
      :title="title"
      :visible.sync="contentItemDialogVisible"
      width="40%"
      :close-on-click-modal="false"
    >
      <el-form
        :model="contentItem"
        ref="contentItem"
        :rules="contentRules"
        label-width="120px"
      >
        <el-form-item label="测评内容" prop="id">
          <el-select
            v-model="contentItem.id"
            placeholder="请选择活动区域"
            style="width: 90%"
            filterable
            @change="contentChange(contentItem.id)"
          >
            <el-option
              v-for="item in contentList"
              :key="item.id"
              :label="item.evaluateName"
              :value="item.id"
            ></el-option>
          </el-select>
          <div style="float: right; margin-right: 10px">
            <el-button type="text" size="mini" @click="addContent"
              >新增</el-button
            >
          </div>
        </el-form-item>
        <el-form-item label="测评分类" prop="remark">
          <el-select
            v-model="contentItem.categoryCode"
            placeholder="请选择测评分类"
            style="width: 100%"
            filterable
            @change="getCategoryName(contentItem.categoryCode)"
          >
            <el-option
              v-for="item in categoryList"
              :key="item.id"
              :label="item.name"
              :value="item.code"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="sequence">
          <el-input
            type="number"
            v-model="contentItem.sequence"
            style="width: 50%"
          ></el-input>
          <span style="margin-left: 5px; width: 50%"
            ><i class="el-icon-warning-outline"></i>0-9999,数值越小越靠前</span
          >
        </el-form-item>
        <el-form-item label="测评项\测评明细" prop="contentName">
          <el-input
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
            placeholder="请输入测评明细"
            v-model="contentItem.contentName"
          ></el-input>
        </el-form-item>
        <el-form-item label="责任局办" prop="remark">
          <el-select
            v-model="dutyIds"
            placeholder="请选择活动区域"
            style="width: 100%"
            multiple
            filterable
            @change="getDutyName(dutyIds)"
          >
            <el-option
              v-for="item in orgList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="得分/分值" prop="remark">
          <el-input type="number" v-model="contentItem.core"></el-input>
        </el-form-item>
        <el-form-item label="扣分" prop="remark">
          <el-input type="number" v-model="contentItem.reduceCore"></el-input>
        </el-form-item>
        <el-form-item label="类型" prop="remark">
          <el-radio-group v-model="contentItem.handType">
            <el-radio :label="'0'">静态(默认)</el-radio>
            <el-radio :label="'1'">动态</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="处置时长" prop="remark">
          <el-input type="number" v-model="contentItem.handTime"></el-input>
        </el-form-item>
        <el-form-item label="扣分标准" prop="remark">
          <el-input
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
            v-model="contentItem.reduceStandard"
          ></el-input>
        </el-form-item>
        <el-form-item label="拍照要求" prop="remark">
          <el-input
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
            v-model="contentItem.watchStandard"
          ></el-input>
        </el-form-item>
        <el-form-item label="评判标准" prop="remark">
          <el-checkbox-group v-model="configList2" @change="changeConfig2">
            <el-checkbox label="符合"></el-checkbox>
            <el-checkbox label="基本符合"></el-checkbox>
            <el-checkbox label="不符合"></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="状态" prop="remark">
          <el-radio-group v-model="contentItem.status">
            <el-radio :label="'0'">开启(默认)</el-radio>
            <el-radio :label="'1'">关闭</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :disabled="contentItemDisabled"
            @click="contentItemHand('contentItem')"
            >确认</el-button
          >
          <el-button @click="contentItemDialogVisible = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 测评内容管理 -->
    <el-dialog
      append-to-body
      :title="title"
      :visible.sync="updateContentItemDialogVisible"
      width="40%"
      :close-on-click-modal="false"
    >
      <el-form
        :model="contentStatusList"
        ref="contentForm"
        :rules="contentFormRules"
        label-width="100px"
      >
        <el-form-item label="测评名称" prop="evaluateName">
          <el-input v-model="contentStatusList.evaluateName"></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="sequence">
          <el-input
            type="number"
            v-model="contentStatusList.sequence"
            style="width: 50%"
          ></el-input>
          <span style="margin-left: 5px"
            ><i class="el-icon-warning-outline"></i>0-9999,数值越小越靠前</span
          >
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :disabled="contentStatusListDisabled"
            @click="contentDataHand('contentForm')"
            >确认</el-button
          >
          <el-button @click="updateContentItemDialogVisible = false"
            >取消</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 导入 -->
    <el-dialog
      append-to-body
      :title="title"
      :visible.sync="uploadDialogVisible"
      width="40%"
      :close-on-click-modal="false"
    >
      <el-form
        :model="contentStatusList"
        ref="contentForm"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="测评名称" prop="evaluateName">
          <span></span>
        </el-form-item>
        <el-form-item prop="sequence">
          <el-upload
            class="upload-demo"
            drag
            action="https://jsonplaceholder.typicode.com/posts/"
            multiple
            :on-success="handleAvatarSuccess"
            :file-list="fileList"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              可直接将文件拖拽到此处进行上传，支持格式：xls、xlsx
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="exportFile">导入</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 测评项配置 -->
    <el-dialog
      append-to-body
      :title="title"
      :visible.sync="contentItemConfigDialogVisible"
      width="40%"
      :close-on-click-modal="false"
    >
      <el-form
      v-loading="loading"
        :model="contentConfig"
        ref="contentConfig"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="配置测评结果" prop="evaluateName">
          <el-radio-group v-model="contentConfig.isConfigResult">
            <el-radio :label="'Y'">是</el-radio>
            <el-radio :label="'N'">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="测评结果" prop="evaluateName">
          <el-checkbox-group v-model="configList" @change="changeConfig">
            <el-checkbox label="符合"></el-checkbox>
            <el-checkbox label="基本符合"></el-checkbox>
            <el-checkbox label="不符合"></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="不符合" prop="evaluateName" v-if="noFitTypeTrue">
          <el-radio-group v-model="contentConfig.noFitType">
            <el-radio :label="'0'">任意</el-radio>
            <el-radio :label="'1'">包含</el-radio>
          </el-radio-group>
          <div>
            <el-card class="box-card" shadow="never">
              <el-checkbox-group v-model="noFitIds">
                <el-checkbox
                  style="display: block"
                  v-for="detail in detailList"
                  :label="detail.id"
                  :key="detail.id"
                >
                  <span class="checkbox_content">{{ detail.contentName }}</span>
                </el-checkbox>
              </el-checkbox-group>
            </el-card>
          </div>
        </el-form-item>
        <el-form-item
          label="基本符合"
          prop="evaluateName"
          v-if="baseFitTypeTrue"
        >
          <el-radio-group v-model="contentConfig.baseFitType">
            <el-radio :label="'0'">任意</el-radio>
            <el-radio :label="'1'">包含</el-radio>
          </el-radio-group>
          <div>
            <el-card class="box-card" shadow="never">
              <el-checkbox-group v-model="baseFitIds">
                <el-checkbox
                  style="display: block"
                  v-for="detail in detailList"
                  :label="detail.id"
                  :key="detail.id"
                >
                  <span class="checkbox_content">{{ detail.contentName }}</span>
                </el-checkbox>
              </el-checkbox-group>
            </el-card>
          </div>
        </el-form-item>
        <el-form-item label="符合" prop="evaluateName" v-if="fitTypeTrue">
          <span>所有测评项无问题为符合</span>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="updateContentConfig('contentConfig')"
            >保存</el-button
          >
          <el-button @click="contentItemConfigDialogVisible = false"
            >取消</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data () {
    return {
      collectDisabled: false,
      id: '',
      // 测评项
      tableData: [],
      rowIndex: '-1',
      OrderIndexArr: [],
      hoverOrderArr: [],
      // 分类详情
      category: {
        id: '',
        categoryName: '',
        need: '',
        remark: ''
      },
      // 采集项数据列表
      collectTable: [],
      addCollectItemDialogVisible: false,
      collectData: {
        id: '',
        collectName: '',
        sequence: '',
        status: '',
        remark: '',
        version: 0
      },
      rules: {
        collectName: [
          { required: true, message: '采集项名称不能为空', trigger: 'blur' }
        ]
      },
      contentRules: {
        id: [
          { required: true, message: '请选择测评内容名称', trigger: 'change' }
        ]
      },
      contentFormRules: {
        evaluateName: [
          { required: true, message: '测评内容不能为空', trigger: 'blur' }
        ]
      },
      title: '',
      contentList: [],
      contentItemDialogVisible: false,
      updateContentItemDialogVisible: false,
      contentItemConfigDialogVisible: false,
      contentItem: {
        contentName: '',
        core: '',
        criteriaValue: '',
        detailId: '',
        dutyId: null,
        dutyName: '',
        evaluateCriteria: '',
        evaluateName: '',
        handTime: '',
        handType: '',
        id: '',
        reduceCore: '',
        reduceStandard: '',
        status: '',
        watchStandard: '',
        categoryId: '',
        categoryCode: '',
        categoryName: ''
      },
      dutyIds: [],
      contentStatusList: {
        id: '',
        evaluateName: '',
        sequence: '',
        categoryId: ''
      },
      uploadDialogVisible: false,
      fileList: [],
      file: null,
      orgList: [],
      contentConfig: {
        baseFitIds: '',
        baseFitType: null,
        contentId: null,
        evaluateResult: '',
        fitIds: '',
        fitType: null,
        id: null,
        isConfigResult: null,
        noFitIds: '',
        noFitType: null
      },
      configList: [],
      configList2: [],
      baseFitTypeTrue: false,
      noFitTypeTrue: false,
      fitTypeTrue: false,
      detailList: [],
      noFitIds: [],
      baseFitIds: [],
      contentItemDisabled: false,
      contentStatusListDisabled: false,
      categoryList: [],
      loading: false
    }
  },
  activated () {
    this.id = this.$route.query.id
    // 查询基本信息
    this.queryCategory()
    // 查询采集项
    this.queryCollectData()
    // 查询测评项
    this.queryEvaluateContent()
  },
  //   mounted() {
  //     this.getOrderNumber();
  //   },
  methods: {
    queryCategory () {
      this.$http({
        url: this.$http.adornUrl('/evaluate/cardCategory/getById'),
        method: 'get',
        params: { id: this.$route.query.id }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.category = resp.data.obj
        } else {
          this.collectTable = []
        }
      })
    },
    handleAvatarSuccess (res, file) {
      this.file = file
    },
    queryCollectData () {
      this.$http({
        url: this.$http.adornUrl('/evaluate/collectItem/getItemList'),
        method: 'get',
        params: { categoryId: this.$route.query.id }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.collectDisabled = false
          this.collectTable = resp.data.obj
        } else {
          this.collectDisabled = false
          this.collectTable = []
        }
      })
    },
    // 根据测评分类code获取名称
    getCategoryName (code) {
      this.categoryList.forEach(data => {
        if (code === data.code) {
          this.contentItem.categoryName = data.name
        }
      })
    },
    // 获得对应的测评分类的列表
    getCategoryList () {
      this.$http({
        url: this.$http.adornUrl('/admin/dict/parent'),
        method: 'get',
        params: { code: 'evaluate_detail_category' }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.categoryList = resp.data.obj
        } else {
          this.categoryList = []
        }
      })
    },
    queryEvaluateContent () {
      this.tableData = []
      this.$http({
        url: this.$http.adornUrl('/evaluate/itemDetail/getList2'),
        method: 'get',
        params: { categoryId: this.$route.query.id }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.tableData = resp.data.obj
          this.getOrderNumber()
        } else {
          this.tableData = []
        }
      })
    },
    exportFile () {
      this.$http({
        url: this.$http.adornUrl('/evaluate/itemDetail/importExcelData'),
        method: 'post',
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        data: { categoryId: this.$route.query.id, file: this.file }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.tableData = resp.data.obj
          this.getOrderNumber()
        } else {
          this.tableData = []
        }
      })
    },

    async editConfig (row) {
      this.loading = true
      this.configList = []
      this.configList2 = []
      this.noFitTypeTrue = false
      this.fitTypeTrue = false
      this.baseFitTypeTrue = false
      this.contentItemConfigDialogVisible = true
      this.contentConfig.isConfigResult = null
      this.baseFitIds = []
      this.baseFitType = null
      this.contentId = row.id
      this.evaluateResult = ''
      this.fitIds = []
      this.fitType = null
      this.id = null
      this.isConfigResult = null
      this.noFitIds = []
      this.contentConfig.id = null
      this.noFitType = null
      this.contentConfig.evaluateResult = null
      await this.$http({
        url: this.$http.adornUrl('/evaluate/resultConfig/getConfig'),
        method: 'get',
        params: { contentId: row.id }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.contentConfig = resp.data.obj
          if (this.contentConfig.noFitIds) {
            this.noFitIds = this.contentConfig.noFitIds.split(',')
          }
          if (this.contentConfig.baseFitIds) {
            this.baseFitIds = this.contentConfig.baseFitIds.split(',')
          }
          if (this.contentConfig.fitIds) {
            this.fitIds = this.contentConfig.fitIds.split(',')
          }
        }
      })
      if (this.contentConfig.evaluateResult) {
        if (this.contentConfig.evaluateResult.indexOf('□') > 0) {
          this.contentConfig.evaluateResult = ''
          this.configList = []
        } else {
          this.configList = this.contentConfig.evaluateResult.split(',')
        }
      }
      if (this.configList !== []) {
        this.getConfigHide()
      }
      this.contentConfig.contentId = row.id
      await this.getContentDetail(row.id)
      // this.loading = false
    },
    getContentDetail (id) {
      this.loading = true
      // 测评明细列表
      this.$http({
        url: this.$http.adornUrl('/evaluate/itemDetail/getByContentId'),
        method: 'get',
        params: { contentId: id }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.detailList = resp.data.obj
        } else {
          this.detailList = []
        }
        this.loading = false
      })
    },
    getConfigHide () {
      this.configList.forEach(data => {
        if (data === '不符合') {
          this.noFitTypeTrue = true
        }
        if (data === '符合') {
          this.fitTypeTrue = true
        }
        if (data === '基本符合') {
          this.baseFitTypeTrue = true
        }
      })
    },
    clearEvaluateCard () {
      this.$confirm(`是否彻底清空测评卡？`, '清空操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl(`/evaluate/itemContent/deleteByCategoryId`),
          method: 'get',
          params: { categoryId: this.$route.query.id }
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '测评卡已清空',
              type: 'success'
            })
            this.queryEvaluateContent()
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    changeConfig () {
      this.noFitTypeTrue = false
      this.fitTypeTrue = false
      this.baseFitTypeTrue = false
      if (this.configList !== []) {
        this.getConfigHide()
      }
    },
    changeConfig2 () {
      this.contentItem.evaluateCriteria = this.configList2.join(',')
    },

    addCollectData () {
      this.collectData.id = ''
      this.collectData.collectName = ''
      this.collectData.sequence = ''
      this.collectData.status = '0'
      this.collectData.categoryId = this.$route.query.id
      this.collectData.remark = ''
      this.addCollectItemDialogVisible = true
      this.title = '添加采集项'
    },
    collectDataHand (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.collectDisabled = true
          if (this.collectData.id) {
            // 修改
            this.$http({
              url: this.$http.adornUrl('/evaluate/collectItem/update'),
              method: 'post',
              data: this.collectData
            }).then((resp) => {
              if (resp.data && resp.data.code === 0) {
                this.$message({
                  message: '修改成功',
                  type: 'success'
                })
                this.collectDisabled = false
                this.addCollectItemDialogVisible = false
                this.queryCollectData()
              } else {
                this.collectDisabled = false
                this.$message({
                  message: '修改失败',
                  type: 'warning'
                })
              }
            })
          } else {
            // 添加
            this.$http({
              url: this.$http.adornUrl('/evaluate/collectItem/insert'),
              method: 'post',
              data: this.collectData
            }).then((resp) => {
              if (resp.data && resp.data.code === 0) {
                this.$message({
                  message: '添加成功',
                  type: 'success'
                })
                this.addCollectItemDialogVisible = false
                this.queryCollectData()
              } else {
                this.collectDisabled = false
                this.$message({
                  message: '添加失败',
                  type: 'warning'
                })
              }
            })
          }
        } else {
          return false
        }
      })
    },
    updateContentConfig (formName) {
      this.contentConfig.evaluateResult = this.configList.join(',')
      this.contentConfig.noFitIds = this.noFitIds.join(',')
      this.contentConfig.baseFitIds = this.baseFitIds.join(',')
      if (this.contentConfig.id) {
        this.$http({
          url: this.$http.adornUrl('/evaluate/resultConfig/update'),
          method: 'post',
          data: this.contentConfig
        }).then((resp) => {
          if (resp.data && resp.data.code === 0) {
            this.$message({
              message: '修改成功',
              type: 'success'
            })
            this.contentItemConfigDialogVisible = false
            this.queryEvaluateContent()
          } else {
            this.$message({
              message: '修改失败',
              type: 'warning'
            })
          }
        })
      } else {
        this.$http({
          url: this.$http.adornUrl('/evaluate/resultConfig/save'),
          method: 'post',
          data: this.contentConfig
        }).then((resp) => {
          if (resp.data && resp.data.code === 0) {
            this.$message({
              message: '添加成功',
              type: 'success'
            })
            this.queryEvaluateContent()
            this.contentItemConfigDialogVisible = false
          } else {
            this.$message({
              message: '添加失败',
              type: 'warning'
            })
          }
        })
      }
    },
    openUpload () {
      // this.title = '导入测评项'
      // this.uploadDialogVisible = true
      this.$router.push({ path: '/evaluateImport', query: { categoryId: this.$route.query.id, type: 1 } })
    },
    handleCollectEdit (row) {
      this.addCollectItemDialogVisible = true
      this.collectData.id = row.id
      this.collectData = row
    },
    getDutyName (dutyIds) {
      this.contentItem.dutyId = ''
      this.contentItem.dutyName = ''
      if (dutyIds) {
        this.contentItem.dutyId = dutyIds.join(',')
        const dutyNames = []
        dutyIds.forEach(id => {
          this.orgList.forEach(org => {
            if (org.id + '' === id + '') {
              dutyNames.push(org.name)
            }
          })
        })
        this.contentItem.dutyName = dutyNames.join(',')
      }
    },
    handleCollectDelete (row) {
      this.$confirm(`确认是否彻底删除？`, '删除操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl(`/evaluate/collectItem/removeByIds`),
          method: 'get',
          params: { ids: row.id }
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '删除成功',
              type: 'success'
            })
            // this.tableData = []
            this.queryCollectData()
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    handleClose (done) {
      this.$confirm('确认关闭？')
        .then((_) => {
          done()
        })
        .catch((_) => { })
    },
    updateContentSequence (row) {
      this.updateContentItemDialogVisible = true
      this.contentStatusList.id = row.id
      this.contentStatusList.evaluateName = row.evaluateName
      this.contentStatusList.sequence = row.rank
    },
    addContent () {
      this.updateContentItemDialogVisible = true
      this.contentStatusList.id = ''
      this.contentStatusList.evaluateName = ''
      this.contentStatusList.sequence = ''
      this.contentStatusList.categoryId = this.$route.query.id
    },
    contentDataHand (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.contentStatusListDisabled = true
          if (this.contentStatusList.id) {
            this.contentStatusList.categoryId = this.$route.query.id
            // 修改
            this.$http({
              url: this.$http.adornUrl('/evaluate/itemContent/update'),
              method: 'post',
              data: this.contentStatusList
            }).then((resp) => {
              if (resp.data && resp.data.code === 0) {
                this.$message({
                  message: '修改成功',
                  type: 'success'
                })
                this.updateContentItemDialogVisible = false
                this.queryEvaluateContent()
              } else {
                this.$message({
                  message: '修改失败',
                  type: 'warning'
                })
              }
              // 防止重复提交
              setTimeout(() => {
                this.contentStatusListDisabled = false
              }, 1000)
            })
          } else {
            // 添加
            this.$http({
              url: this.$http.adornUrl('/evaluate/itemContent/save'),
              method: 'post',
              data: this.contentStatusList
            }).then((resp) => {
              if (resp.data && resp.data.code === 0) {
                this.$message({
                  message: '添加成功',
                  type: 'success'
                })
                this.getContentList()
                this.updateContentItemDialogVisible = false
                this.queryEvaluateContent()
              } else {
                this.$message({
                  message: '添加失败',
                  type: 'warning'
                })
              }
              // 防止重复提交
              setTimeout(() => {
                this.contentStatusListDisabled = false
              }, 1000)
            })
          }
        } else {
          return false
        }
      })
    },

    // 修改测评项
    handleContentEdit (row) {
      this.title = '新增测评项'
      this.contentItem = row
      this.dutyIds = []
      if (row.dutyId) {
        this.dutyIds = row.dutyId.split(',')
        this.dutyIds.forEach(data => {
          if (!data) {
            this.dutyIds.splice(data, 1)
          }
        })
      }

      this.contentItem.dutyName = ''
      this.contentItemDialogVisible = true
      this.getContentList()
      this.getOrgList()
      if (row.evaluateCriteria) {
        this.configList2 = row.evaluateCriteria.split(',')
      } else {
        this.configList2 = []
      }
      this.getCategoryList()
    },
    getContentList () {
      this.$http({
        url: this.$http.adornUrl(`/evaluate/itemContent/getListByCategoryId`),
        method: 'get',
        params: { categoryId: this.$route.query.id }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.contentList = resp.data.obj
        } else {
          this.contentList = []
        }
      })
    },
    getOrgList () {
      this.$http({
        url: this.$http.adornUrl(`/admin/org/all`),
        method: 'get'
      }).then((resp) => {
        if (resp.data) {
          this.orgList = resp.data
          this.orgList.forEach(data => {
            data.id = data.id + ''
          })
        } else {
          this.orgList = []
        }
      })
    },
    contentChange (id) {
      this.configList2 = []
      this.contentList.forEach(data => {
        if (id === data.id) {
          this.contentItem.id = id
          this.contentItem.evaluateName = data.evaluateName
          this.contentItem.core = data.core
          this.contentItem.reduceStandard = data.reduceStandard
          this.contentItem.reduceCore = data.reduceCore
          this.contentItem.evaluateCriteria = data.evaluateCriteria
          this.contentItem.watchStandard = data.watchStandard
          if (data.evaluateCriteria) {
            this.configList2 = data.evaluateCriteria.split(',')
          }
        }
      })
    },

    // 添加/修改测评明细
    contentItemHand (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.contentItemDisabled = true
          this.contentItem.categoryId = this.$route.query.id
          if (this.contentItem.detailId) {
            this.getDutyName(this.dutyIds)
            // 修改
            this.$http({
              url: this.$http.adornUrl('/evaluate/itemDetail/updateData'),
              method: 'post',
              data: this.contentItem
            }).then((resp) => {
              if (resp.data && resp.data.code === 0) {
                this.$message({
                  message: '修改成功',
                  type: 'success'
                })
                this.contentItemDialogVisible = false
                this.queryEvaluateContent()
              } else {
                this.$message({
                  message: '修改失败',
                  type: 'warning'
                })
              }
              // 防止重复提交
              setTimeout(() => {
                this.contentItemDisabled = false
              }, 1000)
            })
          } else {
            // 添加
            this.contentItem.evaluateName = null
            this.$http({
              url: this.$http.adornUrl('/evaluate/itemDetail/saveData'),
              method: 'post',
              data: this.contentItem
            }).then((resp) => {
              if (resp.data && resp.data.code === 0) {
                this.$message({
                  message: '添加成功',
                  type: 'success'
                })
                this.contentItemDialogVisible = false
                this.queryEvaluateContent()
              } else {
                this.$message({
                  message: '添加失败',
                  type: 'warning'
                })
              }
              // 防止重复提交
              setTimeout(() => {
                this.contentItemDisabled = false
              }, 1000)
            })
          }
        } else {
          return false
        }
      })
    },

    // 初始化添加测评明细对话框的内容
    resetContentItem () {
      this.contentItem.contentName = null
      this.contentItem.core = null
      this.contentItem.criteriaValue = null
      this.contentItem.detailId = null
      this.contentItem.dutyId = null
      this.contentItem.dutyName = null
      this.contentItem.evaluateCriteria = null
      this.contentItem.evaluateName = null
      this.contentItem.handTime = null
      this.contentItem.id = null
      this.contentItem.reduceCore = null
      this.contentItem.reduceStandard = null
      this.contentItem.watchStandard = null
      this.contentItem.handType = '0'
      this.contentItem.status = '0'
      this.contentItem.categoryName = null
      this.contentItem.categoryCode = null
      this.configList2 = []
    },
    // 添加测评明细的按钮处理
    openAddContent () {
      this.resetContentItem()
      this.contentItemDialogVisible = true
      this.title = '添加测评项'
      this.dutyIds = []
      this.contentItemDialogVisible = true
      this.$http({
        url: this.$http.adornUrl(`/evaluate/itemContent/getListByCategoryId`),
        method: 'get',
        params: { categoryId: this.$route.query.id }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.contentList = resp.data.obj
        } else {
          this.contentList = []
        }
      })
      this.getOrgList()
      this.getCategoryList()
    },
    handleContentRemove (row) {
      this.$confirm(`确认是否彻底删除？`, '删除操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl(`/evaluate/itemDetail/removeByIds`),
          method: 'get',
          params: { ids: row.detailId }
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '删除成功',
              type: 'success'
            })
            this.tableData = []
            this.queryEvaluateContent()
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    updateCategory () {
      this.$http({
        url: this.$http.adornUrl(`/evaluate/cardCategory/updateCategoryStatus`),
        method: 'post',
        data: this.category
      })
    },
    // 获取相同编号的数组
    getOrderNumber () {
      this.OrderIndexArr = []
      let OrderObj = {}
      this.tableData.forEach((element, index) => {
        element.rowIndex = index
        if (OrderObj[element.evaluateName]) {
          OrderObj[element.evaluateName].push(index)
        } else {
          OrderObj[element.evaluateName] = []
          OrderObj[element.evaluateName].push(index)
        }
      })

      // 将数组长度大于1的值 存储到this.OrderIndexArr（也就是需要合并的项）
      for (let k in OrderObj) {
        if (OrderObj[k].length > 1) {
          this.OrderIndexArr.push(OrderObj[k])
        }
      }
    },

    // 合并单元格
    objectSpanMethod ({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 1 || columnIndex === 5 || columnIndex === 6 || columnIndex === 9 || columnIndex === 10 || columnIndex === 11 || columnIndex === 12) {
        for (let i = 0; i < this.OrderIndexArr.length; i++) {
          let element = this.OrderIndexArr[i]
          for (let j = 0; j < element.length; j++) {
            let item = element[j]
            if (rowIndex === item) {
              if (j === 0) {
                return {
                  rowspan: element.length,
                  colspan: 1
                }
              } else if (j !== 0) {
                return {
                  rowspan: 0,
                  colspan: 0
                }
              }
            }
          }
        }
      }
    },

    tableRowClassName ({ row, rowIndex }) {
      let arr = this.hoverOrderArr
      for (let i = 0; i < arr.length; i++) {
        if (rowIndex === arr[i]) {
          return 'hovered-row'
        }
      }
    },

    cellMouseEnter (row, column, cell, event) {
      this.rowIndex = row.rowIndex
      this.hoverOrderArr = []
      this.OrderIndexArr.forEach((element) => {
        if (element.indexOf(this.rowIndex) >= 0) {
          this.hoverOrderArr = element
        }
      })
    },

    cellMouseLeave (row, column, cell, event) {
      this.rowIndex = '-1'
      this.hoverOrderArr = []
    }
  }
}
</script>

<style>
.el-table .hovered-row {
  background: #f5f7fa;
}
.box-card {
  background-color: rgb(251, 251, 251);
}
.checkbox_content {
  display: inline-grid;
  white-space: pre-line;
  word-wrap: break-word;
  overflow: hidden;
  line-height: 20px;
  margin-top: -15px;
}
</style>
