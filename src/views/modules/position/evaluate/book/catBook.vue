<template>
  <!-- 整改通知书 -->
  <el-dialog
    :title="bookTitle"
    :visible.sync="bookDialogVisible2"
    width="50%"
    append-to-body
    :close-on-click-modal="false"
  >
    <div style="margin-right: 15px">
      <el-button style="margin-left: 15px" @click="print" icon="el-icon-printer"
        >打印</el-button
      >
      <!-- <el-button
        style="margin-left: 15px"
        icon="el-icon-download"
        @click="produce"
        >下载pdf</el-button
      > -->
      <el-button
        style="margin-left: 15px"
        icon="el-icon-download"
        @click="produceWord"
        >下载word</el-button
      >
      <el-button
        style="margin-left: 15px"
        icon="el-icon-s-promotion"
        @click="sendEmail"
        >发送邮件</el-button
      >
    </div>
    <div style="clear: both"></div>
    <div>
      <el-card style="margin-left: 15px; margin-right: 15px">

          <div id="printForm2" style="margin-top: 15px" class="printForm2">
            <!-- <div v-if="value + '' === '1'"> -->
            <img
              src="https://wmyq.sipac.gov.cn/template/title.png"
              width="100%"
            />
            <p align="right">
              编号：<span>{{ book.code }}</span>
            </p>
            <p style="font-size: 20px; line-height: 150%">
              为切实巩固文明示范城区建设成果，迎接苏州市“社会文明程度指数”常态测评工作。
              {{ book.startDate }}
              ，
              月亮湾社工委对辖区内的文明创建工作进行了重点检查。现将检查中发现的极为突出的严重影响迎检工
              作的不达标问题，下发整改通知。请接到通知后立即整改，举一反三，保持长效，并在2个工作
              日内将整改结果回复至园区创建办，邮箱：<EMAIL>。
            </p>
            <table border="1" cellspacing="0" style="width: 100%">
              <tr style="height: 50px">
                <td align="center" bgcolor="#C3C3C3"><b>检查时间</b></td>
                <td colspan="3">
                  <p style="margin-left: 25px">{{ book.checkDate }}</p>
                </td>
              </tr>
              <tr style="height: 130px">
                <td align="center" style="width: 25%" bgcolor="#C3C3C3">
                  <b>责任部门</b>
                </td>
                <td style="width: 25%">
                  <p style="margin-left: 25px">{{ book.dutyDept }}</p>
                </td>
                <td align="center" style="width: 25%" bgcolor="#C3C3C3">
                  <b>配合部门</b>
                </td>
                <td style="width: 25%">
                  <p style="margin-left: 25px">{{ book.corDept }}</p>
                </td>
              </tr>
              <tr style="height: 50px">
                <td align="center" bgcolor="#C3C3C3"><b>不达标问题</b></td>
                <td colspan="3">
                  <p style="margin-left: 25px">{{ book.question }}</p>
                </td>
              </tr>
              <tr style="height: 130px">
                <td align="center" bgcolor="#C3C3C3"><b>办理情况及结果</b></td>
                <td colspan="3">
                  <p style="margin-left: 25px">{{ book.result }}</p>
                </td>
              </tr>
              <tr style="height: 50px">
                <td align="center" style="width: 25%" bgcolor="#C3C3C3">
                  <b>回复时间</b>
                </td>
                <td style="width: 25%">
                  <p style="margin-left: 25px">{{ book.replyDate }}</p>
                </td>
                <td align="center" style="width: 25%" bgcolor="#C3C3C3">
                  <b>审核领导</b>
                </td>
                <td style="width: 25%">
                  <p style="margin-left: 25px">{{ book.leaderName }}</p>
                </td>
              </tr>
            </table>
            <br />
            <p align="left">附件</p>
            <h2 align="center">不达标的问题</h2>
            <div v-for="(question, index) in questionList" :key="index">
              <div style="width: 100%; margin-top: 5px; border: 0; float: left">
                <p align="left">
                  <b>{{ question.category }}</b
                  ><span></span>
                </p>
                <p align="left">{{ index + 1 }}、{{ question.question }}</p>
              </div>
              <div
                v-if="question.fileRelList.length !== 0"
                style="width: 100%; border: 0; float: left"
              >
                <img
                  style="width: 100%; margin-top: 5px"
                  v-for="item in question.fileRelList"
                  :key="item.id"
                  :src="$http.adornUrl('/file' + item.url)"
                  class="image"
                />
              </div>
            </div>
            <!-- </div> -->
            <div v-if="value + '' === '2'">
              <p align="center">暂无此模板</p>
            </div>
          </div>
      </el-card>
    </div>
    <evaluate-send-email
      v-if="sendEmailVisible"
      ref="sendEmail"
      @change="getMapData($event)"
    ></evaluate-send-email>
  </el-dialog>
</template>

<script>
import printJS from 'print-js'
import evaluateSendEmail from '../../component/evaluateSendEmail.vue'
export default {
  components: { evaluateSendEmail },
  data () {
    return {
      bookTitle: '',
      bookDialogVisible2: false,
      book: {
        name: '',
        code: '',
        checkDate: null,
        dutyDept: '',
        corDept: '',
        question: '',
        result: '',
        replyDate: null,
        isSend: '',
        sendDate: '',
        leaderName: '',
        startDate: null,
        instanceIds: '',
        detailQuestionIds: '',
        questionList: [],
        type: ''
      },
      value: '1',
      questionList: [],
      sendEmailVisible: false
    }
  },
  methods: {
    catBook (bookId) {
      this.bookDialogVisible2 = true
      this.$http({
        url: this.$http.adornUrl('/evaluate/book/getById'),
        method: 'get',
        params: { id: bookId }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.book = resp.data.obj
          this.value = this.book.type + ''
        } else {
          this.questionList = []
        }
        this.questionList = this.book.questionList
      })
    },
    print () {
      // 打印表单
      const style = '@page {margin:0 10mm};'
      printJS({
        printable: 'printForm2',
        type: 'html',
        targetStyles: ['*'],
        style
      })
      // printJS('printForm', 'html')
    },
    produce () {
      this.$http({
        url: this.$http.adornUrl('/evaluate/book/exportBookPdf'),
        method: 'get',
        responseType: 'blob',
        params: { id: this.book.id }
      }).then(({ data }) => {
        let blob = new Blob([data], { type: 'application/pdf;chartset=UTF-8' })
        let downloadElement = document.createElement('a')
        let href = window.URL.createObjectURL(blob) // 创建下载的链接
        downloadElement.href = href
        downloadElement.download = `${this.book.code}`    // 下载后的文件名，根据需求定义
        document.body.appendChild(downloadElement)
        downloadElement.click() // 点击下载
        document.body.removeChild(downloadElement) // 下载完成移除元素
        window.URL.revokeObjectURL(href) // 释放掉blob对象
      })
      // const value = {
      //   id: 'printForm2',
      //   name: this.book.name
      // }
      // // htmlToPdf2(value)
      // htmlToPdf2(value)
    },
    produceWord (row) {
      this.$http({
        url: this.$http.adornUrl('/evaluate/book/exportBookWord'),
        method: 'get',
        responseType: 'blob',
        params: { id: this.book.id }
      }).then(({ data }) => {
        let blob = new Blob([data], { type: 'application/msword;chartset=UTF-8' })
        let url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
        // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
        let a = document.createElement('a')
        a.href = url
        a.download = this.book.name + '.doc'
        a.click()
        // 5.释放这个临时的对象url
        window.URL.revokeObjectURL(url)
      })
    },
    sendEmail () {
      this.sendEmailVisible = true
      console.log(document.querySelector('#printForm2').innerHTML)
      this.$nextTick(() => {
        this.$refs.sendEmail.dataForm.htmlContent = '<div style="margin:0 auto;width:50%" >' + document.querySelector('#printForm2').innerHTML + '</div>'
        this.$refs.sendEmail.visible = true
        this.$refs.sendEmail.title = '邮件通知'
        this.$refs.sendEmail.init('1', this.book.id)
        this.$refs.sendEmail.dataForm.title = this.book.name
      })
    },
    async getMapData (value) {
      this.sendEmailVisible = false
    }
  }

}
</script>

<style>
</style>
