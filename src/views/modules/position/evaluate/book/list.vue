<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item>
        <el-input
          v-model="dataForm.name"
          placeholder="名称"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="dataForm.code"
          placeholder="编码"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="dataForm.type"
          clearable
          placeholder="请选择问题区域"
        >
          <el-option
            v-for="item in bookModule"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" @click="queryPage()">查询</el-button>
        <el-button
          v-if="isAuth('business:evaluaterectificationbook:save')"
          type="primary"
          @click="addOrUpdateHandle()"
          >新增</el-button
        >
        <el-button
          v-if="isAuth('business:evaluaterectificationbook:delete')"
          type="danger"
          @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0"
          >批量删除</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%"
    >
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50"
      >
      </el-table-column>
      <el-table-column
        prop="name"
        header-align="center"
        align="center"
        label="名称"
      >
      </el-table-column>
      <el-table-column
        prop="code"
        header-align="center"
        align="center"
        label="编码"
      >
      </el-table-column>
      <el-table-column
        prop="dutyDept"
        header-align="center"
        align="center"
        label="责任部门"
      >
      </el-table-column>
      <!-- <el-table-column
        prop="corDept"
        header-align="center"
        align="center"
        label="配合部门">
      </el-table-column> -->
      <!-- <el-table-column
        prop="isSend"
        header-align="center"
        align="center"
        label="是否发送通知">
      </el-table-column>
         <el-table-column
        prop="sendDate"
        header-align="center"
        align="center"
        label="发送通知时间">
      </el-table-column> -->
      <el-table-column
        prop="type"
        header-align="center"
        align="center"
        label="模板的类型"
      >
        <template slot-scope="scope">
          {{
            scope.row.type == "1"
              ? "整改通知书自定义模板1"
              : "整改通知书自定义模板2"
          }}
        </template>
      </el-table-column>

      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="200"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row.id)"
            >详情</el-button
          >
          <!-- <el-button type="text" size="small" @click="produce(scope.row)"
            >pdf</el-button
          > -->
          <el-button type="text" size="small" @click="produceWord(scope.row)"
            >word</el-button
          >
          <el-button
            type="text"
            size="small"
            @click="deleteHandle(scope.row.id)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 30, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></add-or-update>
    <cat-book
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></cat-book>
  </div>
</template>

<script>
import CatBook from './catBook.vue'
import AddOrUpdate from './evaluaterectificationbook-add-or-update'
export default {
  data () {
    return {
      dataForm: {
        name: '',
        code: '',
        type: ''
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 30,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      book: [],
      bookModule: [
        {
          id: '1',
          label: '整改通知书自定义模板1',
          value: '1'
        },
        {
          id: '2',
          label: '整改通知书自定义模板2',
          value: '2'
        }
      ]
    }
  },
  components: {
    CatBook,
    AddOrUpdate
  },
  activated () {
    this.queryPage()
  },
  methods: {
    queryPage () {
      this.pageIndex = 1
      this.getDataList()
    },
    // 获取数据列表
    getDataList () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/evaluate/book/pages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'name': this.dataForm.name,
          'code': this.dataForm.code,
          'type': this.dataForm.type

        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle (val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle (id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.catBook(id)
      })
    },
    produce (row) {
      this.$http({
        url: this.$http.adornUrl('/evaluate/book/exportBookPdf'),
        method: 'get',
        responseType: 'blob',
        params: { id: row.id }
      }).then(({ data }) => {
        let blob = new Blob([data], { type: 'application/pdf;chartset=UTF-8' })
        let downloadElement = document.createElement('a')
        let href = window.URL.createObjectURL(blob) // 创建下载的链接
        downloadElement.href = href
        downloadElement.download = `${row.name}`    // 下载后的文件名，根据需求定义
        document.body.appendChild(downloadElement)
        downloadElement.click() // 点击下载
        document.body.removeChild(downloadElement) // 下载完成移除元素
        window.URL.revokeObjectURL(href) // 释放掉blob对象
      })
    },
    produceWord (row) {
      this.$http({
        url: this.$http.adornUrl('/evaluate/book/exportBookWord'),
        method: 'get',
        responseType: 'blob',
        params: { id: row.id }
      }).then(({ data }) => {
        let blob = new Blob([data], { type: 'application/msword;chartset=UTF-8' })
        let url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
        // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
        let a = document.createElement('a')
        a.href = url
        a.download = row.name + '.doc'
        a.click()
        // 5.释放这个临时的对象url
        window.URL.revokeObjectURL(url)
      })
    },
    // 删除
    deleteHandle (id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/evaluate/book/removeByIds'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': ids.join(',')
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    }
  }
}
</script>
