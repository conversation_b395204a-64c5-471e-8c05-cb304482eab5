<template>
  <el-dialog  :close-on-click-modal="false" append-to-body :visible.sync="visible">
    <el-button style="margin-left: 15px" @click="print">打印</el-button>
    <!-- <el-button style="margin-left: 15px" @click="produce">下载pdf</el-button> -->
    <el-button style="margin-left: 15px" @click="produceWord"
      >下载word</el-button
    >
    <el-card style="margin-left: 15px; margin-right: 15px">
      <div id="printForm" style="margin-top: 15px">
        <div v-if="value + '' === '1'">
          <img src="../../../../../assets/img/report_title.png" width="100%" />
          <p align="right">
            编号：<span>{{ book.name }}</span>
          </p>
          <p style="font-size: 20px; line-height: 150%">
            为切实巩固文明示范城区建设成果，迎接苏州市“社会文明程度指数”常态测评工作。
            {{ book.startDate }}
            ，
            月亮湾社工委对辖区内的文明创建工作进行了重点检查。现将检查中发现的极为突出的严重影响迎检工
            作的不达标问题，下发整改通知。请接到通知后立即整改，举一反三，保持长效，并在2个工作
            日内将整改结果回复至园区创建办，邮箱：<EMAIL>。
          </p>
          <table border="1" cellspacing="0" style="width: 100%">
            <tr style="height: 50px">
              <td align="center" bgcolor="#C3C3C3"><b>检查时间</b></td>
              <td colspan="3">
                <p style="margin-left: 25px">{{ book.checkDate }}</p>
              </td>
            </tr>
            <tr style="height: 130px">
              <td align="center" style="width: 25%" bgcolor="#C3C3C3">
                <b>责任部门</b>
              </td>
              <td style="width: 25%">
                <p style="margin-left: 25px">{{ book.dutyDept }}</p>
              </td>
              <td align="center" style="width: 25%" bgcolor="#C3C3C3">
                <b>配合部门</b>
              </td>
              <td style="width: 25%">
                <p style="margin-left: 25px">{{ book.corDept }}</p>
              </td>
            </tr>
            <tr style="height: 50px">
              <td align="center" bgcolor="#C3C3C3"><b>不达标问题</b></td>
              <td colspan="3">
                <p style="margin-left: 25px">{{ book.question }}</p>
              </td>
            </tr>
            <tr style="height: 130px">
              <td align="center" bgcolor="#C3C3C3"><b>办理情况及结果</b></td>
              <td colspan="3">
                <p style="margin-left: 25px">{{ book.result }}</p>
              </td>
            </tr>
            <tr style="height: 50px">
              <td align="center" style="width: 25%" bgcolor="#C3C3C3">
                <b>回复时间</b>
              </td>
              <td style="width: 25%">
                <p style="margin-left: 25px">{{ book.replyDate }}</p>
              </td>
              <td align="center" style="width: 25%" bgcolor="#C3C3C3">
                <b>审核领导</b>
              </td>
              <td style="width: 25%">
                <p style="margin-left: 25px">{{ book.leaderName }}</p>
              </td>
            </tr>
          </table>
          <br />
          <p align="left">附件</p>
          <h2 align="center">不达标的问题</h2>
          <div v-for="(question, index) in questionList" :key="index">
            <div style="width: 100%; margin-top: 5px; border: 0; float: left">
              <p align="left">
                <b>{{ question.category }}</b
                ><span></span>
              </p>
              <p align="left">{{ index + 1 }}、{{ question.question }}</p>
            </div>
            <div
              v-if="question.fileRelList.length === 0"
              style="width: 100%; border: 0; float: left"
            >
              <div v-for="item in question.fileRelList" :key="item.id">
                <img
                  style="width: 100%; margin-top: 5px"
                  :src="$http.adornUrl(item.url)"
                  class="image"
                  v-if="!item.url"
                />
              </div>
            </div>
          </div>
        </div>
        <div v-if="value + '' === '2'">
          <p align="center">暂无此模板</p>
        </div>
      </div>
    </el-card>
  </el-dialog>
</template>

<script>
import printJS from 'print-js'
import moment from 'moment'
export default {
  data () {
    return {
      visible: false,
      book: [],
      questionList: [],
      value: '1',
      prefix: ''
    }
  },
  activated () {
    this.prefix = window.SITE_CONFIG.baseUrl + '/file'
  },
  components: {
    moment
  },
  methods: {
    init (id) {
      this.visible = true
      this.$nextTick(() => {
        // this.$refs['dataForm'].resetFields()
        this.$http({
          url: this.$http.adornUrl('/evaluate/book/getById'),
          method: 'get',
          params: { id: id }
        }).then((resp) => {
          if (resp.data && resp.data.code === 0) {
            this.book = resp.data.obj
            this.value = this.book.type + ''
          } else {
            this.questionList = []
          }
          this.questionList = this.book.questionList
        })
      })
    },
    print () {
      // 打印表单
      const style = '@page {margin:0 10mm};'
      printJS({
        printable: 'printForm',
        type: 'html',
        targetStyles: ['*'],
        style
      })
    },
    produce () {
      this.$http({
        url: this.$http.adornUrl('/evaluate/book/exportBookPdf'),
        method: 'get',
        responseType: 'blob',
        params: { id: this.book.id }
      }).then(({ data }) => {
        let blob = new Blob([data], { type: 'application/pdf;chartset=UTF-8' })
        let downloadElement = document.createElement('a')
        let href = window.URL.createObjectURL(blob) // 创建下载的链接
        downloadElement.href = href
        downloadElement.download = `${this.book.code}`    // 下载后的文件名，根据需求定义
        document.body.appendChild(downloadElement)
        downloadElement.click() // 点击下载
        document.body.removeChild(downloadElement) // 下载完成移除元素
        window.URL.revokeObjectURL(href) // 释放掉blob对象
      })
    },
    produceWord (row) {
      this.$http({
        url: this.$http.adornUrl('/evaluate/book/exportBookWord'),
        method: 'get',
        responseType: 'blob',
        params: { id: this.book.id }
      }).then(({ data }) => {
        let blob = new Blob([data], { type: 'application/msword;chartset=UTF-8' })
        let url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
        // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
        let a = document.createElement('a')
        a.href = url
        a.download = this.book.name + '.doc'
        a.click()
        // 5.释放这个临时的对象url
        window.URL.revokeObjectURL(url)
      })
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/evaluaterectificationbook/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>
