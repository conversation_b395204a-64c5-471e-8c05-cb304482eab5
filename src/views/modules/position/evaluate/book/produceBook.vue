<template>
  <div>
    <el-dialog
      :title="bookTitle"
      :visible.sync="bookDialogVisible"
      width="50%"
      :close-on-click-modal="false"
    >
      <div style="margin-left: 15px; margin-right: 15px">
        <el-select
          v-model="value"
          placeholder="请选择"
          @change="changModule"
          style="width: 100%"
        >
          <el-option
            v-for="item in bookModule"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div>
      <br />
      <el-card style="margin-left: 15px; margin-right: 15px">
        <div v-if="value + '' === '1'" class="inputDeep">
          <p align="left" style="color: red">请确认责任部门（点击可修改）：</p>
          <img src="../../../../../assets/img/report_title.png" width="100%" />
          <p align="right">
            编号：<span>{{ book.name }}</span>
          </p>
          <p style="font-size: 20px; line-height: 150%">
            为切实巩固文明示范城区建设成果，迎接苏州市“社会文明程度指数”常态测评工作。
            <span
              style="width: 15%"
              type="text"
              @click="canVim = false"
              v-if="canVim"
              >{{ book.startDate }}</span
            >
            <el-input
              style="width: 15%"
              type="text"
              @blur="canVim = true"
              v-if="!canVim"
              autofocus
              v-model="book.startDate"
            ></el-input>
            ，
            月亮湾社工委对辖区内的文明创建工作进行了重点检查。现将检查中发现的极为突出的严重影响迎检工
            作的不达标问题，下发整改通知。请接到通知后立即整改，举一反三，保持长效，并在2个工作
            日内将整改结果回复至园区创建办，邮箱：<EMAIL>。
          </p>
          <table border="1" cellspacing="0" style="width: 100%; height: 20px">
            <tr>
              <td><p align="center">检查时间</p></td>
              <td colspan="3">
                <el-date-picker
                  style="width: 100%"
                  v-model="book.checkDate"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="选择日期"
                >
                </el-date-picker>
              </td>
            </tr>
            <tr>
              <td align="center" style="width: 25%">责任部门</td>
              <td style="width: 25%">
                <div class="inputDeep">
                  <el-input
                    type="textarea"
                    :rows="4"
                    v-model="book.dutyDept"
                  ></el-input>
                </div>
              </td>
              <td align="center" style="width: 25%">配合部门</td>
              <td style="width: 25%">
                <el-input
                  type="textarea"
                  :rows="4"
                  v-model="book.corDept"
                ></el-input>
              </td>
            </tr>
            <tr>
              <td align="center">不达标问题</td>
              <td colspan="3">
                <input class="test_el-input__inner" v-model="book.question" />
              </td>
            </tr>
            <tr>
              <td align="center">办理情况及结果</td>
              <td colspan="3">
                <el-input
                  class="input_textarea"
                  type="textarea"
                  :rows="6"
                  v-model="book.result"
                ></el-input>
              </td>
            </tr>
            <tr>
              <td align="center" style="width: 25%">回复时间</td>
              <td style="width: 25%">
                <el-date-picker
                  style="width: 100%"
                  v-model="book.replyDate"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="选择日期"
                >
                </el-date-picker>
              </td>
              <td align="center" style="width: 25%">审核领导</td>
              <td style="width: 25%">
                <input class="test_el-input__inner" v-model="book.leaderName" />
              </td>
            </tr>
          </table>
          <br />
          <p align="left">附件</p>
          <h2 align="center">不达标的问题</h2>
          <div v-for="(question, index) in questionList" :key="index">
            <div>
              <p align="left">
                <b>{{ question.category }}</b
                ><span></span>
              </p>
              <p align="left">{{ index + 1 }}、{{ question.question }}</p>
            </div>
            <div v-if="question.fileRelList">
              <img
                style="width: 90%; margin-top: 5px"
                v-for="item in question.fileRelList"
                :key="item.id"
                :src="$http.adornUrl('/file' + item.url)"
                class="image"
              />
            </div>
          </div>
        </div>
        <div v-if="value + '' === '2'">
          <p align="center">暂无此模板</p>
        </div>
      </el-card>
      <div style="margin-top: 5px; margin-left: 20px">
        <span slot="footer" class="dialog-footer">
          <!-- <el-button @click="bookDialogVisible = false">取 消</el-button> -->
          <el-button type="primary" @click="submitProduceBook" size="small"
            >确认生成</el-button
          >
        </span>
      </div>
    </el-dialog>

    <el-dialog
    append-to-body
      :visible.sync="submitDialogVisible"
      width="30%"
      :showClose="false"
    >
      <div align="center">
        <el-form :model="book" label-width="80px">
          <el-form-item label="名称">
            <el-input v-model="book.name"></el-input>
          </el-form-item>
          <el-form-item label="编码">
            <el-input v-model="book.code"></el-input>
          </el-form-item>
        </el-form>

        <br />
        <el-button icon="el-icon-printer" @click="submitDialogVisible = false"
          >放弃操作</el-button
        >
        <el-button icon="el-icon-printer" @click="submitBook"
          >确认生成</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data () {
    return {
      canVim: true,
      bookTitle: '',
      bookDialogVisible: false,
      submitDialogVisible: false,
      book: {
        name: '',
        code: '',
        checkDate: null,
        dutyDept: '',
        corDept: '',
        question: '',
        result: '',
        replyDate: null,
        isSend: '',
        sendDate: '',
        leaderName: '',
        startDate: null,
        instanceIds: '',
        detailQuestionIds: '',
        questionList: [],
        type: ''
      },
      value: '1',
      questionList: [],
      bookModule: [
        {
          id: '1',
          label: '整改通知书自定义模板1',
          value: '1'
        },
        {
          id: '2',
          label: '整改通知书自定义模板2',
          value: '2'
        }
      ]
    }
  },
  methods: {
    changModule () { },
    getQuestionList (instanceIds, detailQuestionIds, created) {
      this.book.instanceIds = instanceIds
      this.book.detailQuestionIds = detailQuestionIds
      this.value = '1'
      this.book.startDate = (new Date().getFullYear()) + '-' + (new Date().getMonth() + 1) + '-' + (new Date().getDate())
      this.book.checkDate = created

      this.bookDialogVisible = true
      this.book.questionList = []
      this.questionList = []
      this.$http({
        url: this.$http.adornUrl('/evaluate/questionOpInstance/getFileList'),
        method: 'get',
        params: { ids: instanceIds, detailQuestionIds: detailQuestionIds }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.questionList = resp.data.obj
        } else {
          this.questionList = []
        }
        this.book.questionList = this.questionList
        let a = 0
        if (this.questionList) {
          a = this.questionList.length
        }
        this.book.question = '共涉及' + a + '个问题'
      })
      this.bookTitle = '生成整改通知书'
    },
    submitProduceBook () {
      this.submitDialogVisible = true
      this.book.type = this.value
      this.book.name = ''
      this.book.code = ''
    },
    submitBook () {
      this.$http({
        url: this.$http.adornUrl('/evaluate/book/insertBook'),
        method: 'post',
        data: this.book
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.bookDialogVisible = false
          this.submitDialogVisible = false
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 500,
            onClose: () => {
              this.$emit('refreshDataList')
            }
          })
        }
        // this.handleQuery()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.el-date-editor--daterange.el-input__inner {
  width: 450%;
}

.text_el-textarea__inner {
  display: block;
  resize: none;
  padding: 5px 15px;
  line-height: 1.5;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
  font-size: inherit;
  color: #606266;
  background-color: #fff;
  background-image: none;
  border: 0px solid #dcdfe6;
  border-radius: 4px;
  -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}
.test_el-input__inner {
  -webkit-appearance: none;
  background-color: #fff;
  background-image: none;
  border-radius: 4px;
  border: 0px solid #dcdfe6;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  color: #606266;
  display: inline-block;
  font-size: inherit;
  height: 40px;
  line-height: 40px;
  outline: 0;
  padding: 0 15px;
  -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  width: 100%;
}
/* 利用穿透，设置input边框隐藏 */
.inputDeep /deep/ .el-input__inner {
  border: 0;
}
/* 如果你的 el-input type 设置成textarea ，就要用这个了 */
.inputDeep /deep/ .el-textarea__inner {
  border: 0;
  resize: none; /* 这个是去掉 textarea 下面拉伸的那个标志，如下图 */
}
</style>