<template>
  <div>
    <el-form
      :inline="true"
      ref="dataForm"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <el-form-item prop="name" label="问题标题">
        <el-input
          v-model="dataForm.name"
          placeholder="问题标题"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" @click="getDataList()">查询</el-button>
        <el-button icon="el-icon-refresh" type="warning" @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      style="width: 100%"
    >
      <el-table-column align="center" type="index" label="序号" width="80">
      </el-table-column>
      <el-table-column align="center" prop="code" label="问题编号">
      </el-table-column>
      <el-table-column align="center" prop="questionName" label="问题标题">
      </el-table-column>
      <el-table-column align="center" prop="countNum" label="超时天数">
        <template slot-scope="scope"> {{ scope.row.countNum }}天 </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="goDetail(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="block">
      <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="dataForm.currentPage"
        :page-sizes="[10, 20, 30, 50, 100]"
        :page-size="dataForm.pageSize"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      // 查询条件
      dataForm: {
        key: '',
        name: '',
        currentPage: 1,
        pageSize: 30
      },
      total: 0,
      dataList: [],
      dataListLoading: false
    }
  },
  activated () {
    this.handleQuery()
  },
  methods: {
    getDataList () {
      this.dataForm.currentPage = 1
      this.handleQuery()
    },
    handleQuery () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/evaluate/questionOpInstance/overQuestionList'),
        method: 'post',
        data: this.$http.adornData(this.dataForm)
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.total = data.obj.total
        } else {
          this.dataList = []
          this.total = 0
        }
        this.dataListLoading = false
      })
    },
    // 去详情页面
    goDetail (row) {
      this.$router.push({
        path: '/evaluateQuestionDetail',
        query: { id: row.instanceId, detailQuestionId: row.questionId, categoryId: row.categoryId, positionId: row.positionId }
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.dataForm.pageSize = val
      this.dataForm.currentPage = 1
      this.handleQuery()
    },
    // 当前页
    currentChangeHandle (val) {
      this.dataForm.currentPage = val
      this.handleQuery()
    },
    resetForm () {
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.handleQuery()
      })
    }
  }
}
</script>
