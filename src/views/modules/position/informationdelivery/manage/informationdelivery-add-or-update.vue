<template>
  <el-dialog
    append-to-body
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      label-width="120px"
    >
      <el-form-item
        label="标题"
        prop="title"
        :error="titleError"
        @keyup.enter.native="dataFormSubmit()"
      >
        <el-input
          style="width: 100%"
          v-model="dataForm.title"
          placeholder="标题"
        ></el-input>
      </el-form-item>

      <el-form-item
        label="副标题"
        prop="viceTitle"
        @keyup.enter.native="dataFormSubmit()"
      >
        <el-input
          style="width: 100%"
          v-model="dataForm.viceTitle"
          placeholder="副标题"
        ></el-input>
      </el-form-item>

      <el-form-item label="是否启用外链" prop="outerChain">
        <el-radio-group v-model="dataForm.outerChain">
          <el-radio :label="true">启用</el-radio>
          <el-radio :label="false">禁用</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item
        v-if="dataForm.outerChain"
        label="外链地址"
        prop="link"
        @keyup.enter.native="dataFormSubmit()"
      >
        <el-input
          style="width: 100%"
          v-model="dataForm.link"
          placeholder="外链地址"
        ></el-input>
      </el-form-item>
      <el-form-item v-if="!dataForm.outerChain" label="正文内容" prop="content">
        <Editor :taskDesc.sync="dataForm.taskDesc"></Editor>
      </el-form-item>

      <el-form-item v-if="!dataForm.outerChain" label="附件">
        <el-upload
          :action="this.$http.adornUrl('/file/oss/upload')"
          multiple
          :data="{ serverCode: this.serverCode, media: false }"
          :file-list="fileList2"
          :on-preview="handlePictureCardPreview2"
          :on-remove="handleRemove2"
          :on-success="handleAvatarSuccess2"
          :before-upload="beforeUpload2"
          accept=".png,.jpg,.jpeg,.tiff,.bpm,.webp"
          :headers="myHeaders"
        >
          <el-button icon="el-icon-upload2" size="small" type="primary"
            >点击上传</el-button
          >
          <span style="margin-left: 5px">
            只支持 png, jpg, jpeg, bmp, webp, tiff 格式
          </span>
        </el-upload>
      </el-form-item>

      <el-form-item
        label="排序"
        prop="sequence"
        @keyup.enter.native="dataFormSubmit()"
      >
        <el-input-number
          style="width: 100%"
          v-model="dataForm.sequence"
          type="number"
        ></el-input-number>
      </el-form-item>
      <el-form-item
        label="所在位置"
        prop="locationCode"
        :error="locationCodeError"
      >
        <el-select
          style="width: 100%"
          v-model="dataForm.locationCode"
          placeholder="请选择位置"
          clearable
        >
          <el-option
            v-for="item in options"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item
        v-if="
          dataForm.locationCode === 'banner' ||
          dataForm.locationCode === 'activity_dynamics' ||
          dataForm.locationCode === 'wmyq_news' ||
          dataForm.locationCode === 'wmyq_message' ||
          dataForm.locationCode === 'wmyq_notice'
        "
        label="缩略图"
        prop="banner"
      >
        <el-upload
          :action="this.$http.adornUrl('/file/oss/upload')"
          limit="1"
          :data="{ serverCode: this.serverCode, media: false }"
          :file-list="fileList"
          list-type="picture-card"
          :on-preview="handlePictureCardPreview"
          :on-remove="handleRemove"
          :on-success="handleAvatarSuccess"
          accept=".png,.jpg,.jpeg"
          :before-upload="beforeUpload"
          :headers="myHeaders"
        >
          <i class="el-icon-plus"></i>
        </el-upload>
        <div slot="tip" class="el-upload__tip">
          PNG,JPG格式， 5MB以内、 750X240像素
        </div>

        <el-dialog :visible.sync="dialogVisible" append-to-body>
          <img width="100%" :src="dialogImageUrl" alt="" />
        </el-dialog>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button
        type="primary"
        :disabled="submitDisable"
        @click="dataFormSubmit()"
        >确定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import Editor from '../../task/dispatchManagement/component/editor.vue'
import Vue from 'vue'
export default {
  components: {
    Editor
  },
  data () {
    let validateFileList = (rule, value, callback) => {
      console.log(this.fileList)
      if (!this.fileList) {
        callback(new Error('轮播图不能为空'))
      } else {
        if (this.fileList.length === 0) {
          callback(new Error('轮播图不能为空'))
        } else {
          callback()
        }
      }
    }
    return {
      submitDisable: false,
      visible: false,
      dataForm: {
        id: null,
        version: null,
        title: '',
        viceTitle: '',
        content: '',
        outerChain: false,
        link: '',
        locationCode: '',
        state: 1,
        sequence: '',
        taskDesc: '',
        fileUrlList: [],
        bannerUrlList: [],
        source: 1
      },
      dataRule: {
        title: [
          { required: true, message: '标题不能为空', trigger: 'blur' }
        ],
        locationCode: [
          { required: true, message: '所在位置不能为空', trigger: 'change' }
        ],
        sequence: [
          { required: true, message: '排序不能为空', trigger: 'blur' }
        ],
        banner: [
          { required: true, validator: validateFileList, trigger: 'change' }
        ],
        link: [
          { required: true, message: '外链地址不能为空', trigger: 'blur' }
        ]
      },
      titleError: null,
      viceTitleError: null,
      contentError: null,
      outerChainError: null,
      linkError: null,
      locationCodeError: null,
      stateError: null,
      sequenceError: null,
      options: [],
      fileList: [],
      fileList2: [],
      serverCode: 'LocalServer',
      myHeaders: { Authorization: sessionStorage.getItem('Authorization') },
      dialogVisible: false,
      dialogImageUrl: '',
      fileUrlList: [],
      fileUrlList2: []
    }
  },
  methods: {
    init (id) {
      this.getInformationDict()
      this.dataForm.link = null
      this.dataForm.taskDesc = null
      this.fileList = []
      this.fileList2 = []
      this.fileUrlList = []
      this.fileUrlList2 = []
      this.dataForm.id = id || null
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/position/information/release/getDetail`),
            method: 'get',
            params: this.$http.adornParams({ id: this.dataForm.id })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
              this.dataForm.taskDesc = this.dataForm.content
              if (this.dataForm.fileUrlList) {
                this.dataForm.fileUrlList.forEach(data => {
                  const file = {
                    name: data.fileName,
                    url: window.SITE_CONFIG.baseUrl + '/file' + data.filePath
                  }
                  this.fileList2.push(file)
                })
              }
              this.dataForm.bannerUrlList.forEach(data => {
                const file = {
                  name: '',
                  url: window.SITE_CONFIG.baseUrl + '/file' + data
                }
                this.fileList.push(file)
              })
            }
          })
        }
      })
    },
    // 获取资讯类型字典
    getInformationDict () {
      const code = 'Information_type'
      this.$http({
        url: this.$http.adornUrl('/admin/dict/parent'),
        method: 'get',
        params: {
          code
        }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.options = resp.data.obj
        } else {
          this.options = []
        }
      })
    },
    beforeUpload (file) {
      let filePath = file.name
      let index = filePath.lastIndexOf('.')
      let ext = filePath.substring(index + 1)
      const isImage = ['png', 'jpg'].indexOf(ext.toLowerCase()) !== -1
      const isLt5M = file.size / 1024 / 1024 < 5

      if (!isImage) {
        this.$message.error('上传的格式只能是PNG,JPG格式')
      }
      if (!isLt5M) {
        this.$message.error('上传图片大小不能超过 5MB!')
      }
      return isImage && isLt5M
    },
    handleRemove (file, fileList) {
      if (this.fileList.indexOf(file) !== -1) {
        this.fileList.splice(this.fileList.indexOf(file), 1)
      }
    },
    handlePictureCardPreview (file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleAvatarSuccess (res, file) {
      const a = {
        name: res.obj.fileName,
        url: window.SITE_CONFIG.baseUrl + '/file' + res.obj.path
      }
      this.fileList.push(a)
      this.dialogImageUrl = window.SITE_CONFIG.baseUrl + '/file' + res.obj.path
      this.$refs['dataForm'].validate()
    },

    beforeUpload2 (file) {
      const isImage = this.isImage(file)
      if (!isImage) {
        this.$message.error('上传文件只能是图片格式')
      }
      return isImage
    },
    handleRemove2 (file, fileList) {
      if (this.fileList2.indexOf(file) !== -1) {
        this.fileList2.splice(this.fileList2.indexOf(file), 1)
      }
    },
    handlePictureCardPreview2 (file) {
      // this.dialogImageUrl = file.url
      // this.dialogVisible = true
    },
    handleAvatarSuccess2 (res, file) {
      const a = {
        name: res.obj.fileName,
        url: window.SITE_CONFIG.baseUrl + '/file' + res.obj.path
      }
      this.fileList2.push(a)
    },
    // 判断文件后缀是否是图片格式
    isImage (file) {
      let filePath = file.name
      let index = filePath.lastIndexOf('.')
      let ext = filePath.substring(index + 1)
      return this.isAssetTypeAnImage(ext)
    },
    isAssetTypeAnImage (fileExt) {
      return ['png', 'jpg', 'jpeg', 'bmp', 'webp', 'tiff'].indexOf(fileExt.toLowerCase()) !== -1
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.submitDisable = true
          this.fileList.forEach(data => {
            data.path = data.url.replace(window.SITE_CONFIG.baseUrl + '/file', '')
            this.fileUrlList.push(data.path)
          })
          this.dataForm.bannerUrlList = this.fileUrlList

          this.fileList2.forEach(data => {
            const file = {
              fileName: data.name,
              filePath: data.url.replace(window.SITE_CONFIG.baseUrl + '/file', '')
            }
            this.fileUrlList2.push(file)
          })
          this.dataForm.fileUrlList = this.fileUrlList2
          this.dataForm.content = this.dataForm.taskDesc
          this.$http({
            url: this.$http.adornUrl(`/position/information/release/${!this.dataForm.id ? 'create' : 'edit'}`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.submitDisable = false
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
              this.submitDisable = false
            } else {
              this.$message.error(data.msg)
              this.submitDisable = false
            }
          })
        }
      })
    }
  }
}
</script>
