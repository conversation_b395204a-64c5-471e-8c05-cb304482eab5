<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item>
        <el-input
          v-model="dataForm.title"
          placeholder="标题"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-select
          v-model="dataForm.locationCode"
          placeholder="请选择位置"
          clearable
        >
          <el-option
            v-for="item in options"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.state" placeholder="请选择状态" clearable>
          <el-option
            v-for="item in states"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
<!--      <el-form-item>-->
<!--        <el-select v-model="dataForm.source" placeholder="请选择来源" clearable>-->
<!--          <el-option-->
<!--            v-for="item in sources"-->
<!--            :key="item.value"-->
<!--            :label="item.label"-->
<!--            :value="item.value"-->
<!--          >-->
<!--          </el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-button icon="el-icon-search" @click="queryPage()">查询</el-button>
        <el-button icon="el-icon-plus" type="primary" @click="addOrUpdateHandle()">新增</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%"
    >
      <el-table-column
        prop="title"
        header-align="center"
        align="center"
        label="标题"
      >
      </el-table-column>
      <el-table-column
        prop="sequence"
        header-align="center"
        align="center"
        label="排序"
      >
      </el-table-column>
      <el-table-column
        prop="publisher"
        header-align="center"
        align="center"
        label="创建人"
      >
      </el-table-column>
      <el-table-column
        prop="outerChain"
        header-align="center"
        align="center"
        label="是否是外链"
      >
        <template slot-scope="scope">
          <el-tag
            v-if="scope.row.outerChain === false"
            size="small"
            type="danger"
            >否</el-tag
          >
          <el-tag v-else size="small">是</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="location"
        header-align="center"
        align="center"
        label="位置"
      >
      </el-table-column>
      <el-table-column
        prop="state"
        header-align="center"
        align="center"
        label="状态"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.state === 1" size="small">草稿</el-tag>
          <el-tag v-if="scope.row.state === 2" size="small">已撤回</el-tag>
          <el-tag v-if="scope.row.state === 4" size="small">审核中</el-tag>
          <el-tag v-if="scope.row.state === 8" size="small">审核不通过</el-tag>
          <el-tag v-if="scope.row.state === 16" size="small">上线中</el-tag>
          <el-tag v-if="scope.row.state === 32" size="small">已下线</el-tag>
        </template>
      </el-table-column>
<!--      <el-table-column-->
<!--        prop="source"-->
<!--        header-align="center"-->
<!--        align="center"-->
<!--        label="来源"-->
<!--      >-->
<!--        <template slot-scope="scope">-->
<!--          <el-tag v-if="scope.row.source === 1" size="small">后台</el-tag>-->
<!--          <el-tag v-if="scope.row.source === 2" size="small">公众号</el-tag>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            v-show="checkSubmit(scope.row.state)"
            size="small"
            @click="submitApproval(scope.row.id)"
            >提交审核</el-button
          >
          <!--  v-show="checkSubmit(scope.row.state)" -->
          <el-button
            type="text"
            size="small"
            v-show="checkSubmit(scope.row.state)"
            @click="addOrUpdateHandle(scope.row.id)"
            >编辑</el-button
          >
          <el-button
            type="text"
            v-show="checkSubmit(scope.row.state)"
            size="small"
            @click="deleteHandle(scope.row.id)"
            >删除</el-button
          >
          <el-button
            type="text"
            v-show="scope.row.state === 4"
            size="small"
            @click="revoke(scope.row.id)"
            >撤回</el-button
          >
          <el-button
            type="text"
            v-show="scope.row.state === 16"
            size="small"
            @click="offShelf(scope.row.id)"
            >下架</el-button
          >
          <el-button
            type="text"
            v-show="scope.row.state === 32"
            size="small"
            @click="onShelf(scope.row.id)"
          >重新上架</el-button
          >
          <el-button type="text" size="small" @click="getDetail(scope.row.id)"
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 30, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './informationdelivery-add-or-update'
export default {
  data () {
    return {
      dataForm: {
        title: '',
        state: null,
        locationCode: ''
      },
      options: [],
      states: [{
        value: 1,
        label: '草稿'
      }, {
        value: 2,
        label: '已撤回'
      }, {
        value: 4,
        label: '审核中'
      }, {
        value: 8,
        label: '审核不通过'
      }, {
        value: 16,
        label: '上线中'
      }, {
        value: 32,
        label: '已下线'
      }],
      sources: [{
        value: 1,
        label: '后台设置'
      }, {
        value: 2,
        label: '公众号'
      }],
      dataList: [],
      pageIndex: 1,
      pageSize: 30,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false
    }
  },
  components: {
    AddOrUpdate
  },
  activated () {
    this.getInformationDict()
    this.queryPage()
  },
  methods: {
    // 获取资讯类型字典
    getInformationDict () {
      const code = 'Information_type'
      this.$http({
        url: this.$http.adornUrl('/admin/dict/parent'),
        method: 'get',
        params: {
          code
        }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.options = resp.data.obj
        } else {
          this.options = []
        }
      })
    },
    // 判断是否有提交审核功能
    checkSubmit (state) {
      let arr = [1, 2, 8, 32]
      return arr.includes(state)
    },
    queryPage () {
      this.pageIndex = 1
      this.getDataList()
    },
    // 获取数据列表
    getDataList () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/position/information/release/page'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'title': this.dataForm.title,
          'state': this.dataForm.state,
          'locationCode': this.dataForm.locationCode,
          'source': this.dataForm.source
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle (val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle (id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle (id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/position/information/release/removeByIds'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': ids.join(',')
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 详情
    getDetail (id) {
      this.$router.push({ path: '/informationDetail', query: { id: id } })
    },
    // 提交审核
    submitApproval (id) {
      this.$confirm(`确定进行提交审核操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/position/information/release/submit'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '提交审核成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
      this.getDataList()
    },
    // 撤回
    revoke (id) {
      this.$confirm(`确定进行撤回操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/position/information/release/withdraw'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '撤回成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
      this.getDataList()
    },
    // 下架
    offShelf (id) {
      this.$confirm(`确定进行下架操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/position/information/release/offShelf'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '下架成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
      this.getDataList()
    },
    // 上架
    onShelf (id) {
      this.$confirm(`确定进行重新上架操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/position/information/release/onShelf'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '上架成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
      this.getDataList()
    }
  }
}
</script>
