<!-- 判断审核通过及不通过页面 -->
<template>
  <el-dialog
    :title="'审核操作'"
    :close-on-click-modal="false"
    :visible.sync="visible"
    append-to-body
  >
    <div>审核通过后信息将发布到相应的板块，是否确认此操作</div>
    <br />
    <div>
      <el-button @click="pass" type="primary">审核通过</el-button>
      <el-button @click="refuse">审核不通过</el-button>
      <approval-refuse
        v-if="is_refuse"
        ref="approvalRefuse"
        @refreshDataList="disappear"
      />
    </div>
  </el-dialog>
</template>

<script>
import approvalRefuse from './approval-refuse'
export default {
  components: { approvalRefuse },
  data () {
    return {
      id: '',
      visible: false,
      is_refuse: false
    }
  },
  methods: {
    init (id) {
      this.visible = true
      this.id = id
    },
    pass () {
      this.$http({
        url: this.$http.adornUrl('/position/information/release/agree'),
        method: 'get',
        params: this.$http.adornParams({
          'id': this.id
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.$message({
            message: '提交审核成功',
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    refuse () {
      this.is_refuse = true
      this.$nextTick(() => {
        this.$refs.approvalRefuse.init(this.id)
      })
    },
    disappear () {
      this.visible = false
      this.is_refuse = false
      this.$emit('refreshDataList')
    }
  }
}
</script>
