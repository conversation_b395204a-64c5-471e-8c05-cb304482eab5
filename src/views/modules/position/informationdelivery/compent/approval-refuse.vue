<!-- 审核不通过页面 -->
<template>
  <el-dialog
    :title="'审核不通过原因'"
    :close-on-click-modal="false"
    append-to-body
    :visible.sync="visible"
  >
    <div>
      <div>
        <el-input
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 4 }"
          placeholder="请输入不通过原因"
          v-model="dataForm.fbDesc"
        >
        </el-input>
      </div>
      <div style="margin-top: 15px">
        <el-button type="primary" @click="sumbit()">提交</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      dataForm: {
        id: '',
        fbDesc: ''
      },
      visible: false,
      fbDesc: ''
    }
  },
  methods: {
    init (id) {
      this.visible = true
      this.dataForm.id = id
    },
    sumbit () {
      this.$http({
        url: this.$http.adornUrl('/position/information/release/refuse'),
        method: 'post',
        data: this.$http.adornData(this.dataForm)
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.$message({
            message: '提交审核成功',
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        } else {
          this.$message.error(data.msg)
        }
      })
    }
  }
}
</script>
