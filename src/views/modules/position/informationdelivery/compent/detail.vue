<template>
  <div class="detail">
    <div>
      <div>
        <div class="text_size">
          <span style="color: rgb(127, 127, 127)">标题</span>
          <br />
          <p style="margin-right: 40px">{{ information.title }}</p>
        </div>
        <div class="text_size">
          <span style="color: rgb(127, 127, 127)">副标题</span>
          <br />
          <p style="margin-right: 40px">{{ information.viceTitle }}</p>
        </div>
        <div class="text_size">
          <span style="color: rgb(127, 127, 127)">排序</span>
          <br />
          <p style="margin-right: 40px">{{ information.sequence }}</p>
        </div>
        <div class="text_size">
          <span style="color: rgb(127, 127, 127)">状态</span>
          <br />
          <p style="margin-right: 40px">{{ information.stateName }}</p>
        </div>

        <div class="text_size">
          <span style="color: rgb(127, 127, 127)">选择的位置</span>
          <br />
          <p style="margin-right: 40px">{{ information.locationName }}</p>
        </div>
        <div class="text_size">
          <span style="color: rgb(127, 127, 127)">是否是外链</span>
          <br />
          <p style="margin-right: 40px">{{ information.outerChain ? "是" : "否" }}</p>
        </div>
        <div class="text_size">
          <span style="color: rgb(127, 127, 127)">外链</span>
          <br />
          <p style="margin-right: 40px">{{ information.link }}</p>
        </div>

        <div style="width: 100%; clear: both"></div>

        <div >
          <span style="color: rgb(127, 127, 127)">正文内容</span>
          <br />
          <div>
            <p style="margin-right: 40px" v-html="information.content"/>
          </div>
        </div>

        <div style="width: 100%; clear: both"></div>

        <div class="text_size" v-if="information.fileUrlList">
          <span style="color: rgb(127, 127, 127)">附件展示</span>
          <br/><br/>
          <div>
            <el-link
              style="margin-left: 10px"
              v-for="item in information.fileUrlList"
              :key="item"
              type="info"
              :href="$http.adornUrl('/file' + item.filePath)"
            >
              <i class="el-icon-document" style="color: red"/>
              {{ item.fileName }}
            </el-link>
          </div>
        </div>

        <div style="width: 100%; clear: both"></div>

        <div v-if="information.bannerUrlList">
          <span style="color: rgb(127, 127, 127)">缩略图展示</span>
          <br />
          <div>
            <el-row :gutter="20">
              <el-col
                :span="4"
                v-for="(item, index) in information.bannerUrlList"
                :key="index"
              >
                <img
                  @click="openImage(item)"
                  class="image"
                  :src="$http.adornUrl('/file' + item)"
                  style="width: 95%; margin-top: 10px"
                />
              </el-col>
              <!-- 图片展示 -->
              <el-dialog append-to-body :visible.sync="dialogImageDialogVisible">
                <img
                  width="100%"
                  :src="$http.adornUrl('/file' + dialogImageUrl)"
                  alt=""
                />
              </el-dialog>
            </el-row>
          </div>
        </div>
        <div style="width: 100%; clear: both"></div>
      </div>
    </div>

    <div v-if="isApproval">
<!--      <el-button  v-if="isApproval" @click="approval" type="primary">审核</el-button>-->
<!--      <approval   v-if="approvaled" ref="approval" @refreshDataList="getDetail" />-->
      <el-divider></el-divider>
      <div class="line"></div>
    </div>

    <h4>审核日志</h4>
    <el-divider></el-divider>
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column
        header-align="center"
        align="center"
        prop="operator"
        label="用户"
      >
      </el-table-column>
      <el-table-column
        header-align="center"
        align="center"
        prop="operateDes"
        label="操作记录"
      >
      </el-table-column>
      <el-table-column
        header-align="center"
        align="center"
        prop="remark"
        label="审核说明"
      >
      </el-table-column>
      <el-table-column
        header-align="center"
        align="center"
        prop="createDate"
        label="操作时间"
      >
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import Approval from './approval'
export default {
  components: { Approval },
  data () {
    return {
      id: '',
      isApproval: false,
      approvaled: false,
      information: {},
      tableData: [],
      fileArr: [],
      fileUrlList: [],
      dialogImageDialogVisible: false,
      dialogImageUrl: ''
    }
  },
  activated () {
    if (this.$route.query.id) {
      this.id = this.$route.query.id
      this.getDetail()
    }
  },
  methods: {
    getDetail () {
      this.$http({
        url: this.$http.adornUrl(`/position/information/release/getDetail`),
        method: 'get',
        params: this.$http.adornParams({ id: this.id })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.information = data.obj
          if (data.obj.state === 4) {
            this.isApproval = true
          } else {
            this.isApproval = false
          }
          this.tableData = data.obj.logList
        }
      })
    },
    approval () {
      this.approvaled = true
      this.$nextTick(() => {
        this.$refs.approval.init(this.id)
      })
    },

    downLoad (file) {

    },
    // 图片对话框
    openImage (url) {
      this.dialogImageDialogVisible = true
      this.dialogImageUrl = url
    }
  }
}
</script>

<style lang="scss" scoped>
.detail {
  .content {
    width: 100%;
    .item-list {
      width: 100%;
      font-size: 16px;
      line-height: 30px;
      margin-bottom: 25px;
      display: flex;
      flex-direction: row;
      .label {
        width: 120px;
        color: #7f7f7fd8;
        margin-right: 50px;
        text-align: right;
      }
      .text {
        flex: auto;
        .point-list {
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;
          margin-bottom: 15px;
          .name {
            width: 400px;
            padding-right: 15px;
          }
          .number {
            flex-basis: 50px;
          }
          .info {
            flex: 1;
            min-width: 0;
            color: #1890ff;
            word-break: break-all;
          }
        }
      }
    }
  }
  .button {
    margin: 35px 0;
    text-align: center;
    .el-button {
      margin: 0 20px;
    }
  }
  .el-divider {
    margin: 8px 0;
    background: 0 0;
    border-top: 1px solid #e6ebf5;
  }
  .item .el-form-item__label {
    /* color: wheat; */
    size: 20px;
  }
  .text_size {
    width: 25%;
    float: left;
    text-align: left;
    margin-top: 20px;
    word-wrap: break-word;
    word-break: normal;
    height: 80px;
  }
}
// 多行显示省略号，数字3为超出3行显示，
p {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}
</style>
