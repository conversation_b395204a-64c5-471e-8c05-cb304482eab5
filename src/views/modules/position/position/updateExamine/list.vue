<template>
  <div>
    <div>
      <commonList v-bind:queryData="queryData" v-if="flag"></commonList>
    </div>
  </div>
</template>

<script>
import commonList from '../commonList.vue'
export default {
  components: {
    commonList
  },
  data () {
    return {
      queryData: {
        number: '3',
        name: '点位报修上下架管理',
        stateList: [32, 64]
      },
      flag: false
    }
  },
  created () {
    this.flag = true
  }

}
</script>

<style>
</style>