<template>
  <div class="mod-config map" style="position: relative">
    <el-form
      :inline="true"
      :model="queryParams"
      @keyup.enter.native="queryPage()"
      class="formfix"
    >
      <el-form-item class="select-box">
        <el-select
          multiple
          v-model="queryParams.regionCodeList"
          placeholder="所属区域"
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="dict in regionList"
            :key="dict.id"
            :label="dict.name"
            :value="dict.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="select-box">
        <el-select
          multiple
          v-model="queryParams.categoryCodeList"
          placeholder="所属分类"
          clearable
          style="width: 100%"
        >
<!--          <el-option-->
<!--            v-for="dict in categoryList"-->
<!--            :key="dict.id"-->
<!--            :label="dict.name"-->
<!--            :value="dict.code"-->
<!--          />-->
          <el-option
            v-for="dict in categoryList"
            :key="dict.id"
            :label="dict.name"
            :value="dict.code">
            <span><img :src="dict.img" style="width:17px;height:23px;"></span>
            <span>{{dict.name}}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input style="width: 200px"
          v-model="queryParams.positionName"
          placeholder="点位名称"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="queryPage()" type="primary">查询</el-button>
        <el-button  type="primary"  @click="openRangingTool"><span :class="{'button-text': !buttonVisible}">测距</span></el-button>
        <el-button  type="primary" @click="openMark"><span :class="{'button-text': !buttonMark}">标记</span></el-button>
      </el-form-item>
    </el-form>
    <div style="width: 100%; height: 100vh">
      <div class="container" >
        <!--
        amap-manager： 地图管理对象
        vid：地图容器节点的ID
        zooms： 地图显示的缩放级别范围，在PC上，默认范围[3,18]，取值范围[3-18]；在移动设备上，默认范围[3-19]，取值范围[3-19]
        center： 地图中心点坐标值
        plugin：地图使用的插件
        events： 事件
      -->
        <el-amap
          class="amap-box"
          :amap-manager="amapManager"
          :vid="'amap-vue'"
          :zoom="zoom"
          :plugin="plugin"
          :center="center"
          :events="mapEvents"
        >
          <!-- 标记 -->
          <el-amap-marker
            v-for="(item, index) in markers"
            :position="[item.lng,item.lat]"
            :key="index"
            :extData="item"
            :events="item.events"
            :content="item.icon"
          ></el-amap-marker>
          <el-amap-marker
            v-for="(item, index) in pushMark"
            :key="index"
            :position="[item.lng,item.lat]"
            :visible="item.visible"
            :content="item.icon"
            :draggable="item.draggable"
            :cursor="item.cursor"
            :offset="item.offset"
            :label="item.label"
          >
          </el-amap-marker>
          <el-amap-info-window
            v-for="(item, index) in markers"
            :key="index"
            :offset="item.offset"
             :position="[item.lng,item.lat]"
            :content="item.content"
            :visible="item.visible"
            :open="item.open"
            >
          </el-amap-info-window>
<!--            <el-amap-text-->
<!--              v-for="(item, index) in pushMark"-->
<!--              :key="index"-->
<!--              :offset="item.offset"-->
<!--              :position="[item.lng,item.lat]"-->
<!--              :text="item.content"-->
<!--              :visible="item.visible"-->
<!--            >-->
<!--          </el-amap-text>-->
          <!--道路-->
          <el-amap-polyline  v-for="(item,index) in polylineList"
            :editable="item.editable"
                            :path="item.path"
                             :key="index"
                             :strokeColor="item.strokeColor"
                            :events="item.events">

          </el-amap-polyline>
          <el-amap-polyline  v-for="(item,index) in getRegionListLat"
                             :editable="item.editable"
                             :path="item.path"
                             :key="index"
                             :strokeColor="item.strokeColor"
                             :events="item.events">

          </el-amap-polyline>
        </el-amap>
      </div>
    </div>

  </div>
</template>

<script>
  import { AMapManager, lazyAMapApiLoaderInstance } from 'vue-amap'
  let amapManager = new AMapManager()
  export default {
    data () {
      let self = this
      return {
        queryParams: {
          categoryName: '',
          categoryCode: '',
          regionCode: '',
          categoryCodeList: [],
          regionCodeList: [],
          positionName: ''
        },
        regionList: [],
        categoryList: [],
        mapData: {
          dialogVisible: true,
          address: '',
          lng: '',
          lat: ''
        },
        visible: true,
        address: null,
        searchKey: '',
        amapManager,
        markers: [],
        pushMark: [],
        searchOption: {
          city: '全国',
          citylimit: true
        },
        center: [120.723299, 31.323974],
        zoom: 11, // 地图缩放，zoom越小，看的内容越多
        lng: 0,
        lat: 0,
        loaded: false,
        events: {},
        // 一些工具插件
        plugin: [
          {
            pName: 'RangingTool',
            events: {
              init (instance) {
              }
            }
          },
          {
            // 工具栏
            pName: 'ToolBar',
            events: {
              init (instance) {
                // console.log("工具栏:"+instance);
              }
            }
          },
          // {
          //   // 鹰眼
          //   pName: 'OverView',
          //   events: {
          //     init (instance) {
          //       // console.log("鹰眼:"+instance);
          //     }
          //   }
          // },
          {
            // 地图类型
            pName: 'MapType',
            defaultType: 0,
            events: {
              init (instance) {
                // console.log("地图类型:"+instance);
              }
            }
          },
          {
            // 搜索
            pName: 'PlaceSearch',
            events: {
              init (instance) {
                // console.log("搜索:"+instance)
              }
            }
          },
          {
            // 搜索
            pName: 'Scale',
            events: {
              init (instance) {
                // console.log("搜索:"+instance)
              }
            }
          }
        ],
        polylineList: [],
        getRegionListLat: [],
        window: [],
        currentWindow: {
          lng: '',
          lag: ''
        },
        mapRuler: {},
        mapEvents: {
          init (map) {
            const ruler = new window.AMap.RangingTool(map)// 初始化插件
            self.mapRuler = ruler
            // map.event.addListerner(self.mapRuler, 'end', () => {
            //   console.log(2)
            //   alert(2323)
            // })
            // map.on('click', e => {
            //   console.log(e)
            //   var lnglatXY = [e.lnglat.getLng(), e.lnglat.getLat()]
            //   self.init(lnglatXY)
            // })
          }
          // click (e) {
          //   // self.markers = []
          //   let { lng, lat } = e.lnglat
          //   self.lng = lng
          //   self.lat = lat
          //   // self.center = [lng, lat]
          //   // self.mapData.lat = self.lat
          //   // self.mapData.lng = self.lng
          //   // self.markers.push([lng, lat])
          //   // 这里通过高德 SDK 完成。
          //   let geocoder = new AMap.Geocoder({
          //     radius: 1000,
          //     extensions: 'all'
          //   })
          //   geocoder.getAddress([lng, lat], function (status, result) {
          //     if (status === 'complete' && result.info === 'OK') {
          //       if (result && result.regeocode) {
          //         console.log(result)
          //         // self.address = result.regeocode.formattedAddress
          //         // self.mapData.address = result.regeocode.formattedAddress
          //         // self.$nextTick()
          //         let mark = {
          //           lng: lng,
          //           lat: lat,
          //           offset: [5, -32],
          //           content: '<div style="width:300px;padding:10px 10px 20px 10px;background: #ffffff">' +
          //             '<div><input type="text" id="inputLocation"><br/></div>' +
          //             result.regeocode.formattedAddress + '</div>' +
          //             '<div class="amap-info-sharp"></div>',
          //           visible: true
          //         }
          //         console.log(mark)
          //         self.pushMark.push(mark)
          //         console.log(self.pushMark)
          //         self.$nextTick()
          //       }
          //     }
          //   })
          // }
        },
        buttonVisible: true,
        buttonMark: true,
        k: 0,
        iconList: ['爱国主义教育基地', '宾馆饭店', '社区', '大型超市', '大学', '公共广场', '公共文化设施', '公园', '交通场站', '街道办事处', '社区新时代文明实践站', '实体书店', '小学', '新时代文明实践站', '医院', '政务大厅', '中学', '主要交通路口', '主要商业大街', '农贸（集贸）市场']
      }
    },
    created () {
    // activated () {
      this.queryPage()
      this.getRegionList()
      this.getCategoryList()
      this.getRegion()
      // this.mapRuler.turnOn()
    },
    methods: {
      queryPage () {
        // this.pageIndex = 1
        this.getDataList()
        this.getStreet()
        // console.log(this.getSixNum('123'))
      },
      getRegionList () {
        this.$http({
          url: this.$http.adornUrl('/position/area/top'),
          method: 'get'
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.regionList = data.obj
          } else {
            this.regionList = []
          }
        })
      },
      getRegionName (code) {
        if (code && this.regionList) {
          this.regionList.forEach(region => {
            if (region.code === code) {
              this.dataForm.regionName = region.name
            }
          })
        }
      },
      getCategoryList () {
        this.$http({
          url: this.$http.adornUrl('/position/category/list'),
          method: 'get',
          params: this.$http.adornParams({ categoryCode: this.$route.query.categoryCode })
        }).then(({ data }) => {
          this.categoryList = data.obj || []
          this.categoryList.forEach(e => {
            var iconName = '其它'
            if (this.iconList.indexOf(e.name) >= 0) {
              iconName = e.name
            }
            e.img = require('@/assets/img/icon/' + iconName + '.png')
          })
        })
      },
      initSearch () {
        let vm = this
        let map = this.amapManager.getMap()
        // AMapUI.loadUI(['misc/PoiPicker'], function (PoiPicker) {
        //   var poiPicker = new PoiPicker({
        //     input: 'search',
        //     placeSearchOptions: {
        //       map: map,
        //       pageSize: 10
        //     },
        //     suggestContainer: 'searchTip',
        //     searchResultsContainer: 'searchTip'
        //   })
        //   vm.poiPicker = poiPicker
        //   // 监听poi选中信息
        //   poiPicker.on('poiPicked', function (poiResult) {
        //     // console.log(poiResult)
        //     let source = poiResult.source
        //     let poi = poiResult.item
        //     if (source !== 'search') {
        //       poiPicker.searchByKeyword(poi.name)
        //     } else {
        //       poiPicker.clearSearchResults()
        //       vm.markers = []
        //       let lng = poi.location.lng
        //       let lat = poi.location.lat
        //       let address = poi.cityname + poi.adname + poi.name
        //       vm.center = [lng, lat]
        //       vm.markers.push([lng, lat])
        //       vm.lng = lng
        //       vm.lat = lat
        //       vm.address = address
        //       vm.searchKey = address
        //     }
        //   })
        // })
      },
      getDataList () {
        var that = this
        this.$http({
          url: this.$http.adornUrl('/position/position/listAll'),
          method: 'post',
          data: this.$http.adornData({
            // 'currentPage': -1,
            // 'pageSize': -1,
            'categoryCodeList': this.queryParams.categoryCodeList,
            'regionCodeList': this.queryParams.regionCodeList,
            'positionName': this.queryParams.positionName
          })
        }).then(({ data }) => {
          var i = 0
          if (data && data.code === 0) {
            this.markers = []
            data.obj.forEach(e => {
              var iconName = '其它'
              if (this.iconList.indexOf(e.categoryName) >= 0) {
                iconName = e.categoryName
              }
              if (e.positionMap) {
                let mark = {
                  index: i++,
                  lng: '',
                  lat: '',
                  icon: '<img src="' + require('@/assets/img/icon/' + iconName + '.png') + '" style="width:20px;height:26px;"></img>',
                  offset: [5, -32],
                  label: { content: e.positionName, offset: [-10, -42], direction: 'top', visible: false },
                  events: {
                    click (e) {
                      // console.log(e)
                      // that.window = e
                      that.markers[e.target.w.extData.index].visible = true
                      that.markers.forEach(k => {
                        if (k.index !== e.target.w.extData.index) {
                          k.visible = false
                        }
                      })
                      // console.log(e.target.getId())
                      // console.log(e.target.w.extData.index)
                      // console.log(e.target.getExtData().key)
                    }}
                }
                mark.lat = e.positionMap.split(',')[0]
                mark.lng = e.positionMap.split(',')[1]
                mark.content = e.positionName
                mark.visible = false
                mark.open = true
                this.markers.push(mark)
                // if (i === 0) {
                //   this.currentWindow.lat = mark.lat
                //   this.currentWindow.lng = mark.lng
                //   this.currentWindow.content = mark.positionName
                //   this.currentWindow.visible = false
                // }
              }
            })
          }
        })
      },
      // 打道路
      getStreet () {
        this.$http({
          url: this.$http.adornUrl('/position/street/list'),
          method: 'post',
          data: this.$http.adornData({
            'categoryCodeList': this.queryParams.categoryCodeList,
            'regionCodeList': this.queryParams.regionCodeList
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.polylineList = []
            data.obj.forEach(e => {
              var polyline = {
                path: [],
                events: {
                  click (e) {
                    // alert('click polyline')
                  },
                  end: (e) => {
                    let newPath = e.target.getPath().map(point => [point.lng, point.lat])
                    console.log(newPath)
                  }
                },
                editable: false,
                strokeColor: '#ff0000'
              }
              e.coordinate.split('/').forEach(k => {
                polyline.path.push([k.split(',')[0], k.split(',')[1]])
              })
              this.polylineList.push(polyline)
            })
            console.log('1111')
            console.log(this.polylineList)
          }
        })
      },
      getRegion () {
        this.$http({
          url: this.$http.adornUrl('/position/regional/boundary/list'),
          method: 'post',
          data: this.$http.adornData({
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.getRegionListLat = []
            data.obj.forEach(e => {
              var polyline = {
                path: [],
                events: {
                  click (e) {
                    // alert('click polyline')
                  },
                  end: (e) => {
                    let newPath = e.target.getPath().map(point => [point.lng, point.lat])
                    console.log(newPath)
                  }
                },
                editable: false,
                strokeColor: '#ff0000'
              }
              e.coordinate.split('/').forEach(k => {
                polyline.path.push([k.split(',')[0], k.split(',')[1]])
              })
              this.getRegionListLat.push(polyline)
            })
          }
          console.log(this.getRegionListLat)
        })
      },
      openRangingTool () {
        this.amapManager.getMap().off('click', this.clickMark)
        if (this.buttonVisible) {
          this.mapRuler.turnOn()
        } else {
          this.mapRuler.turnOff()
        }
        this.buttonVisible = !this.buttonVisible
      },
      openMark () {
        console.log(1)
        this.mapRuler.turnOff()
        if (this.buttonMark) {
          this.amapManager.getMap().on('click', this.clickMark)
        } else {
          this.amapManager.getMap().off('click', this.clickMark)
        }
        this.buttonMark = !this.buttonMark
      },
      clickMark (e) {
        console.log(e)
        if (e.overlay) {
          return
        }
        var self = this
        // self.markers = []
        let {lng, lat} = e.lnglat
        self.lng = lng
        self.lat = lat
        // 这里通过高德 SDK 完成。
        let geocoder = new AMap.Geocoder({
          radius: 1000,
          extensions: 'all'
        })
        geocoder.getAddress([lng, lat], function (status, result) {
          if (status === 'complete' && result.info === 'OK') {
            if (result && result.regeocode) {
              console.log(result)
              // var content = '<div style="width:300px;padding:20px 10px 10px 10px;background: #ffffff">' +
              //   '<a class="amap-info-close" href="javascript: void(0)" style="right: 5px;"  id="' + 'close' + self.k + '" >×</a>' +
              //   '<div style="margin-bottom: 10px"><input type="text" id="' + 'inputLocation' + self.k + '" style="width:100%"></div>' +
              //   result.regeocode.formattedAddress +
              //   '<div style="display:flex; justify-content: right"><div style="border:2px solid #f7f7f7;padding: 2px 2px" id="' + 'save' + self.k + '">保存</div></div>' +
              //   '</div>' +
              //   '<div class="amap-info-sharp"></div>'
              var content = '<div style="padding:20px 10px 10px 10px;background: #ffffff; width: 180px; border-radius: 0.25rem">' +
                '<a class="amap-info-close" href="javascript: void(0)" style="right: 5px;"  id="' + 'close' + self.k + '" ">×</a>' +
                '<div style="margin-bottom: 10px"><input type="text" class="el-input__inner" id="' + 'inputLocation' + self.k + '"  placeholder="请输入标记文字"></div>' +
                '<div style="display:flex; justify-content: right"><div style="color:#409EFF;padding: 2px 2px;cursor: pointer" id="' + 'save' + self.k + '">保存</div></div>' +
                '</div>'
              let mark = {
                index: self.k++,
                lng: lng,
                lat: lat,
                offset: [-13, -50],
                // 设置是否可以拖拽
                draggable: true,
                cursor: 'move',
                // content: content,
                icon: '<img src="' + require('@/assets/img/icon/mark.png') + '"style="height:40px;with:40px"></img>',
                visible: true,
                label: { content: content, offset: [40, 0], direction: 'right', visible: true }
              }
              // document.getElementById('close').addEventListener('click', function (e) {
              //   console.log(e)
              //   alert('hahah')
              // })
              console.log(mark)
              self.pushMark.push(mark)
              console.log(self.pushMark)
              // self.openMark()
              // self.$nextTick()
              setTimeout(k => {
                console.log(document.getElementById('close' + (self.k - 1)))
                document.getElementById('close' + (self.k - 1)).onclick = function (e) {
                  var tag = e.srcElement || e.target
                  var index = tag.id.substr(5)
                  self.pushMark[parseInt(index)].visible = false
                }
                document.getElementById('save' + (self.k - 1)).onclick = function (e) {
                  var tag = e.srcElement || e.target
                  var index = tag.id.substr(4)
                  var textValue = document.getElementById('inputLocation' + index).value
                  if (textValue.trim() === '') {
                    alert('请输入点位标记名称')
                  }
                  content = '<div style="padding:5px 20px 5px 5px;background: #ffffff; font-size: 13px">' +
                    '<a class="amap-info-close" href="javascript: void(0)" style="right: 5px;"  id="' + 'close' + index + '" >×</a>' +
                    textValue + '</div>'
                  self.pushMark[parseInt(index)].label = { content: content, offset: [40, 0], direction: 'right', visible: true }
                  setTimeout(k => {
                    document.getElementById('close' + index).onclick = function (e) {
                      var tag = e.srcElement || e.target
                      var index = tag.id.substr(5)
                      self.pushMark[parseInt(index)].visible = false
                    }
                  }, 300)
                }
                self.openMark()
              }, 800)
              // setTimeout(e => {
              //   document.getElementById('close' + (self.k - 1)).onclick = function () {
              //     self.pushMark[self.k - 1].visible = false
              //   }
              //   document.getElementById('save' + (self.k - 1)).onclick = function () {
              //     var textValue = document.getElementById('inputLocation' + (self.k - 1)).value
              //     var newContent = result.regeocode.formattedAddress
              //     if (textValue.trim() !== '') {
              //       newContent = textValue
              //     }
              //     self.pushMark[self.k - 1].content = '<div style="width:300px;padding:20px 10px 10px 10px;background: #ffffff">' +
              //         '<a class="amap-info-close" href="javascript: void(0)" style="right: 5px;"  id="' + 'close' + self.pushMark[self.k - 1].index + '" >×</a>' +
              //       newContent +
              //         '</div>' +
              //         '<div class="amap-info-sharp"></div>'
              //     setTimeout(k => {
              //       document.getElementById('close' + (self.pushMark[self.k - 1].index)).onclick = function () {
              //         self.pushMark[self.k - 1].visible = false
              //       }
              //     }, 50)
              //   }
              // }, 50)
            }
          }
        })
      }
    }
  }
</script>
<style scoped>
  .moren {
    background-color: white;
    color: black;
  }
  .select {
  }
  .container {
    width: 100%;
    height: 100%;
    position: relative;
    left: 50%;
    top: 50%;
    transform: translate3d(-50%, -50%, 0);
    border: 1px solid #999;
  /*.amap-info-content {*/
  /*    .amap-info-close {*/
  /*      display: none !important;*/
  /*    }*/
  /*}*/
  }
  .amap-info-close {
    display: none !important;
  }

  .el-select__tags {
    overflow: hidden !important;
    flex-wrap: nowrap !important;
  }
  /*input {*/
  /*  width: 200px !important;*/
  /*}*/
  .formfix {
    padding: 10px 10px;
   position: fixed;
    top: 20px;
    left: 5px;
    border-right: 10px 10px;
    border-right: 10px 10px;
    z-index: 1;
    background-color: #fff;
  }
  .el-form-item {
    margin-bottom: unset !important;
  }
</style>
