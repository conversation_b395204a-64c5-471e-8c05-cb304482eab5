<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="queryParams"
      @keyup.enter.native="queryPage()"
    >
      <el-form-item>
        <el-input
          v-model="queryParams.positionCode"
          placeholder="点位编码"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="queryParams.positionName"
          placeholder="点位名称"
          clearable
        ></el-input>
      </el-form-item>

      <!-- <el-form-item>
          <el-popover
            popper-class="popperBox"
            ref="categoryListPopover2"
            placement="bottom-start"
            trigger="click"
          >
            <el-tree
              :data="categoryList"
              :props="orgProps"
              node-key="id"
              ref="orgTree"
              @current-change="categoryTreeCurrentChangeHandle2"
              :default-expand-all="true"
              :highlight-current="true"
              :expand-on-click-node="false"
            >
            </el-tree>
          </el-popover>
          <el-input
            v-model="queryParams.categoryName"
            v-popover:categoryListPopover2
            clearable
            placeholder="选择分类"
          >
            <i
              slot="suffix"
              style="margin-right: 5px"
              class="el-icon-arrow-down"
            >
            </i>
          </el-input>
        </el-form-item> -->

      <el-form-item>
        <el-select
          @change="queryPage()"
          v-model="queryParams.regionCode"
          placeholder="所属区域"
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="dict in regionList"
            :key="dict.id"
            :label="dict.name"
            :value="dict.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          @change="queryPage"
          v-model="queryParams.state"
          placeholder="请选择点位状态"
          clearable
        >
          <el-option
            v-for="dict in statusList"
            :key="dict.id"
            :label="dict.label"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="queryParams.startTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="创建开始时间"
          @change="queryPage()"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="queryParams.endTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="创建结束时间"
          @change="queryPage()"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" @click="queryPage()">查询</el-button>
        <!-- <el-button
          v-if="isAuth('business:positionapplyinfo:save')"
          type="primary"
          @click="addOrUpdateHandle()"
          >新增</el-button
        > -->
        <el-button
          v-if="isAuth('business:positionapplyinfo:delete')"
          type="danger"
          @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0"
          >批量删除</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%"
    >
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50"
      >
      </el-table-column>
      <el-table-column
        prop="positionCode"
        header-align="center"
        align="center"
        label="点位编码"
      >
      </el-table-column>
      <el-table-column
        prop="positionName"
        header-align="center"
        align="center"
        label="点位名称"
      >
      </el-table-column>
      <el-table-column
        prop="categoryName"
        header-align="center"
        align="center"
        label="所属分类"
      >
      </el-table-column>
      <el-table-column
        prop="regionName"
        header-align="center"
        align="center"
        label="所属区域"
      >
      </el-table-column>
      <!-- <el-table-column
        prop="gridName"
        header-align="center"
        align="center"
        label="所属网格"
      >
      </el-table-column> -->
      <!-- <el-table-column
        prop="positionMap"
        header-align="center"
        align="center"
        label="地图定位获取到的地址（测绘的接口获取）">
      </el-table-column> -->
      <el-table-column
        prop="positionAddress"
        header-align="center"
        align="center"
        label="详细地址"
      >
      </el-table-column>
      <el-table-column
        prop="createDate"
        header-align="center"
        align="center"
        label="创建时间"
      >
      </el-table-column>
      <el-table-column
        prop="updateDate"
        header-align="center"
        align="center"
        label="更新时间"
      >
      </el-table-column>
      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        label="状态"
        :formatter="statusFormat"
      >
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="200"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            v-if="
              queryData.number === '1' &&
              scope.row.positionExamine.state === 256
            "
            type="text"
            size="small"
            @click="positionLower(scope.row)"
            >下架</el-button
          >
          <el-button
            v-if="
              queryData.number === '1' &&
              scope.row.positionExamine.state === 128
            "
            type="text"
            size="small"
            @click="positionUpper(scope.row)"
            >重新上架</el-button
          >
          <el-button
            v-if="
              queryData.number === '2' &&
              (scope.row.positionExamine.state === 2 ||
                scope.row.positionExamine.state === 16 ||
                scope.row.positionExamine.state === 32)
            "
            type="text"
            size="small"
            @click="handExamine(scope.row)"
            >审核</el-button
          >
          <el-button
            v-if="
              queryData.number === '3' &&
              (scope.row.positionExamine.state === 64 ||
                scope.row.positionExamine.state === 32)
            "
            type="text"
            size="small"
            @click="updateOrMove(scope.row)"
            >审核</el-button
          >
          <el-button type="text" size="small" @click="examineDetail(scope.row)"
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 30, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination>

    <!-- 审核通过，不通过页面 -->
    <el-dialog
      append-to-body
      :visible.sync="examineDialogVisible"
      width="30%"
      :close-on-click-modal="false"
    >
      <h2 align="center">审核操作</h2>
      <p align="center">确认审核结果</p>
      <div align="center" style="margin-top: 40px">
        <el-button type="primary" @click="examinePass()">审核通过</el-button>
        <el-button @click="examineRefuse()">审核不通过</el-button>
      </div>
    </el-dialog>

    <!-- 审核不通过，要输入原因的页面 -->
    <el-dialog
      title="审核操作"
      :visible.sync="examineRefuseDialogVisible"
      width="30%"
      center
      append-to-body
    >
      <div>
        <el-input
          type="textarea"
          :autosize="{ minRows: 4, maxRows: 8 }"
          v-model="examine.fbDesc"
          placeholder="请输入审核不通过原因"
        ></el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="refuseExamine()">确 定</el-button>
        <el-button @click="examineRefuseDialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>

    <!-- 保修或者上下架 -->
    <el-dialog
      append-to-body
      :title="title"
      :visible.sync="updateOrMoveDialogVisible"
      width="30%"
    >
      <div>
        <el-form ref="reasonForm" :model="reasonForm" label-width="80px">
          <el-form-item label="原因">
            <span>{{ reasonForm.reason }}</span>
          </el-form-item>
          <el-form-item label="图片">
            <img
              v-for="(url, index) in reasonForm.fileUrlList"
              :key="index"
              style="width: 30%; margin-left: 5px"
              :src="$http.adornUrl('/file' + url)"
            />
          </el-form-item>
          <el-form-item label="备注">
            <span>{{ reasonForm.remark }}</span>
          </el-form-item>
        </el-form>
      </div>
      <div align="center" style="margin-top: 40px">
        <el-button type="primary" @click="examinePass()">审核通过</el-button>
        <el-button @click="examineRefuse()">审核不通过</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    queryData: Object
  },
  data () {
    return {
      queryParams: {
        state: '',
        level: '',
        parentId: '',
        positionCode: '',
        positionName: '',
        categoryName: '',
        categoryCode: '',
        regionCode: '',
        startTime: '',
        endTime: '',
        stateList: ''
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 30,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      statusList: [
        { 'id': 1, 'label': '草稿' },
        { 'id': 2, 'label': '审核中' },
        { 'id': 4, 'label': '已撤回' },
        { 'id': 8, 'label': '审核不通过' },
        { 'id': 16, 'label': '更新中' },
        { 'id': 32, 'label': '报修审核中' },
        { 'id': 64, 'label': '下架审核中' },
        { 'id': 128, 'label': '已下架' },
        { 'id': 256, 'label': '正常' }
      ],
      rowStatus: '',
      detailVisible: false,
      visible: false,
      dataForm: {
        id: null,
        version: null,
        positionCode: '',
        positionName: '',
        categoryCode: '',
        categoryName: '',
        regionCode: '',
        regionName: '',
        gridCode: '',
        gridName: '',
        positionMap: '',
        positionAddress: '',
        qrCodeBinding: '',
        source: 1,
        parentId: '',
        level: '',
        applyUserId: '',
        applyUserName: '',
        orgIdLinked: '',
        orgName: '',
        fileUrlList: [],
        saveState: '2'
      },
      fileList: [],
      orgList: [],
      orgProps: {
        key: 'id',
        label: 'name',
        children: 'children'
      },
      categoryList: [],
      examineDialogVisible: false,
      examineRefuseDialogVisible: false,
      updateOrMoveDialogVisible: false,
      examine: {
        id: null,
        positionCode: null,
        state: null,
        fbDesc: null
      },
      title: '',
      // 保修下架原因
      reasonForm: {
        reason: '',
        remark: '',
        fileUrlList: []
      },
      url: '',
      regionList: []

    }
  },
  created () {
    this.queryPage()
    this.getRegionList()
  },
  methods: {
    queryPage () {
      this.pageIndex = 1
      this.getDataList()
    },
    // 获取数据列表
    getDataList () {
      if (this.queryData.isDeleted === 'Y') {
        this.url = '/position/position/getPages'
      } else {
        this.url = '/position/position/getPages'
      }
      if (this.queryParams.state) {
        this.queryParams.stateList = [this.queryParams.state]
      } else {
        this.queryParams.stateList = this.queryData.stateList
      }
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl(this.url),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'parentId': this.queryParams.parentId,
          'positionCode': this.queryParams.positionCode,
          'positionName': this.queryParams.positionName,
          'regionCode': this.queryParams.regionCode,
          'categoryName': this.categoryName,
          'categoryCode': this.categoryCode,
          'startTime': this.queryParams.startTime,
          'endTime': this.queryParams.endTime,
          'stateList': this.queryParams.stateList
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    getRegionList () {
      this.$http({
        url: this.$http.adornUrl('/position/area/top'),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.regionList = data.obj
        } else {
          this.regionList = []
        }
      })
    },
    handExamine (row) {
      this.examineDialogVisible = true
      this.examine.id = row.id
      this.examine.state = row.positionExamine.state
      this.examine.positionCode = row.positionCode
    },
    // 审核通过
    examinePass () {
      this.$http({
        url: this.$http.adornUrl('/position/position/examine/agree'),
        method: 'get',
        params: this.$http.adornParams({
          'id': this.examine.id
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.examineDialogVisible = false
          this.updateOrMoveDialogVisible = false
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 500,
            onClose: () => {
              this.getDataList()
            }
          })
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    updateOrMove (row) {
      this.$http({
        url: this.$http.adornUrl('/position/position/getReason'),
        method: 'get',
        params: {
          positionCode: row.positionCode,
          type: row.positionExamine.state + '' === '32' ? 0 : 1
        }
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.reasonForm = data.obj
        } else {
          this.reasonForm = []
        }
      })
      this.updateOrMoveDialogVisible = true
      this.examine.id = row.id
      this.examine.state = row.positionExamine.state
      this.examine.positionCode = row.positionCode
      this.title = '报修下架审核'
    },
    // 审核不通过
    examineRefuse () {
      this.examineRefuseDialogVisible = true
    },
    refuseExamine () {
      this.$http({
        url: this.$http.adornUrl('/position/position/examine/refuse'),
        method: 'post',
        data: this.examine
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.examineDialogVisible = false
          this.examineRefuseDialogVisible = false
          this.updateOrMoveDialogVisible = false
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 500,
            onClose: () => {
              this.getDataList()
            }
          })
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    // 点位下架
    positionLower (row) {
      this.$confirm(`确认是否对点位进行下架操作?`, '下架操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/position/position/upperOrLowerShelves'),
          method: 'get',
          params: { id: row.id, code: 1 }
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 点位重新上架
    positionUpper (row) {
      this.$confirm(`确认是否对点位进行重新上架操作?`, '重新上架操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/position/position/upperOrLowerShelves'),
          method: 'get',
          params: { id: row.id, code: '0' }
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    statusFormat (row, column) {
      if (row.positionExamine.state + '' === '1') return '草稿'
      if (row.positionExamine.state + '' === '2') return '审核中'
      if (row.positionExamine.state + '' === '4') return '已撤回'
      if (row.positionExamine.state + '' === '8') return '审核不通过'
      if (row.positionExamine.state + '' === '16') return '更新中'
      if (row.positionExamine.state + '' === '32') return '报修中'
      if (row.positionExamine.state + '' === '64') return '下架中'
      if (row.positionExamine.state + '' === '128') return '已下架'
      if (row.positionExamine.state + '' === '256') return '正常'
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle (val) {
      this.dataListSelections = val
    },
    // 删除
    deleteHandle (row) {
      var ids = row.id ? row.positionCode : this.dataListSelections.map(item => {
        return item.positionCode
      })
      this.$confirm(`确定进行[${row.positionName ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/position/position/delete'),
          method: 'get',
          params: this.$http.adornParams({
            'positionCode': ids
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    getDetail (id) {
      this.fileList = []
      this.dataForm.id = id || null
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/position/position/detail`),
            method: 'get',
            params: { id: this.dataForm.id }
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
              this.dataForm.fileUrlList.forEach(data => {
                const file = {
                  name: '',
                  url: window.SITE_CONFIG.baseUrl + '/file' + data
                }
                this.fileList.push(file)
              })
            }
          })
        }
      })
    },
    getOrgList () {
      this.$http({
        url: this.$http.adornUrl('/admin/org/tree/user'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        this.orgList = data.obj || []
      })
    },
    getCategoryList () {
      this.$http({
        url: this.$http.adornUrl('/position/category/tree'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        this.categoryList = data.obj || []
      })
    },
    // 区域树选中
    orgTreeCurrentChangeHandle (data, node) {
      this.dataForm.orgIdLinked = data.code
      this.dataForm.orgName = data.name
      this.$refs[`orgListPopover`].doClose()
    },
    // 区域树设置当前选中节点
    orgListTreeSetCurrentNode () {
      let key = this.dataForm.orgIdLinked
      if (key) {
        this.$refs.orgTree.setCurrentKey(key)
        this.dataForm.orgName = (this.$refs.orgTree.getCurrentNode() || {})['name']
      } else {
        this.$refs.orgTree.setCurrentKey([])
        this.dataForm.orgName = ''
      }
    },
    // 区域树选中
    categoryTreeCurrentChangeHandle (data, node) {
      this.dataForm.categoryCode = data.code
      this.dataForm.categoryName = data.name
      this.$refs[`categoryListPopover`].doClose()
    },
    // 区域树设置当前选中节点
    categoryTreeSetCurrentNode () {
      let key = this.dataForm.categoryCode
      if (key) {
        this.$refs.orgTree.setCurrentKey(key)
        this.dataForm.categoryName = (this.$refs.orgTree.getCurrentNode() || {})['name']
      } else {
        this.$refs.orgTree.setCurrentKey([])
        this.dataForm.categoryName = ''
      }
    },

    handleClose (done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done()
        })
        .catch(_ => { })
    },
    examineDetail (row) {
      this.$router.push({
        path: '/examineDetail',
        query: {
          id: row.id,
          code: row.positionCode,
          examineId: row.id,
          state: row.positionExamine.state
        }
      })
    }
  },
  watch: {
    instanceData: function (val) {
      this.formData = val
    }
  }
}
</script>
