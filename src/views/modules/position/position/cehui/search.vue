<!-- -->
<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm">
      <el-form-item label="经纬度">
        <el-input
          v-model="dataForm.location"
          placeholder="请输入经纬度坐标 示例：120.665973,31.296024"
          style="width: 500px"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" @click="search()">查询</el-button>
      </el-form-item>
      <el-button  icon="el-icon-upload2" type="success" @click="importData()"
      >导入</el-button
      >
    </el-form>

    <el-table
      border
      :data="array"
      :show-header="false"
      v-show="dataListLoading"
      style="width: 100%"
    >
      <el-table-column
        prop="label" width = "250px">
      </el-table-column>
      <el-table-column
        prop="value"
        style="width: 90%">
      </el-table-column>
    </el-table>

  </div>
</template>

<script>
export default {
  data () {
    return {
      dataList: [],
      dataListLoading: false,
      columns: [],
      array: [
        {key: 'regionCode', label: '街道编码', value: ''},
        {key: 'regionName', label: '街道名称', value: ''},
        {key: 'communityCode', label: '社区编码', value: ''},
        {key: 'communityName', label: '社区名称', value: ''},
        {key: 'gridCode', label: '网格编码', value: ''},
        {key: 'gridName', label: '网格名称', value: ''},
        {key: 'coordinate84', label: '84坐标系', value: ''},
        {key: 'coordinate2000', label: '苏州2000坐标系', value: ''},
        {key: 'address', label: '地址', value: ''}],
      dataForm: {
        location: '120.665973,31.296024',
        regionCode: '',
        regionName: '',
        communityCode: '',
        communityName: '',
        gridCode: '',
        gridName: '',
        coordinate84: '',
        coordinate2000: '',
        address: ''
      }
    }
  },
  methods: {
    search () {
      const lng = this.dataForm.location.split(',')[0]
      const lat = this.dataForm.location.split(',')[1]
      if (lng.trim() === '' || lat.trim() === '') {
        this.$message.error('筛选参数有误')
      } else {
        this.getCeHuiLocation(lng, lat)
      }
    },
    getCeHuiLocation (lng, lat) {
      this.$http({
        url: this.$http.adornUrl('/position/area/obtain'),
        method: 'get',
        params: this.$http.adornParams({
          'longitude': lng,
          'latitude': lat
        })
      }).then(({ data }) => {
        this.dataList = []
        if (data && data.code === 0) {
          this.dataListLoading = true
          this.array.forEach(e => {
            e.value = data.obj[e.key]
          })
        } else {
          this.dataListLoading = false
          this.$message.error(data.msg)
        }
      })
    },
    importData () {
      this.$router.push({
        path: '/evaluateImport',
        query: {
          parentId: this.$route.query.parentId,
          type: 6
        }
      })
    }
  }
}
</script>

