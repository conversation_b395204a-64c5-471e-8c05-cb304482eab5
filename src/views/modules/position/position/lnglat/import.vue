<template>
  <div v-loading="loading" element-loading-text="等待生成点位经纬度数据……"
       element-loading-spinner="el-icon-loading">
    <div class="module">
      <div></div>
      <div class="module_content">
        <p>第一步：下载excel导入模板，按格式批量填写数据，点我下载</p>
        <p>根据提示信息完善表格内容</p>
        <el-button @click="openFile()">下载模板表格</el-button>
      </div>
    </div>
    <div class="import">
      <div class="import_content">
        <p>第二步：上传完善后的表格</p>
        <el-upload
          class="upload-demo"
          drag
          :auto-upload="false"
          :action="action"
          :headers="headers"
          :data="params"
          :file-list="fileList"
          :limit="1"
          ref="upload"
          :on-change="changeHandle"
          :http-request="uploadFile"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <el-progress style="width: 100%;margin-top: 8px"  :percentage="progressPercent" />
        </el-upload>
        <!-- //进度条 -->
      </div>
    </div>
    <div>
      <el-button
        type="primary"
        style="margin-left: 10px; margin-top: 20px"
        @click="submitUpload"
      >
        导入
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      action: '',
      headers: {
        'Authorization': ''
      },
      fileList: [],
      params: {},
      progressPercent: 0,
      loading: false
    }
  },
  activated () {
    this.getAction()
    this.headers.Authorization = this.getCookigetCookie('Authorization').replace('bearer%20', 'Bearer ')
  },
  methods: {
    getAction () {
      this.action = window.SITE_CONFIG.baseUrl + '/position/lngLat/import'
    },
    getCookigetCookie (param) {
      var cParam = ''
      if (document.cookie.length > 0) {
        var arr = document.cookie.split('; ') // 这里显示的格式需要切割一下自己可输出看下
        for (var i = 0; i < arr.length; i++) {
          var arr2 = arr[i].split('=') // 再次切割
          // 判断查找相对应的值
          if (arr2[0] === param) {
            cParam = arr2[1]
            // 保存到保存数据的地方
          }
        }
        return cParam
      }
    },
    // 手动上传按钮
    submitUpload () {
      this.progressPercent = 0
      if (this.fileList.length === 0) {
        this.$message.error('请选择excel文件')
      } else {
        this.$refs.upload.submit()
      }
    },
    changeHandle (file, fileList) {
      this.progressPercent = 0
      let FileExt = file.name.replace(/.+\./, '')
      const isLt10M = file.size / 1024 / 1024 < 50
      if (['xlsx', 'xls'].indexOf(FileExt.toLowerCase()) === -1) {
        this.$message({
          type: '上传失败',
          message: '请上传后缀名为xlsx或xls的文件！'
        })
        this.fileList = []
        return
      }
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过 50M!')
        this.fileList = []
        return
      }
      this.fileList = fileList
    },
    // 表格下载
    openFile () {
      window.open('https://wmyq.sipac.gov.cn/template/经纬度批量获取导入模板.2022.08.16.xlsx')
    },
    uploadFile (params) {
      let file = params.file
      let FileExt = file.name.replace(/.+\./, '')
      if (['xlsx', 'xls'].indexOf(FileExt.toLowerCase()) === -1) {
        this.$message({
          type: '上传失败',
          message: '请上传后缀名为xlsx, xls的附件！'
        })
        return false
      }
      const isLt10M = file.size / 1024 / 1024 < 50
      if (['xlsx', 'xls'].indexOf(FileExt.toLowerCase()) !== -1 && !isLt10M) {
        this.$message.error('附件大小不能超过 50M!')
        return false
      }
      this.loading = true
      this.$http({
        timeout: 600 * 1000,
        url: this.action,
        method: 'post',
        data: this.$http.adornData({
          'file': file
        }, true, 'file'),
        responseType: 'arraybuffer',
        onUploadProgress: event => {
          this.progressPercent = Math.floor((event.loaded * 100) / event.total)
        }
      }).then(({data}) => {
        var enc = new TextDecoder('utf-8')
        var str = enc.decode(new Uint8Array(data))
        if (str.substr(0, 1) === '{') {
          var jsonData = JSON.parse(str)
          this.$message.error(jsonData.msg)
        } else {
          let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
          let objectUrl = URL.createObjectURL(blob)
          let a = document.createElement('a')
          a.href = objectUrl
          a.download = '点位经纬度数据.xlsx'
          a.click()
          URL.revokeObjectURL(objectUrl)
          this.$message({type: 'success', message: '下载成功'})
        }
        this.loading = false
        this.$refs.upload.clearFiles()
      })
    }

  }

}
</script>

<style>
.module {
  width: 100%;
  height: 200px;
  background-color: rgb(242, 242, 242);
}
.module_content {
  margin-top: 50px;
  margin-left: 50px;
  display: inline-block;
}

.import {
  width: 100%;
  background-color: rgb(242, 242, 242);
  margin-top: 15px;
}
.import_content {
  margin-top: 20px;
  margin-left: 50px;
  margin-bottom: 15px;
  display: inline-block;
}
.import_content_border {
  width: 400px;
  border: 2px dashed lightblue;
  display: inline-block;
}
</style>
