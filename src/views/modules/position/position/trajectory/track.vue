<template>
  <div>
    <!-- 搜索栏 -->
    <el-form
      :inline="true"
      :model="queryParams"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="getMakers()"
    >
      <el-form-item label="用户"
                    prop="userId"
                    :error="userError"
      >
        <el-select
          placeholder="选择指定用户"
          v-model="queryParams.userId"
          clearable
        >
          <el-option
            v-for="user in userList"
            :key="user.id"
            :label="user.name"
            :value="user.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="开始时间">
        <el-date-picker
          v-model="queryParams.startTime"
          type="datetime"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          placeholder="开始时间"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item label="结束时间">
        <el-date-picker
          v-model="queryParams.endTime"
          type="datetime"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          placeholder="开始时间"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button icon="el-icon-search" @click="getMakers()">查询</el-button>
      </el-form-item>
      <el-link type="info" disabled style="margin-bottom: -20px">更新数据频率 10秒</el-link>
    </el-form>

    <!-- 点位标记 -->
    <div class="container" style="width: 100%; height: 76vh">
      <el-amap
        class="amap-box"
        :amap-manager="amapManager"
        :vid="'amap-vue'"
        :zoom="zoom"
        :plugin="plugin"
        :center="center"
        :events="mapEvents"
      >
        <!-- 标点 -->
        <el-amap-marker
          v-for="(item, index) in markers"
          :position="[item.longitude, item.latitude]"
          :label="item.label"
          :key="index"
          :extData="item"
          :events="markerEvents"
        />
        <!-- 标点连线 -->
        <el-amap-polyline  visible="true"
                           editable="false"
                           :path="markerLine"
                           strokeOpacity=0.5
                           strokeWeight=1
                           strokeStyle="solid"
                           strokeColor="#ff0000"
                           lineJoin="round"
        />
        <!-- 贝塞尔曲线 -->
<!--        <el-amap-bezier-curve-->
<!--                           :path="markerCurve"-->
<!--                           strokeStyle="dashed"-->
<!--                           strokeColor="#ff0000"-->
<!--        />-->
      </el-amap>
    </div>
  </div>
</template>

<script>
import { AMapManager } from 'vue-amap'
let amapManager = new AMapManager()
export default {
  data () {
    let self = this
    return {
      amapManager,
      userList: null,
      queryParams: {
        userId: null,
        startTime: null,
        endTime: null
      },
      dataRule: {
        userId: [
          { required: true, message: '未指定用户', trigger: 'blur' }
        ]
      },
      userError: null,
      markers: [],
      markerLine: [],
      markerCurve: [],
      zoom: 12,
      center: [120.677934, 31.316626],
      // 一些工具插件
      plugin: [
        {
          pName: 'RangingTool',
          events: {
            init (instance) {
            }
          }
        },
        {
          // 工具栏
          pName: 'ToolBar',
          events: {
            init (instance) {
            }
          }
        },
        {
          // 地图类型
          pName: 'MapType',
          defaultType: 0,
          events: {
            init (instance) {
            }
          }
        },
        {
          // 搜索
          pName: 'PlaceSearch',
          events: {
            init (instance) {
            }
          }
        },
        {
          // 比例尺
          pName: 'Scale',
          events: {
            init (instance) {
            }
          }
        }
      ],
      mapEvents: {
        // init (map) {
        //   const ruler = new window.AMap.RangingTool(map)// 初始化插件
        //   self.mapRuler = ruler
        // },
        click () {
          self.markers.forEach(e => {
            e.label = {}
          })
        }
      },
      markerEvents: {
        click (e) {
          self.markers.forEach(e => {
            e.label = {}
          })
          self.center = [
            self.markers[e.target.w.extData.index].longitude,
            self.markers[e.target.w.extData.index].latitude
          ]
          self.zoom = 20
          self.markers[e.target.w.extData.index].label = {
            content: self.markers[e.target.w.extData.index].tip,
            offset: [18, -75],
            direction: 'right'
          }
        }
      }
    }
  },
  mounted () {
    this.getUsers()
  },
  methods: {
    getUsers () {
      this.$http({
        url: this.$http.adornUrl('/admin/user/getUsersByRoleCode'),
        method: 'get',
        params: this.$http.adornParams({ roleCode: 'RealtimeTrackingOfPM' })
      }).then(({ data }) => {
        if (data.code === 0) {
          this.userList = data.obj
        }
      })
    },
    getMakers () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl('/position/user/trail/pages'),
            method: 'post',
            data: this.$http.adornData({
              'currentPage': -1,
              'pageSize': -1,
              'userId': this.queryParams.userId,
              'startTime': this.queryParams.startTime,
              'endTime': this.queryParams.endTime,
              'orders': [{column: 'reportTime', sort: 'asc'}]
            })
          }).then(({ data }) => {
            if (data.code === 0) {
              let records = data.obj.records
              if (records) {
                this.markerLine = []
                let markerArr = []
                records.forEach((re, index) => {
                  this.markerLine.push([re.longitude, re.latitude])
                  let marker = {
                    index: index,
                    longitude: re.longitude,
                    latitude: re.latitude,
                    tip: '<div class=\'info\'>' +
                      '<span>经纬度</span><br/>' +
                      '<span>' + re.longitude + ',' + re.latitude + '</span><br/><br/>' +
                      '<span>报告时间</span><br/>' +
                      '<span>' +
                      re.reportTime.split('T')[0] + ' ' + re.reportTime.split('T')[1] +
                      '</span><br/>' +
                      '</div>',
                    label: {}
                  }
                  markerArr.push(marker)
                })
                this.markers = markerArr
              }
            }
          })
        }
      })
    }
  }
}
</script>

<style scoped>
.amap-marker-label {
  border: 0;
  background-color: #aacefa;
}
.info {/*正常标签样式*/
  position: relative;
  top: 0;
  right: 0;
  min-width: 0;
  z-index: 999;
}
</style>
