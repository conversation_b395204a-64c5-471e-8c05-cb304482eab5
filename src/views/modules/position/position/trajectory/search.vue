<template>
  <div>
    <el-form
      :inline="true"
      :model="queryParams"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="queryPage()"
    >
      <el-form-item label="用户"
                    prop="userId"
                    :error="userError"
      >
        <el-select
          placeholder="选择指定用户"
          v-model="queryParams.userId"
          clearable
        >
          <el-option
            v-for="user in userList"
            :key="user.id"
            :label="user.name"
            :value="user.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="开始时间">
        <el-date-picker
          v-model="queryParams.startTime"
          type="datetime"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          placeholder="开始时间"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item label="结束时间">
        <el-date-picker
          v-model="queryParams.endTime"
          type="datetime"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          placeholder="开始时间"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button icon="el-icon-search" @click="queryPage()">查询</el-button>
      </el-form-item>
      <el-link type="info" disabled style="margin-bottom: -20px">更新数据频率 10秒</el-link>
    </el-form>

    <div id="test-map" style="width: 100%; height: 76vh"/>

  </div>
</template>

<script>
import { lazyAMapApiLoaderInstance } from 'vue-amap'
export default {
  data () {
    return {
      userList: null,
      queryParams: {
        userId: null,
        startTime: null,
        endTime: null
      },
      dataRule: {
        userId: [
          { required: true, message: '未指定用户', trigger: 'blur' }
        ]
      },
      userError: null,
      dataList: [],
      // 地图实例
      map: '',
      // 地图范围
      zoom: 10,
      // 地图中心
      center: [120.677934, 31.316626],
      // 轨迹数组，格式[{},{},...]
      path: [
        {
          path: [
            // [120.666562, 31.295838],
            // [120.670896, 31.318793],
            // [120.739939, 31.263829],
            // [120.63744, 31.278848]
          ]
        }
      ],
      // 巡航器是否循环展示
      loop: true,
      // 巡航器行动速度 速度(km/h)
      speed: 1000,
      markerList: []
    }
  },
  mounted () {
    this.getUsers()
    this.createFootPrint()
  },
  methods: {
    getUsers () {
      this.$http({
        url: this.$http.adornUrl('/admin/user/getUsersByRoleCode'),
        method: 'get',
        params: this.$http.adornParams({ roleCode: 'RealtimeTrackingOfPM' })
      }).then(({ data }) => {
        if (data.code === 0) {
          this.userList = data.obj
        }
      })
    },
    queryPage () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl('/position/user/trail/pages'),
            method: 'post',
            data: this.$http.adornData({
              'currentPage': -1,
              'pageSize': -1,
              'userId': this.queryParams.userId,
              'startTime': this.queryParams.startTime,
              'endTime': this.queryParams.endTime,
              'orders': [{column: 'reportTime', sort: 'asc'}]
            })
          }).then(({ data }) => {
            if (data.code === 0) {
              this.path[0].path = []
              let records = data.obj.records
              if (records.length > 0) {
                this.dataList = records
                let path = []
                records.forEach(re => {
                  let pa = []
                  pa.push(re.longitude)
                  pa.push(re.latitude)
                  path.push(pa)
                  const tips = '<div>' +
                    '<span>经纬度</span><br/>' +
                    '<span>' + pa + '</span><br/><br/>' +
                    '<span>报告时间</span><br/>' +
                    '<span>' +
                    re.reportTime.split('T')[0] + ' ' + re.reportTime.split('T')[1] +
                    '</span><br/>' +
                    '</div>'
                  this.markerList.push(this.signPoint(pa, tips))
                })
                this.path[0].path = path
              }
            }
          })
          this.createFootPrint()
          this.signPoint()
        }
      })
    },
    createFootPrint () {
      lazyAMapApiLoaderInstance.load().then(() => {
        this.map = new AMap.Map('test-map', {
          zoom: this.zoom, // 地图缩放范围
          center: new AMap.LngLat(this.center[0], this.center[1])
        })
        AMapUI.load(['ui/misc/PathSimplifier'], (PathSimplifier) => {
          if (!PathSimplifier.supportCanvas) {
            alert('当前环境不支持 Canvas！')
            return
          }
          if (this.path[0].path.length > 0) {
            // 创建组件实例
            const pathSimplifierIns = new PathSimplifier({
              map: this.map,
              zIndex: 100, // 图层叠加顺序
              data: this.path, // 巡航路径
              // 获取巡航路径中的路径坐标数组
              getPath: (pathData, pathIndex) => {
                return pathData.path
              },
              getHoverTitle: (pathData, pathIndex, pointIndex) => {
                if (pointIndex >= 0) {
                  return '<div>' +
                    '<span>当前点位数</span><br/>' +
                    '<span>' + (pointIndex + 1) + '</span><br/><br/>' +
                    '<span>经纬度</span><br/>' +
                    '<span>' + pathData.path[0] + '</span><br/><br/>' +
                    '<span>报告时间</span><br/>' +
                    '<span>' +
                    this.dataList[pointIndex].reportTime.split('T')[0] + ' ' + this.dataList[pointIndex].reportTime.split('T')[1] +
                    '</span><br/>' +
                    '</div>'
                }
              }
            })
            this.map.add(this.markerList)
            // 创建巡航器
            const pathNavigator = pathSimplifierIns.createPathNavigator(0, {
              loop: this.loop, // 是否循环
              speed: this.speed // 速度(km/h)
            })
            pathNavigator.start()
          }
        })
      })
    },
    // 标记点位
    signPoint (item, tips) {
      // 创建一个 Icon
      var icon = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(100, 100),
        // 图标的取图地址
        image: '//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-red.png'
        // 图标所用图片大小
        // imageSize: new AMap.Size(135, 100)
        // 图标取图偏移量
        // imageOffset: new AMap.Pixel(-25, 20)
      })
      let marker = new AMap.Marker({
        // icon: '//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-red.png',
        position: item,
        icon: icon,
        offset: new AMap.Pixel(-25, -60)
      })
      return marker
    }
  }
}
</script>

<style scoped>
#test-map{
  height: 650px;
}
</style>
