<template>
  <div>
    <div>
      <commonList v-bind:queryData="queryData" v-if="flag"></commonList>
    </div>
  </div>
</template>

<script>
import commonList from '../commonList.vue'
export default {
  components: {
    commonList
  },
  data () {
    return {
      queryData: {
        number: '2',
        name: '点位审核',
        stateList: [2, 8, 16, 256]
      },
      flag: false
    }
  },
  created () {
    this.flag = true
  }

}
</script>

<style>
</style>