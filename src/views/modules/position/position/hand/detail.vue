<template>
  <div>
    <div>
      <p align="left" style="font-size: 20px">点位基本信息</p>
      <el-divider></el-divider>
      <br />
      <div>
        <div class="text_size">
          <span style="color: rgb(127, 127, 127)">点位编码</span>
          <br />
          <p style="margin-right: 40px">{{ dataForm.positionCode }}</p>
        </div>
        <div class="text_size">
          <span style="color: rgb(127, 127, 127)">点位名称</span>
          <br />
          <p style="margin-right: 40px">{{ dataForm.positionName }}</p>
        </div>
        <div class="text_size">
          <span style="color: rgb(127, 127, 127)">点位分类</span>
          <br />
          <p style="margin-right: 40px">{{ dataForm.categoryName }}</p>
        </div>
        <div class="text_size">
          <span style="color: rgb(127, 127, 127)">所属街道</span>
          <br />
          <p style="margin-right: 40px">{{ dataForm.regionName }}</p>
        </div>
        <div class="text_size">
          <span style="color: rgb(127, 127, 127)">所属社区</span>
          <br />
          <p style="margin-right: 40px">{{ dataForm.communityName }}</p>
        </div>
        <div class="text_size">
          <span style="color: rgb(127, 127, 127)">所属网格</span>
          <br />
          <p style="margin-right: 40px">{{ dataForm.gridName }}</p>
        </div>
        <div class="text_size">
          <span style="color: rgb(127, 127, 127)">点位经纬度</span>
          <br />
          <p v-if="dataForm.positionMap" style="margin-right: 40px">
            {{ dataForm.positionMap
            }}<i
              @click="openMap"
              style="color: blue"
              class="el-icon-location-outline"
            ></i>
          </p>
        </div>
        <div class="text_size">
          <span style="color: rgb(127, 127, 127)">84坐标</span>
          <br />
          <p style="margin-right: 40px">{{ dataForm.coordinate84 }}</p>
        </div>
        <div class="text_size">
          <span style="color: rgb(127, 127, 127)">苏州2000坐标</span>
          <br />
          <p style="margin-right: 40px">{{ dataForm.coordinate2000 }}</p>
        </div>
        <div class="text_size">
          <span style="color: rgb(127, 127, 127)">详细地址</span>
          <br />
          <p style="margin-right: 40px">{{ dataForm.positionAddress }}
            <i
              @click="openMap"
              style="color: blue"
              class="el-icon-location-outline"
            ></i>
          </p>
        </div>
        <div class="text_size">
          <span style="color: rgb(127, 127, 127)">创建人</span>
          <br />
          <p style="margin-right: 40px">{{ dataForm.applyUserName }}</p>
        </div>
        <div class="text_size">
          <span style="color: rgb(127, 127, 127)">创建时间</span>
          <br />
          <p style="margin-right: 40px">{{ dataForm.createDate }}</p>
        </div>
        <div class="text_size">
          <span style="color: rgb(127, 127, 127)">绑定二维码</span>
          <br />
          <p style="margin-right: 40px">
            {{ dataForm.bound ? "已绑定" : "未绑定" }}
          </p>
        </div>
        <div class="text_size">
          <span style="color: rgb(127, 127, 127)">状态</span>
          <br />
          <p style="margin-right: 40px">{{ dataForm.stateName }}</p>
        </div>
        <div style="width: 100%; clear: both"></div>
        <div>
          <span style="color: rgb(127, 127, 127)">图片</span>
          <br />
          <div>
            <el-row :gutter="20">
              <el-col
                :span="4"
                v-for="(item, index) in dataForm.fileUrlList"
                :key="index"
              >
                <img
                  @click="openImage(item)"
                  class="image"
                  :src="$http.adornUrl('/file' + item)"
                  style="width: 95%; margin-top: 10px"
                />
              </el-col>
              <!-- 图片展示 -->
              <el-dialog
                append-to-body
                :visible.sync="dialogImageDialogVisible"
              >
                <img
                  width="100%"
                  :src="$http.adornUrl('/file' + dialogImageUrl)"
                  alt=""
                />
              </el-dialog>
            </el-row>
          </div>
        </div>
        <div style="width: 100%; clear: both"></div>
      </div>
    </div>
    <el-divider></el-divider>
    <div class="line"></div>

    <template>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="操作记录" name="first">
          <el-table :data="logList" border style="width: 100%">
            <el-table-column align="center" prop="operateDes" label="操作" />
            <el-table-column align="center" prop="operator" label="操作人" />
            <el-table-column align="center" prop="createDate" label="时间" />
            <el-table-column align="center" prop="remark" label="操作说明" />
          </el-table>
        </el-tab-pane>
        <el-tab-pane
          label="点位随手拍问题记录"
          name="second"
          v-if="this.dataForm.level === 1"
        >
          <el-table
            :data="questionDataList"
            border
            v-loading="dataListLoading"
            style="width: 100%"
          >
            <el-table-column
              prop="questionCode"
              header-align="center"
              align="center"
              label="问题编号"
            >
            </el-table-column>
            <el-table-column
              prop="title"
              header-align="center"
              align="center"
              label="问题标题"
            >
            </el-table-column>
            <el-table-column
              prop="regionName"
              header-align="center"
              align="center"
              label="问题区域"
            >
            </el-table-column>
            <el-table-column
              prop="address"
              header-align="center"
              align="center"
              label="定位地点"
            >
            </el-table-column>
            <el-table-column
              prop="questionType.name"
              header-align="center"
              align="center"
              label="问题类别"
            >
            </el-table-column>
            <el-table-column
              prop="relation.relationName"
              header-align="center"
              align="center"
              label="责任局办"
            >
            </el-table-column>
            <el-table-column
              prop="subUserName"
              header-align="center"
              align="center"
              label="提交人姓名"
            >
            </el-table-column>
            <el-table-column
              prop="subPhone"
              header-align="center"
              align="center"
              label="提交人电话"
            >
            </el-table-column>
            <el-table-column
              prop="myself"
              header-align="center"
              align="center"
              label="是否自行解决"
            >
              <template slot-scope="scope">
                <span v-if="scope.row.myself === 1">是</span>
                <span v-else>否</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="createDate"
              header-align="center"
              align="center"
              label="提交日期"
            >
            </el-table-column>
            <el-table-column
              prop="questionExamine"
              header-align="center"
              align="center"
              label="问题状态"
            >
              <template slot-scope="scope">
                <el-tag
                  v-if="scope.row.questionExamine.state == 1"
                  size="small"
                  type="warning"
                  >待审核</el-tag
                >
                <el-tag v-if="scope.row.questionExamine.state == 2" size="small"
                  >已撤回</el-tag
                >
                <el-tag
                  v-if="scope.row.questionExamine.state == 4"
                  size="small"
                  type="danger"
                  >已退回</el-tag
                >
                <el-tag
                  v-if="scope.row.questionExamine.state == 8"
                  size="small"
                  type="primary"
                  >待回复</el-tag
                >
                <el-tag
                  v-if="scope.row.questionExamine.state == 16"
                  size="small"
                  type="info"
                  >已回复</el-tag
                >
                <el-tag
                  v-if="scope.row.questionExamine.state == 32"
                  size="small"
                  type="success"
                  >已完成</el-tag
                >
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            @size-change="questionSizeChangeHandle"
            @current-change="questionCurrentChangeHandle"
            :current-page="pageIndex"
            :page-sizes="[10, 20, 30, 50, 100]"
            :page-size="pageSize"
            :total="totalPage"
            layout="total, sizes, prev, pager, next, jumper"
          >
          </el-pagination>
        </el-tab-pane>
        <el-tab-pane
          label="点位测评记录"
          name="third"
          v-if="this.dataForm.level === 1"
        >
          <el-table :data="evaluateDataList" border>
            <el-table-column
              prop="instanceCode"
              label="测评单号"
              align="center"
            >
              <template slot-scope="scope">
                <!-- <span style="color: #409eff; cursor: pointer"> -->
                {{ scope.row.instanceCode }}
                <!-- </span> -->
              </template>
            </el-table-column>
            <el-table-column prop="instanceName" label="名称" align="center">
              <template slot-scope="scope">
                <!-- <span style="color: #409eff; cursor: pointer"> -->
                {{ scope.row.instanceName }}
                <!-- </span> -->
              </template>
            </el-table-column>
            <el-table-column prop="regionName" label="测评区域" align="center">
              <template slot-scope="scope">
                <!-- <span style="color: #409eff; cursor: pointer"> -->
                {{ scope.row.regionName }}
                <!-- </span> -->
              </template>
            </el-table-column>
            <!-- <el-table-column align="center" prop="areaName" label="测评社区" /> -->
            <el-table-column align="center" prop="typeName" label="测评类型" />
            <el-table-column
              align="center"
              prop="categoryName"
              label="测评子类型"
            />
            <el-table-column
              align="center"
              prop="evaluatorName"
              label="测评员"
            />
            <el-table-column
              align="center"
              prop="evaluatorDate"
              label="测评时间"
            />
            <el-table-column
              align="center"
              prop="cardName"
              label="测评卡版本"
              show-overflow-tooltip
            />
          </el-table>
          <el-pagination
            @size-change="sizeChangeHandle"
            @current-change="currentChangeHandle"
            :current-page="pageIndex"
            :page-sizes="[10, 20, 30, 50, 100]"
            :page-size="pageSize"
            :total="totalPage"
            layout="total, sizes, prev, pager, next, jumper"
          >
          </el-pagination>
        </el-tab-pane>
      </el-tabs>
    </template>

    <!-- 引入地图 -->
    <mapDiaglog
      v-if="mapDataVisible"
      ref="mapDiaglog"
      @change="getMapData($event)"
    ></mapDiaglog>
  </div>
</template>

<script>
import mapDiaglog from '../componet/mapDiaglogDetails.vue'
export default {
  components: {
    mapDiaglog
  },
  data () {
    return {
      visible: false,
      dataForm: {
        id: null,
        version: null,
        positionCode: '',
        positionName: '',
        categoryCode: '',
        categoryName: '',
        regionCode: '',
        regionName: '',
        gridCode: '',
        gridName: '',
        positionMap: '',
        positionAddress: '',
        qrCodeBinding: '',
        source: 1,
        parentId: '',
        level: '',
        applyUserId: '',
        applyUserName: '',
        orgIdLinked: '',
        orgName: '',
        fileUrlList: [],
        saveState: '2',
        stateName: ''
      },
      fileList: [],
      logList: [],
      isHide: false,
      mapDataVisible: false,
      id: '',
      mapData: {
        address: '',
        lng: '',
        lat: '',
        dialogVisible: true
      },
      dialogImageDialogVisible: false,
      dialogImageUrl: '',
      activeName: 'first',
      questionDataList: [],
      evaluateDataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false
    }
  },
  activated () {
    this.getData()
    // if (this.$route.query.state + '' === '2' || this.$route.query.state + '' === '32') {
    //   this.isHide = true
    // }
  },
  methods: {
    getData () {
      this.$http({
        url: this.$http.adornUrl(`/position/position/detail`),
        method: 'get',
        params: { id: this.$route.query.examineId }
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataForm = data.obj
          this.dataForm.stateName = this.statusFormat(this.$route.query.state)
          // 记录列表
          this.logList = data.obj.logList ? data.obj.logList : []
        }
      })
    },
    statusFormat (state) {
      if (state + '' === '1') return '草稿'
      if (state + '' === '2') return '审核中'
      if (state + '' === '4') return '已撤回'
      if (state + '' === '8') return '审核不通过'
      if (state + '' === '16') return '更新中'
      if (state + '' === '32') return '报修中'
      if (state + '' === '64') return '下架中'
      if (state + '' === '128') return '已下架'
      if (state + '' === '256') return '正常'
    },
    openMap () {
      this.mapData.address = this.dataForm.positionAddress
      this.mapData.dialogVisible = false
      this.mapDataVisible = true
      if (this.dataForm.positionMap) {
        this.mapData.lat = this.dataForm.positionMap.split(',')[1]
        this.mapData.lng = this.dataForm.positionMap.split(',')[0]
      }
      this.$nextTick(() => {
        this.$refs.mapDiaglog.visible = true
        this.$refs.mapDiaglog.initMap(this.mapData)
      })
    },
    async getMapData (value) {
    },
    // 图片对话框
    openImage (url) {
      this.dialogImageDialogVisible = true
      this.dialogImageUrl = url
    },
    // 每页数
    questionSizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getQuestionDataList()
    },
    // 当前页
    questionCurrentChangeHandle (val) {
      this.pageIndex = val
      this.getQuestionDataList()
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getEvaluateList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getEvaluateList()
    },
    // 获取随手拍记录列表
    getQuestionDataList () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/position/position/question/page'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'positionCode': this.dataForm.positionCode
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.questionDataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.questionDataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 获取点位测评记录列表
    getEvaluateList () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/evaluate/questionOpInstance/getAllList'),
        method: 'get',
        params: {
          'codeStatus': 'eva_submit',
          'optionCode': this.dataForm.positionCode,
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize
        }
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.evaluateDataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.evaluateDataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // tabs 标签页切换事件
    handleClick (tab, event) {
      // eslint-disable-next-line eqeqeq
      if (tab.index == 1) {
        this.getQuestionDataList()
      }
      // eslint-disable-next-line eqeqeq
      if (tab.index == 2) {
        this.getEvaluateList()
      }
    }
  }
}
</script>

<style scoped>
.el-divider {
  margin: 8px 0;
  background: 0 0;
  border-top: 1px solid #e6ebf5;
}
.item .el-form-item__label {
  /* color: wheat; */
  size: 20px;
}
.text_size {
  width: 25%;
  float: left;
  text-align: left;
  margin-top: 20px;
  word-wrap: break-word;
  word-break: normal;
  height: 80px;
}
</style>
