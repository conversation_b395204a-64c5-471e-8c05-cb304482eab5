<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="queryParams"
      @keyup.enter.native="queryPage()"
    >
      <el-form-item>
        <el-input
          v-model="queryParams.positionCode"
          placeholder="点位编码"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-input
          v-model="queryParams.positionName"
          placeholder="点位名称"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-select
          @change="queryPage()"
          v-model="queryParams.categoryCode"
          placeholder="所属分类"
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="dict in categoryList"
            :key="dict.id"
            :label="dict.name"
            :value="dict.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select
          @change="queryPage()"
          v-model="queryParams.regionCode"
          placeholder="所属区域"
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="dict in regionList"
            :key="dict.id"
            :label="dict.name"
            :value="dict.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="queryParams.startTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="创建开始时间"
          @change="queryPage()"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="queryParams.endTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="创建结束时间"
          @change="queryPage()"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" @click="queryPage()">查询</el-button>
        <el-button
          icon="el-icon-plus"
          v-if="isAuth('business:positionapplyinfo:save')"
          type="primary"
          @click="addOrUpdateHandle()"
          >新增</el-button
        >
        <el-button  icon="el-icon-upload2" v-if="!parentId" type="success" @click="importData()"
          >导入</el-button
        >
        <el-button
          v-if="isAuth('business:positionapplyinfo:delete')"
          type="danger"
          @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0"
          >批量删除</el-button
        >
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%"
    >
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50"
      >
      </el-table-column>
      <el-table-column
        prop="positionCode"
        header-align="center"
        align="center"
        label="点位编码"
      >
      </el-table-column>
      <el-table-column
        prop="positionName"
        header-align="center"
        align="center"
        label="点位名称"
      >
      </el-table-column>
      <el-table-column
        prop="categoryName"
        header-align="center"
        align="center"
        label="所属分类"
      >
      </el-table-column>
      <el-table-column
        prop="regionName"
        header-align="center"
        align="center"
        label="所属街道"
      >
      </el-table-column>
      <el-table-column
        prop="communityName"
        header-align="center"
        align="center"
        label="所属社区"
      >
      </el-table-column>
      <el-table-column
        prop="gridName"
        header-align="center"
        align="center"
        label="所属网格"
      >
      </el-table-column>
      <el-table-column
        prop="positionAddress"
        header-align="center"
        align="center"
        label="详细地址"
      >
      </el-table-column>
      <el-table-column
        prop="createDate"
        header-align="center"
        align="center"
        label="创建时间"
      >
      </el-table-column>
      <el-table-column
        prop="updateDate"
        header-align="center"
        align="center"
        label="更新时间"
      >
      </el-table-column>
      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        label="状态"
        :formatter="statusFormat"
      >
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="200"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.level + '' === '1' || scope.row.level + '' === '2'"
            type="text"
            size="small"
            @click="goOtherPosition(scope.row)"
            >{{
              scope.row.level + "" === "1"
                ? "二"
                : scope.row.level + "" === "2"
                ? "三"
                : ""
            }}级点位</el-button
          >
          <el-button
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row)"
            >编辑</el-button
          >
          <el-button type="text" size="small" @click="deleteHandle(scope.row)"
            >删除</el-button
          >
          <el-button type="text" size="small" @click="detail(scope.row)"
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 30, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination>

    <!-- 弹窗, 新增 / 修改 -->
    <el-dialog
      append-to-body
      :title="!dataForm.id ? '新增' : '修改'"
      :visible.sync="visible"
      :before-close="resetDateForm"
    >
      <el-form
        v-loading="loading"
        :model="dataForm"
        :rules="dataRule"
        ref="dataForm"
        @keyup.enter.native="dataFormSubmit()"
        label-width="150px"
      >
        <div v-if="dataForm.id">
          <el-form-item
            label="编码值"
            prop="positionCode"
            :error="positionCodeError"
          >
            <el-input
              v-model="dataForm.positionCode"
              placeholder="点位编码值"
            ></el-input>
          </el-form-item>
        </div>

        <el-form-item
          label="点位名称"
          prop="positionName"
          :error="positionNameError"
        >
          <el-input
            v-model="dataForm.positionName"
            placeholder="点位名称"
          ></el-input>
        </el-form-item>

        <el-form-item
          label="分类名称"
          prop="categoryName"
          :error="categoryNameError"
        >
          <el-select
            @change="categoryChange(dataForm.categoryCode)"
            v-model="dataForm.categoryCode"
            placeholder="所属分类"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="dict in categoryList"
              :key="dict.id"
              :label="dict.name"
              :value="dict.code"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="所属类型" prop="typeCode">
          <el-dict
            :code="'PointType'"
            v-model="dataForm.typeCode"
            clearable
          ></el-dict>
        </el-form-item>

        <el-form-item
          label="详细地址"
          prop="positionAddress"
          :error="positionAddressError"
        >
          <el-input
            class="inputt"
            @focus="openMap"
            placeholder="请选择地图位置"
            v-model="dataForm.positionAddress"
          >
            <i
              @click="openMap"
              slot="suffix"
              style="margin-right: 5px"
              class="el-icon-location-outline"
            >
            </i>
          </el-input>
        </el-form-item>

        <el-form-item
          label="所处街道"
          prop="regionName"
          :error="positionMapError"
        >
          <el-input
            :disabled="true"
            v-model="dataForm.regionName"
            placeholder="所处街道"
          ></el-input>
        </el-form-item>

        <el-form-item
          label="所处社区"
          prop="communityName"
          :error="positionMapError"
        >
          <el-input
            :disabled="true"
            v-model="dataForm.communityName"
            placeholder="所处社区"
          ></el-input>
        </el-form-item>

        <el-form-item
          label="所处网格"
          prop="gridName"
          :error="positionMapError"
        >
          <el-input
            :disabled="true"
            v-model="dataForm.gridName"
            placeholder="所处网格"
          ></el-input>
        </el-form-item>

        <el-form-item
          label="经纬度（高德）"
          prop="positionMap"
          :error="positionMapError"
        >
          <el-input
            :disabled="true"
            v-model="dataForm.positionMap"
            placeholder="经纬度（高德）"
          ></el-input>
        </el-form-item>

        <el-form-item
          label="经纬度（84坐标系）"
          prop="coordinate84"
          :error="positionMapError"
        >
          <el-input
            :disabled="true"
            v-model="dataForm.coordinate84"
            placeholder="经纬度（84坐标系）"
          ></el-input>
        </el-form-item>

        <el-form-item
          label="经纬度（苏州2000坐标系）"
          prop="coordinate2000"
          :error="positionMapError"
        >
          <el-input
            :disabled="true"
            v-model="dataForm.coordinate2000"
            placeholder="经纬度（苏州2000坐标系）"
          ></el-input>
        </el-form-item>

        <el-form-item label="图片">
          <el-upload
            :action="this.$http.adornUrl('/file/oss/upload')"
            multiple
            :data="{ serverCode: this.serverCode, media: false }"
            :file-list="fileList"
            list-type="picture-card"
            :on-preview="handlePictureCardPreview"
            :on-remove="handleRemove"
            :before-upload="beforeUpload"
            :on-success="handleAvatarSuccess"
            :headers="myHeaders"
          >
            <!-- accept=".png,.jpg,.jpeg,.tiff,.bpm,.webp" -->
            <i class="el-icon-plus"></i>
          </el-upload>

          <el-dialog :visible.sync="dialogVisible" append-to-body>
            <img width="100%" :src="dialogImageUrl" alt="" />
          </el-dialog>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :disabled="addPositionDisabled"
            @click="dataFormSubmit()"
            >确定</el-button
          >
          <el-button @click="resetDateForm()">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 地图 -->
    <mapDiaglog
      v-if="mapDataVisible"
      ref="mapDiaglog"
      @change="getMapData($event)"
    ></mapDiaglog>
  </div>
</template>

<script>
import Vue from 'vue'
import mapDiaglog from '../componet/mapDiaglog.vue'
import detail from './detail.vue'
export default {
  watch: {},
  data () {
    const validatePositionAddress = (rule, value, callback) => {
      if (!this.dataForm.positionAddress) {
        callback(new Error('点位详细地址不能为空'))
      } else {
        callback()
      }
    }
    return {
      queryParams: {
        level: '',
        parentId: '',
        positionCode: '',
        positionName: '',
        categoryName: '',
        categoryCode: '',
        regionCode: '',
        startTime: '',
        endTime: ''
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 30,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      statusList: [
        { 'id': 1, 'label': '草稿' },
        { 'id': 2, 'label': '审核中' },
        { 'id': 4, 'label': '已撤回' },
        { 'id': 8, 'label': '审核不通过' },
        { 'id': 16, 'label': '更新中' },
        { 'id': 32, 'label': '报修中' },
        { 'id': 64, 'label': '下架中' },
        { 'id': 128, 'label': '已下架' },
        { 'id': 256, 'label': '正常' }
      ],
      rowStatus: '',
      detailVisible: false,
      visible: false,
      dataForm: {
        id: null,
        version: null,
        positionCode: '',
        positionName: '',
        categoryCode: '',
        categoryName: '',
        regionCode: '',
        regionName: '',
        communityCode: '',
        communityName: '',
        gridCode: '',
        gridName: '',
        positionMap: '',
        coordinate84: '',
        coordinate2000: '',
        positionAddress: '',
        qrCodeBinding: '',
        source: 1,
        parentId: '',
        level: '',
        applyUserId: '',
        applyUserName: '',
        orgIdLinked: '',
        orgName: '',
        fileUrlList: [],
        saveState: '2',
        lat: '',
        lng: '',
        typeCode: 'StaticPoint'
      },
      fileList: [],
      dataRule: {
        positionName: [
          { required: true, message: '点位名称不能为空', trigger: 'blur' }
        ],
        typeCode: [
          { required: true, message: '点位所属类型不能为空', trigger: 'change' }
        ],
        regionName: [
          { required: true, message: '街道名称不能为空', trigger: 'blur' }
        ],
        communityName: [
          { required: true, message: '社区名称不能为空', trigger: 'blur' }
        ],
        gridName: [
          { required: true, message: '网格名称不能为空', trigger: 'blur' }
        ],
        positionMap: [
          { required: true, message: '高德地图定位获取到的坐标不能为空', trigger: 'blur' }
        ],
        coordinate84: [
          { required: true, message: '84坐标系坐标数据不能为空', trigger: 'blur' }
        ],
        coordinate2000: [
          { required: true, message: '苏州2000坐标系坐标数据不能为空', trigger: 'blur' }
        ],
        positionAddress: [
          { required: true, validator: validatePositionAddress, trigger: 'change' }
        ]
      },
      positionCodeError: null,
      positionNameError: null,
      categoryCodeError: null,
      categoryNameError: null,
      regionCodeError: null,
      regionNameError: null,
      gridCodeError: null,
      gridNameError: null,
      positionMapError: null,
      positionAddressError: null,
      qrCodeBindingError: null,
      sourceError: null,
      parentIdError: null,
      levelError: null,
      applyUserIdError: null,
      applyUserNameError: null,
      orgIdLinkedError: null,
      orgNameError: null,
      orgList: [],
      orgProps: {
        key: 'id',
        label: 'name',
        children: 'children'
      },
      categoryList: [],
      serverCode: 'LocalServer',
      myHeaders: { Authorization: sessionStorage.getItem('Authorization') },
      dialogVisible: false,
      dialogImageUrl: '',
      fileUrlList: [],
      mapDataVisible: false,
      id: '',
      mapData: {
        address: '',
        lng: '',
        lat: '',
        dialogVisible: true
      },
      regionList: [],
      parentId: '',
      loading: false,
      addPositionDisabled: false,
      fullscreenLoading: null
    }
  },
  components: {
    detail,
    mapDiaglog
  },
  activated () {
    this.parentId = this.$route.query.parentId
    this.queryPage()
    this.getAreaType()
    this.getCategoryList()
  },
  methods: {
    openMap () {
      if (this.dataForm.positionMap) {
        this.mapData.lng = this.dataForm.positionMap.split(',')[0]
        this.mapData.lat = this.dataForm.positionMap.split(',')[1]
      }
      if (this.dataForm.positionAddress) {
        this.mapData.address = this.dataForm.positionAddress
      }
      this.mapDataVisible = true
      this.$nextTick(() => {
        this.$refs.mapDiaglog.visible = true
        this.$refs.mapDiaglog.initMap(this.mapData)
      })
    },
    async getMapData (value) {
      // this.fullscreenLoading = this.$loading({
      //   lock: true,
      //   text: '数据加载中',
      //   spinner: 'el-icon-loading',
      //   background: 'rgba(0, 0, 0, 0.7)'
      // })
      this.loading = true
      this.mapData = value
      this.dataForm.lng = value.lng
      this.dataForm.lat = value.lat
      this.getCeHuiLocation(value.lng, value.lat)
      if (value.lat) {
        this.dataForm.positionAddress = value.address
        this.dataForm.positionMap = this.getSixNum(value.lng + '') + ',' + this.getSixNum(value.lat + '')
      }
    },
    // 经纬度获取测绘区域信息
    getCeHuiLocation (lng, lat) {
      this.$http({
        url: this.$http.adornUrl('/position/area/obtain'),
        method: 'get',
        params: this.$http.adornParams({
          'longitude': lng,
          'latitude': lat
        })
      }).then(({ data }) => {
        this.loading = false
        // this.fullscreenLoading.close()
        this.dataForm.regionCode = ''
        this.dataForm.regionName = ''
        this.dataForm.communityCode = ''
        this.dataForm.communityName = ''
        this.dataForm.gridCode = ''
        this.dataForm.gridName = ''
        this.dataForm.coordinate84 = ''
        this.dataForm.coordinate2000 = ''
        if (data && data.code === 0) {
          this.dataForm.regionCode = data.obj.regionCode
          this.dataForm.regionName = data.obj.regionName
          this.dataForm.communityCode = data.obj.communityCode
          this.dataForm.communityName = data.obj.communityName
          this.dataForm.gridCode = data.obj.gridName
          this.dataForm.gridName = data.obj.gridName
          this.dataForm.coordinate84 = data.obj.coordinate84
          this.dataForm.coordinate2000 = data.obj.coordinate2000
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    beforeUpload (file) {
      let filePath = file.name
      let index = filePath.lastIndexOf('.')
      let ext = filePath.substring(index + 1)
      const isImage = ['png', 'jpg'].indexOf(ext.toLowerCase()) !== -1
      const isLt5M = file.size / 1024 / 1024 < 5

      if (!isImage) {
        this.$message.error('上传的格式只能是PNG,JPG格式')
        return false
      }
      if (!isLt5M) {
        this.$message.error('上传图片大小不能超过 5MB!')
        return false
      }
      return isImage && isLt5M
    },

    // 获取小数点后六位
    getSixNum (data) {
      if (data.indexOf('.') !== -1) {
        let prefix = data.substr(0, data.indexOf('.') + 1)
        let lat = data.substr(data.indexOf('.') + 1)
        if (lat.length < 6) {
          let length = 6 - lat.length
          for (var i = 0; i < length; i++) {
            lat = lat + '0'
          }
        }
        return prefix + lat
      }
      return data
    },
    filterNode (value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    queryPage () {
      this.pageIndex = 1
      this.getDataList()
    },
    // 获取数据列表
    async getDataList () {
      if (this.$route.query.parentId) {
        this.queryParams.parentId = this.$route.query.parentId
      } else {
        this.queryParams.parentId = '0'
      }
      this.dataListLoading = true
      await this.$http({
        url: this.$http.adornUrl('/position/position/getPages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'parentId': this.queryParams.parentId,
          'positionCode': this.queryParams.positionCode,
          'positionName': this.queryParams.positionName,
          'regionCode': this.queryParams.regionCode,
          'categoryName': this.queryParams.categoryName,
          'categoryCode': this.queryParams.categoryCode,
          'startTime': this.queryParams.startTime,
          'endTime': this.queryParams.endTime
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    statusFormat (row, column) {
      if (row.positionExamine.state + '' === '1') return '草稿'
      if (row.positionExamine.state + '' === '2') return '审核中'
      if (row.positionExamine.state + '' === '4') return '已撤回'
      if (row.positionExamine.state + '' === '8') return '审核不通过'
      if (row.positionExamine.state + '' === '16') return '更新中'
      if (row.positionExamine.state + '' === '32') return '报修中'
      if (row.positionExamine.state + '' === '64') return '下架中'
      if (row.positionExamine.state + '' === '128') return '已下架'
      if (row.positionExamine.state + '' === '256') return '正常'
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle (val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle (row) {
      this.fileList = []
      this.dataForm.categoryCode = ''
      this.dataForm.categoryName = ''
      this.dataForm.regionCode = ''
      this.dataForm.regionName = ''
      this.mapData = {
        address: '',
        lng: '',
        lat: '',
        dialogVisible: true
      }
      this.getOrgList()
      const id = row ? row.id : ''
      this.addOrUpdateVisible = true
      this.getDetail(id)
      this.getCategoryList()
    },
    detail (row) {
      this.$router.push({
        path: '/examineDetail',
        query: {
          id: row.id,
          code: row.positionCode,
          examineId: row.id,
          state: row.positionExamine.state
        }
      })
    },
    // 删除
    deleteHandle (row) {
      var ids = row.id ? row.id : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定进行[${row.positionName ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/position/position/delete'),
          method: 'get',
          params: this.$http.adornParams({
            'id': ids
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    getDetail (id) {
      this.loading = true
      if (!this.dataForm.id) {
        this.loading = false
      }
      this.fileList = []
      this.dataForm.id = id || null

      this.visible = true

      this.dataForm.positionCode = ''
      this.dataForm.regionCode = ''
      this.dataForm.regionName = ''
      this.dataForm.categoryName = ''
      this.dataForm.categoryName = ''
      this.dataForm.source = 1

      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/position/position/detail`),
            method: 'get',
            params: { id: this.dataForm.id }
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
              this.dataForm.fileUrlList.forEach(data => {
                const file = {
                  name: '',
                  url: window.SITE_CONFIG.baseUrl + '/file' + data
                }
                this.fileList.push(file)
              })
            }
            this.loading = false
          })
        } else {
          this.loading = false
        }
      })
      this.loading = false
    },
    getOrgList () {
      this.$http({
        url: this.$http.adornUrl('/admin/org/tree/user'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        this.orgList = data.obj || []
      })
    },
    // 属性区域列表
    getAreaType () {
      this.$http({
        url: this.$http.adornUrl('/position/area/top')
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.regionList = data.obj
        } else {
          this.regionList = {}
        }
      })
    },
    getCategoryList () {
      this.$http({
        url: this.$http.adornUrl('/position/category/list'),
        method: 'get',
        params: { categoryCode: this.$route.query.categoryCode, status: 1 }
      }).then(({ data }) => {
        this.categoryList = data.obj || []
      })
    },
    // 表单提交
    dataFormSubmit () {
      if (this.$route.query.parentId) {
        this.dataForm.parentId = this.$route.query.parentId
      } else {
        this.dataForm.parentId = '0'
      }
      if (this.$route.query.level) {
        this.dataForm.level = this.$route.query.level
      } else {
        this.dataForm.level = '1'
      }
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.addPositionDisabled = true
          this.fileUrlList = []
          this.fileList.forEach(data => {
            data.path = data.url.replace(window.SITE_CONFIG.baseUrl + '/file', '')
            this.fileUrlList.push(data.path)
          })
          this.dataForm.fileUrlList = this.fileUrlList
          this.dataForm.saveState = '2'
          this.$http({
            url: this.$http.adornUrl(`/position/position/report`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success'
              })
              this.visible = false
              this.getDataList()
              this.fileUrlList = []
              this.fileList = []
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.$message.error(data.msg)
            }
            setTimeout(() => {
              this.addPositionDisabled = false
            }, 1000)
          })
        }
      })
    },
    // 区域树选中
    orgTreeCurrentChangeHandle (data, node) {
      this.dataForm.orgIdLinked = data.code
      this.dataForm.orgName = data.name
      this.$refs[`orgListPopover`].doClose()
    },
    // 区域树设置当前选中节点
    orgListTreeSetCurrentNode () {
      let key = this.dataForm.orgIdLinked
      if (key) {
        this.$refs.orgTree.setCurrentKey(key)
        this.dataForm.orgName = (this.$refs.orgTree.getCurrentNode() || {})['name']
      } else {
        this.$refs.orgTree.setCurrentKey([])
        this.dataForm.orgName = ''
      }
    },
    // 区域树选中
    categoryTreeCurrentChangeHandle (data, node) {
      this.dataForm.categoryCode = data.code
      this.dataForm.categoryName = data.name
      this.$refs[`categoryListPopover`].doClose()
    },
    // 区域树设置当前选中节点
    categoryTreeSetCurrentNode () {
      let key = this.dataForm.categoryCode
      if (key) {
        this.$refs.orgTree.setCurrentKey(key)
        this.dataForm.categoryName = (this.$refs.orgTree.getCurrentNode() || {})['name']
      } else {
        this.$refs.orgTree.setCurrentKey([])
        this.dataForm.categoryName = ''
      }
    },

    // 区域树选中
    categoryTreeCurrentChangeHandle2 (data, node) {
      this.queryParams.categoryCode = data.code
      this.queryParams.categoryName = data.name
      this.$refs[`categoryListPopover2`].doClose()
    },
    // 区域树设置当前选中节点
    categoryTreeSetCurrentNode2 () {
      let key = this.dataForm.categoryCode
      if (key) {
        this.$refs.orgTree.setCurrentKey(key)
        this.queryParams.categoryName = (this.$refs.orgTree.getCurrentNode() || {})['name']
      } else {
        this.$refs.orgTree.setCurrentKey([])
        this.queryParams.categoryName = ''
      }
    },
    handleRemove (file, fileList) {
      if (this.fileList.indexOf(file) === -1) {

      } else {
        this.fileList.splice(this.fileList.indexOf(file), 1)
      }
    },
    handlePictureCardPreview (file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleAvatarSuccess (res, file, fileList) {
      const a = {
        id: res.obj.id,
        name: res.obj.name,
        url: window.SITE_CONFIG.baseUrl + '/file' + res.obj.path
      }
      this.fileList.push(a)
      console.log(this.fileList)
      this.dialogImageUrl = window.SITE_CONFIG.baseUrl + '/file' + res.obj.path
    },
    goOtherPosition (row) {
      if (row.level + '' === '1') {
        this.$router.push({
          meta: { title: '二级点位', isTab: true },
          path: '/twoPosition',
          query: { level: 2, parentId: row.id, categoryCode: row.categoryCode }
        })
      } else if (row.level + '' === '2') {
        this.$router.push({
          path: '/threePosition',
          query: { level: 3, parentId: row.id, categoryCode: row.categoryCode }
        })
      }
    },
    resetDateForm () {
      // this.$refs['dataForm'].validate()
      this.fileUrlList = []
      this.fileList = []
      this.visible = false
    },
    importData () {
      this.$router.push({
        path: '/evaluateImport',
        query: {
          parentId: this.$route.query.parentId,
          type: 2
        }
      })
    },
    categoryChange () {
      this.categoryList.forEach(data => {
        if (data.code + '' === this.dataForm.categoryCode + '') {
          this.dataForm.categoryName = data.name
        }
      })
    }

  }
}
</script>
<style scoped>
#fixtree {
  display: inline-block;
}
.dir-tree {
  width: 100%;
  height: 300px;
}

.el-tree {
  min-width: 100%;
  display: inline-block !important;
}

.el-scrollbar .el-scrollbar__wrap {
  overflow: auto;
  overflow-x: hidden;
}
</style>
