<template>
  <el-dialog
    append-to-body
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="150px"
    >
      <div v-if="dataForm.id">
        <el-form-item
          label="编码值"
          prop="positionCode"
          :error="positionCodeError"
        >
          <el-input
            v-model="dataForm.positionCode"
            placeholder="点位编码值"
            :disabled="true"
          ></el-input>
        </el-form-item>
      </div>

      <el-form-item
        label="点位名称"
        prop="positionName"
        :error="positionNameError"
      >
        <el-input
          v-model="dataForm.positionName"
          placeholder="点位名称"
        ></el-input>
      </el-form-item>

      <el-form-item
        label="分类名称"
        prop="categoryName"
        :error="categoryNameError"
      >
        <el-popover
          ref="categoryListPopover"
          placement="bottom-start"
          trigger="click"
        >
          <el-tree
            :data="categoryList"
            :props="orgProps"
            node-key="id"
            ref="orgTree"
            @current-change="categoryTreeCurrentChangeHandle"
            :default-expand-all="true"
            :highlight-current="true"
            :expand-on-click-node="false"
          >
          </el-tree>
        </el-popover>
        <el-input
          v-model="dataForm.categoryName"
          v-popover:categoryListPopover
          :readonly="true"
          placeholder="点击选择分类架构"
        ></el-input>
      </el-form-item>

      <el-form-item label="区域名称" prop="regionName" :error="regionNameError">
        <el-input
          v-model="dataForm.regionName"
          placeholder="区域名称"
        ></el-input>
      </el-form-item>

      <el-form-item label="网格名称" prop="gridName" :error="gridNameError">
        <el-input v-model="dataForm.gridName" placeholder="网格名称"></el-input>
      </el-form-item>

      <el-form-item
        label="详细地址"
        prop="positionAddress"
        :error="positionAddressError"
      >
        <el-input
          v-model="dataForm.positionAddress"
          placeholder="详细地址"
        ></el-input>
      </el-form-item>

      <el-form-item label="经纬度" prop="positionMap" :error="positionMapError">
        <el-input
          v-model="dataForm.positionMap"
          placeholder="经纬度"
        ></el-input>
      </el-form-item>

      <el-form-item label="收集来源" prop="source" :error="sourceError">
        <el-radio-group v-model="dataForm.source">
          <el-radio :label="0">小程序</el-radio>
          <el-radio :label="1">后台录入</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="点位等级" prop="level" :error="levelError">
        <el-input-number
          v-model="dataForm.level"
          controls-position="right"
          :min="0"
        ></el-input-number>
      </el-form-item>

      <el-form-item label="组织名称" prop="orgName" :error="orgNameError">
        <!-- <el-input v-model="dataForm.orgName" placeholder="组织名称"></el-input> -->
        <el-popover
          ref="orgListPopover"
          placement="bottom-start"
          trigger="click"
        >
          <el-tree
            :data="orgList"
            :props="orgProps"
            node-key="id"
            ref="orgTree"
            @current-change="orgTreeCurrentChangeHandle"
            :default-expand-all="true"
            :highlight-current="true"
            :expand-on-click-node="false"
          >
          </el-tree>
        </el-popover>
        <el-input
          v-model="dataForm.orgName"
          v-popover:orgListPopover
          :readonly="true"
          placeholder="点击选择组织架构"
        ></el-input>
      </el-form-item>

      <el-form-item label="图片">
        <!-- <el-input
          v-model="dataForm.orgIdLinked"
          placeholder="组织部门编码集合"
        ></el-input> -->
        <el-upload
          :action="this.$http.adornUrl('/file/oss/upload')"
          multiple
          :data="{ serverCode: this.serverCode, media: false }"
          :file-list="fileList"
          list-type="picture-card"
          :on-preview="handlePictureCardPreview"
          :on-remove="handleRemove"
          :on-success="handleAvatarSuccess"
          :headers="myHeaders"
        >
          <i class="el-icon-plus"></i>
        </el-upload>

        <el-dialog :visible.sync="dialogVisible" append-to-body>
          <img width="100%" :src="dialogImageUrl" alt="" />
        </el-dialog>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Vue from 'vue'
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: null,
        version: null,
        positionCode: '',
        positionName: '',
        categoryCode: '',
        categoryName: '',
        regionCode: '',
        regionName: '',
        gridCode: '',
        gridName: '',
        positionMap: '',
        positionAddress: '',
        qrCodeBinding: '',
        source: 1,
        parentId: '',
        level: '',
        applyUserId: '',
        applyUserName: '',
        orgIdLinked: '',
        orgName: '',
        fileUrlList: [],
        saveState: '2'
      },
      fileList: [],
      dataRule: {
        positionName: [
          { required: true, message: '点位名称不能为空', trigger: 'blur' }
        ]
        // categoryName: [
        //   { required: true, message: '点位分类名称不能为空', trigger: 'blur' }
        // ],
        // regionCode: [
        //   { required: true, message: '区域编码值不能为空', trigger: 'blur' }
        // ],
        // regionName: [
        //   { required: true, message: '区域名称不能为空', trigger: 'blur' }
        // ],
        // gridCode: [
        //   { required: true, message: '网格编码值不能为空', trigger: 'blur' }
        // ],
        // gridName: [
        //   { required: true, message: '网格名称不能为空', trigger: 'blur' }
        // ],
        // positionMap: [
        //   { required: true, message: '地图定位获取到的地址（测绘的接口获取）不能为空', trigger: 'blur' }
        // ],
        // positionAddress: [
        //   { required: true, message: '手动填写的详细数据（测绘的接口获取）不能为空', trigger: 'blur' }
        // ],
        // qrCodeBinding: [
        //   { required: true, message: '绑定的二维码编码不能为空', trigger: 'blur' }
        // ],
        // source: [
        //   { required: true, message: '收集来源 默认0-小程序 1-后台录入不能为空', trigger: 'blur' }
        // ],
        // parentId: [
        //   { required: true, message: '上级点位ID（没有显示为0）不能为空', trigger: 'blur' }
        // ],
        // level: [
        //   { required: true, message: '点位等级不能为空', trigger: 'blur' }
        // ],
        // applyUserId: [
        //   { required: true, message: '申请人ID不能为空', trigger: 'blur' }
        // ],
        // applyUserName: [
        //   { required: true, message: '申请人不能为空', trigger: 'blur' }
        // ],
        // orgIdLinked: [
        //   { required: true, message: '组织部门编码集合不能为空', trigger: 'blur' }
        // ],
        // orgName: [
        //   { required: true, message: '组织名称不能为空', trigger: 'blur' }
        // ]
      },
      positionCodeError: null,
      positionNameError: null,
      categoryCodeError: null,
      categoryNameError: null,
      regionCodeError: null,
      regionNameError: null,
      gridCodeError: null,
      gridNameError: null,
      positionMapError: null,
      positionAddressError: null,
      qrCodeBindingError: null,
      sourceError: null,
      parentIdError: null,
      levelError: null,
      applyUserIdError: null,
      applyUserNameError: null,
      orgIdLinkedError: null,
      orgNameError: null,
      orgList: [],
      orgProps: {
        key: 'id',
        label: 'name',
        children: 'children'
      },
      categoryList: [],
      serverCode: 'LocalServer',
      myHeaders: { Authorization: sessionStorage.getItem('Authorization') },
      dialogVisible: false,
      dialogImageUrl: '',
      fileUrlList: []
    }
  },
  activated () {
    this.getOrgList()
    this.getCategoryList()
  },
  methods: {
    init (id) {
      this.getOrgList()
      this.getCategoryList()
      this.fileList = []
      this.dataForm.id = id || null
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/position/position/detail`),
            method: 'get',
            params: { id: this.dataForm.id }
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
              this.dataForm.fileUrlList.forEach(data => {
                const file = {
                  name: '',
                  url: window.SITE_CONFIG.baseUrl + '/file' + data
                }
                this.fileList.push(file)
              })
            }
          })
        }
      })
    },
    getOrgList () {
      this.$http({
        url: this.$http.adornUrl('/admin/org/tree/user'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        this.orgList = data.obj || []
      })
    },
    getCategoryList () {
      this.$http({
        url: this.$http.adornUrl('/position/category/list'),
        method: 'get',
        params: this.$http.adornParams({ categoryCode: this.$route.query.categoryCode, status: 1 })
      }).then(({ data }) => {
        this.categoryList = data.obj || []
      })
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.fileList.forEach(data => {
            data.path = data.url.replace(window.SITE_CONFIG.baseUrl + '/file', '')
            this.fileUrlList.push(data.path)
          })
          this.dataForm.fileUrlList = this.fileUrlList
          this.dataForm.saveState = '2'
          this.$http({
            url: this.$http.adornUrl(`/position/position/report`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm = {}
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    // 区域树选中
    orgTreeCurrentChangeHandle (data, node) {
      this.dataForm.orgIdLinked = data.code
      this.dataForm.orgName = data.name
      this.$refs[`orgListPopover`].doClose()
    },
    // 区域树设置当前选中节点
    orgListTreeSetCurrentNode () {
      let key = this.dataForm.orgIdLinked
      if (key) {
        this.$refs.orgTree.setCurrentKey(key)
        this.dataForm.orgName = (this.$refs.orgTree.getCurrentNode() || {})['name']
      } else {
        this.$refs.orgTree.setCurrentKey([])
        this.dataForm.orgName = ''
      }
    },
    // 区域树选中
    categoryTreeCurrentChangeHandle (data, node) {
      this.dataForm.categoryCode = data.code
      this.dataForm.categoryName = data.name
      this.$refs[`categoryListPopover`].doClose()
    },
    // 区域树设置当前选中节点
    categoryTreeSetCurrentNode () {
      let key = this.dataForm.categoryCode
      if (key) {
        this.$refs.orgTree.setCurrentKey(key)
        this.dataForm.categoryName = (this.$refs.orgTree.getCurrentNode() || {})['name']
      } else {
        this.$refs.orgTree.setCurrentKey([])
        this.dataForm.categoryName = ''
      }
    },
    handleRemove (file, fileList) {
      this.fileList.splice(this.fileList.indexOf(file.url), 1)
    },
    handlePictureCardPreview (file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleAvatarSuccess (res, file) {
      const a = {
        name: res.obj.name,
        url: window.SITE_CONFIG.baseUrl + '/file' + res.obj.path
      }
      this.fileList.push(a)
      this.dialogImageUrl = window.SITE_CONFIG.baseUrl + '/file' + res.obj.path
    }
  }
}
</script>
