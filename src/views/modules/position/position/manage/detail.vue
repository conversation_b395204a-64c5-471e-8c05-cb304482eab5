<template>
  <el-dialog append-to-body title="详情" :close-on-click-modal="false" :visible.sync="visible">
    <el-form
      :model="dataForm"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="150px"
    >
      <div v-if="dataForm.id">
        <el-form-item
          label="编码值"
          prop="positionCode"
          :error="positionCodeError"
        >
          <span align="left">{{ dataForm.positionCode }}</span>
        </el-form-item>
      </div>

      <el-form-item
        label="点位名称"
        prop="positionName"
        :error="positionNameError"
      >
        <span align="left">{{ dataForm.positionName }}</span>
      </el-form-item>

      <el-form-item
        label="分类名称"
        prop="categoryName"
        :error="categoryNameError"
      >
        <span align="left">{{ dataForm.categoryName }}</span>
      </el-form-item>

      <el-form-item label="区域名称" prop="regionName" :error="regionNameError">
        <span align="left">{{ dataForm.regionName }}</span>
      </el-form-item>

      <el-form-item label="网格名称" prop="gridName" :error="gridNameError">
        <span align="left">{{ dataForm.gridName }}</span>
      </el-form-item>

      <el-form-item
        label="详细地址"
        prop="positionAddress"
        :error="positionAddressError"
      >
        <span align="left">{{ dataForm.positionAddress }}</span>
      </el-form-item>

      <el-form-item label="经纬度" prop="positionMap" :error="positionMapError">
        <span align="left">{{ dataForm.positionMap }}</span>
      </el-form-item>

      <el-form-item label="收集来源" prop="source" :error="sourceError">
        <span align="left">
          {{ dataForm.source + "" === "0" ? "小程序" : "后台录入" }}
        </span>
      </el-form-item>

      <el-form-item label="点位等级" prop="level" :error="levelError">
        <span align="left">{{ dataForm.level }}</span>
      </el-form-item>

      <el-form-item label="组织名称" prop="orgName" :error="orgNameError">
        <!-- <el-input v-model="dataForm.orgName" placeholder="组织名称"></el-input> -->
        <span align="left">{{ dataForm.orgName }}</span>
      </el-form-item>

      <el-form-item label="图片">
        <img
          v-for="(item, index) in dataForm.fileUrlList"
          :key="index"
          :src="$http.adornUrl('/file' + item)"
          style="width: 30%; margin-left: 15px"
        />

        <el-dialog  :visible.sync="dialogVisible" append-to-body>
          <img width="100%" :src="dialogImageUrl" alt="" />
        </el-dialog>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="visible = false">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Vue from 'vue'
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: null,
        version: null,
        positionCode: '',
        positionName: '',
        categoryCode: '',
        categoryName: '',
        regionCode: '',
        regionName: '',
        gridCode: '',
        gridName: '',
        positionMap: '',
        positionAddress: '',
        qrCodeBinding: '',
        source: 1,
        parentId: '',
        level: '',
        applyUserId: '',
        applyUserName: '',
        orgIdLinked: '',
        orgName: '',
        fileUrlList: [],
        saveState: '2'
      },
      fileList: [],
      positionCodeError: null,
      positionNameError: null,
      categoryCodeError: null,
      categoryNameError: null,
      regionCodeError: null,
      regionNameError: null,
      gridCodeError: null,
      gridNameError: null,
      positionMapError: null,
      positionAddressError: null,
      qrCodeBindingError: null,
      sourceError: null,
      parentIdError: null,
      levelError: null,
      applyUserIdError: null,
      applyUserNameError: null,
      orgIdLinkedError: null,
      orgNameError: null,
      orgList: [],
      orgProps: {
        key: 'id',
        label: 'name',
        children: 'children'
      },
      categoryList: [],
      serverCode: 'LocalServer',
      myHeaders: { Authorization: sessionStorage.getItem('Authorization') },
      dialogVisible: false,
      dialogImageUrl: '',
      fileUrlList: []
    }
  },
  methods: {
    init (id) {
      this.fileList = []
      this.dataForm.id = id || null
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/position/position/detail`),
            method: 'get',
            params: { id: this.dataForm.id }
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
            }
          })
        }
      })
    }
  }
}
</script>
