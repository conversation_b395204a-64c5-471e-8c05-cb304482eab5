<template >
  <div>
    <el-dialog
      append-to-body
      title="地图选择位置"
      :visible.sync="visible"
      :close-on-click-modal="false"
      top="5vh"
      width="60%"
    >
      <div>
        <div class="search-box">
          <input
            style="80%"
            clearable
            v-model="mapData.address"
            type="search"
            id="search"
          />
          <el-button type="primary" @click="searchByHand()">搜索</el-button>
          <div class="tip-box" id="searchTip"></div>
        </div>
      </div>
      <br /><br /><br />
      <div style="width: 100%; height: 600px">
        <div class="container">
          <!--
          amap-manager： 地图管理对象
          vid：地图容器节点的ID
          zooms： 地图显示的缩放级别范围，在PC上，默认范围[3,18]，取值范围[3-18]；在移动设备上，默认范围[3-19]，取值范围[3-19]
          center： 地图中心点坐标值
          plugin：地图使用的插件
          events： 事件
        -->
          <el-amap
            class="amap-box"
            :amap-manager="amapManager"
            :vid="'amap-vue'"
            :zoom="zoom"
            :plugin="plugin"
            :center="center"
            :events="events"
          >
            <!-- 标记 -->
            <el-amap-marker
              v-for="(marker, index) in markers"
              :position="marker"
              :key="index"
            ></el-amap-marker>
          </el-amap>
        </div>
      </div>
      <div style="margin-top: 15px">
        <i class="el-icon-warning"></i>
        <span>请点击地图选择点位位置等信息</span><br />
        <p>当前位置：{{ this.mapData.address }}</p>
        <p>
          经纬度：
          <span v-if="this.mapData.lng">
            {{ this.mapData.lng + "," + this.mapData.lat }}
          </span>
        </p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addMapData()">确 定</el-button>
        <el-button @click="visible = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { AMapManager, lazyAMapApiLoaderInstance } from 'vue-amap'
let amapManager = new AMapManager()
export default {
  name: 'AMap',
  data () {
    let self = this
    return {
      mapData: {
        address: '',
        lng: '',
        lat: ''
      },
      visible: true,
      address: null,
      searchKey: '',
      amapManager,
      markers: [],
      searchOption: {
        city: '全国',
        citylimit: true
      },
      center: [120.723299, 31.323974],
      zoom: 12, // 地图缩放，zoom越小，看的内容越多
      lng: 0,
      lat: 0,
      loaded: false,
      events: {
        init () {
          lazyAMapApiLoaderInstance.load().then(() => {
            self.initSearch()
          })
        },
        // 点击获取地址的数据
        click (e) {
          self.markers = []
          let { lng, lat } = e.lnglat
          self.lng = lng
          self.lat = lat
          self.center = [lng, lat]
          self.mapData.lat = lat
          self.mapData.lng = lng
          self.markers.push([lng, lat])
          // 这里通过高德 SDK 完成。
          let geocoder = new AMap.Geocoder({
            radius: 1000,
            extensions: 'all'
          })
          geocoder.getAddress([lng, lat], function (status, result) {
            if (status === 'complete' && result.info === 'OK') {
              if (result && result.regeocode) {
                self.address = result.regeocode.formattedAddress
                self.mapData.address = result.regeocode.formattedAddress
                self.$nextTick()
              }
            }
          })
        }
      },
      // 一些工具插件
      plugin: [
        {
          pName: 'Geocoder',
          events: {
            init (o) {
            }
          }
        },
        {
          // 定位
          pName: 'Geolocation',
          events: {
            init (o) {
              // o是高德地图定位插件实例
              o.getCurrentPosition((status, result) => {
                if (result && result.position) {
                  // 设置经度
                  self.lng = result.position.lng
                  // 设置维度
                  self.lat = result.position.lat
                  // 设置坐标
                  self.center = [self.lng, self.lat]
                  self.markers.push([self.lng, self.lat])
                  // load
                  self.loaded = true
                  // 页面渲染好后
                  self.$nextTick()
                }
              })
            }
          }
        },
        {
          // 工具栏
          pName: 'ToolBar',
          events: {
            init (instance) {
              // console.log("工具栏:"+instance);
            }
          }
        },
        {
          // 鹰眼
          pName: 'OverView',
          events: {
            init (instance) {
              // console.log("鹰眼:"+instance);
            }
          }
        },
        {
          // 地图类型
          pName: 'MapType',
          defaultType: 0,
          events: {
            init (instance) {
              // console.log("地图类型:"+instance);
            }
          }
        },
        {
          // 搜索
          pName: 'PlaceSearch',
          events: {
            init (instance) {
              // console.log("搜索:"+instance)
            }
          }
        }
      ]
    }
  },
  methods: {
    initMap (mapData) {
      this.mapData.address = mapData.address
      this.mapData.lng = mapData.lng
      this.mapData.lat = mapData.lat
      this.markers = [this.mapData.lng, this.mapData.lat]
      // this.poiPicker.searchByKeyword(this.mapData.address)

      // this.lng = mapData.lng
      // this.lat = mapData.lat
      // this.center = [this.lng, this.lat]
      // this.markers = [this.lng, this.lat]
      var map = new AMap.Map('container')
      var marker = new AMap.Marker({
        position: new AMap.LngLat(this.lng, this.lat),   // 经纬度对象，也可以是经纬度构成的一维数组[116.39, 39.9]
        title: 'this.mapData.address'
      })

      // 将创建的点标记添加到已有的地图实例：
      map.add(marker)
      // let geocoder = new AMap.Geocoder({
      //   radius: 1000,
      //   extensions: 'all'
      // })
      // geocoder.getAddress([this.lng, this.lat], function (status, result) {
      //   if (status === 'complete' && result.info === 'OK') {
      //     if (result && result.regeocode) {
      //       this.address = result.regeocode.formattedAddress
      //       this.mapData.address = result.regeocode.formattedAddress
      //       this.$nextTick()
      //     }
      //   }
      // })
    },
    addMapData () {
      this.visible = false
      this.$emit('change', this.mapData)
    },
    onSearchResult (pois) {
      const vm = this
      vm.amap.markers = []
      let latSum = 0
      let lngSum = 0
      if (pois.length > 0) {
        pois.forEach((poi, index) => {
          const { lng, lat } = poi
          if (index === 0) {
            lngSum = lng
            latSum = lat
            const obj = {
              position: [poi.lng, poi.lat],
              text: poi.name,
              offset: [0, 30]
            }
            vm.amap.markers.push(obj)
            // console.log('地图搜索回调', poi)
            vm.dataForm.orgAddr = poi.name
            vm.dataForm.lat = poi.lat ? poi.lat.toString() : ''
            vm.dataForm.lon = poi.lng ? poi.lng.toString() : ''
          }
        })
        vm.amap.center = [lngSum, latSum]
      }
    },
    initSearch () {
      let vm = this
      let map = this.amapManager.getMap()
      AMapUI.loadUI(['misc/PoiPicker'], function (PoiPicker) {
        var poiPicker = new PoiPicker({
          input: 'search',
          placeSearchOptions: {
            map: map,
            pageSize: 10
          },
          suggestContainer: 'searchTip',
          searchResultsContainer: 'searchTip'
        })
        vm.poiPicker = poiPicker
        // 监听poi选中信息
        poiPicker.on('poiPicked', function (poiResult) {
          // console.log(poiResult)
          let source = poiResult.source
          let poi = poiResult.item
          if (source !== 'search') {
            poiPicker.searchByKeyword(poi.name)
          } else {
            poiPicker.clearSearchResults()
            vm.markers = []
            let lng = poi.location.lng
            let lat = poi.location.lat
            let address = poi.cityname + poi.adname + poi.name
            vm.center = [lng, lat]
            vm.markers.push([lng, lat])
            vm.lng = lng
            vm.lat = lat
            vm.address = address
            vm.searchKey = address
          }
        })
      })
    },
    searchByHand () {
      if (this.mapData.address !== '') {
        this.poiPicker.searchByKeyword(this.mapData.address)
        // this.poiPicker.searchByKeyword(this.searchKey)
      }
    },
    handleClose (done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done()
        })
        .catch(_ => { })
    }
  }
}
</script>

<style lang="css">
.container {
  width: 100%;
  height: 100%;
  position: relative;
  left: 50%;
  top: 50%;
  transform: translate3d(-50%, -50%, 0);
  border: 1px solid #999;
}

/* 输入框 */
.search-box {
  position: absolute;
  z-index: 5;
  width: 60%;
  top: 70px;
  height: 30px;
}
/* 按钮 */
.search-box input {
  float: left;
  width: 80%;
  height: 150%;
  /* border: 1px solid #30ccc1; */
  padding: 0 8px;
  outline: none;
}
.search-box button {
  float: left;
  width: 20%;
  height: 150%;
  background: #409eff;
  border: 1px solid #409eff;
  color: #fff;
  outline: none;
}
.tip-box {
  width: 100%;
  max-height: 260px;
  position: absolute;
  top: 30px;
  overflow-y: auto;
  background-color: #fff;
}
/* 去除小标以及版本号 */
.amap-logo {
  display: none !important;
}
.amap-copyright {
  visibility: hidden !important;
}
</style>
