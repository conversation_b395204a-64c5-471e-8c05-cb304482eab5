<template>
  <div>
    <div>
      <commonList v-bind:queryData="queryData" v-if="flag"></commonList>
    </div>
  </div>
</template>

<script>
import commonList from '../commonList.vue'
export default {
  components: {
    commonList
  },
  data () {
    return {
      queryData: {
        number: '1',
        name: '点位维护',
        stateList: [128, 256]
      },
      flag: false
    }
  },
  created () {
    this.flag = true
  }

}
</script>

<style>
</style>