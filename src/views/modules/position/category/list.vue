<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      ref="queryParams"
      :model="dataForm"
      @keyup.enter.native="queryPage()"
    >
      <el-form-item>
        <el-input
          v-model="queryParams.name"
          placeholder="分类名称"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" @click="queryPage()">查询</el-button>
        <el-button
          icon="el-icon-plus"
          v-if="isAuth('business:category:save')"
          type="primary"
          @click="addOrUpdateHandle()"
          >新增</el-button
        >
        <el-button
          v-if="isAuth('business:category:delete')"
          type="danger"
          @click="deleteHandle()"
          :disabled="dataListSelections.length <= 0"
          >批量删除</el-button
        >
      </el-form-item>
    </el-form>

    <el-table
      :data="dataList"
      style="width: 100%; margin-bottom: 20px"
      row-key="id"
      border
      default-expand-all
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <el-table-column prop="name" header-align="center" label="名称">
      </el-table-column>
      <el-table-column
        prop="code"
        header-align="center"
        align="center"
        label="编号"
      >
      </el-table-column>

      <!-- <el-table-column
        prop="collect"
        header-align="center"
        align="center"
        label="是否有采集项"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.collect == false" size="small" type="danger"
            >否</el-tag
          >
          <el-tag v-else size="small">是</el-tag>
        </template>
      </el-table-column> -->
      <el-table-column
        prop="sequence"
        header-align="center"
        align="center"
        label="排序"
      >
      </el-table-column>
      <el-table-column
        prop="remark"
        header-align="center"
        align="center"
        label="备注"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        label="状态"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === false" size="small" type="danger"
            >未启用</el-tag
          >
          <el-tag v-else size="small">启用</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addCategory(scope.row)"
            >添加子分类</el-button
          >
          <el-button
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row)"
            >修改</el-button
          >
          <el-button
            type="text"
            size="small"
            @click="deleteHandle(scope.row.id)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 30, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination>

    <div>
      <el-dialog
        :title="!dataForm.id ? '新增' : '修改'"
        :close-on-click-modal="false"
        :visible.sync="visible"
      >
        <el-form
          :model="dataForm"
          :rules="dataRule"
          ref="dataForm"
          @keyup.enter.native="dataFormSubmit()"
          label-width="120px"
          style="width: 95%"
        >
          <div v-if="dataForm.parentName">
            <!-- <div v-if="dataForm.parentId && dataForm.id"> -->
            <el-form-item
              label="上级分类主键"
              prop="parentName"
              :error="parentIdError"
            >
              <el-input
                :disabled="true"
                v-model="dataForm.parentName"
                placeholder="上级分类主键"
              ></el-input>
            </el-form-item>
          </div>

          <el-form-item
            v-if="!categoryHide"
            label="分类编号"
            prop="code"
            :error="codeError"
          >
            <el-input v-model="dataForm.code" placeholder="编号"></el-input>
          </el-form-item>

          <el-form-item label="分类名称" prop="name" :error="nameError">
            <el-input v-model="dataForm.name" placeholder="名称"></el-input>
          </el-form-item>

          <el-form-item label="选择分类" v-if="categoryHide">
            <el-row>
              <div style="margin-top: -10px; margin-bottom: -10px">
                <p style="margin-left: -40px">
                  <span
                    :style="getStyle(index)"
                    v-for="(item, index) in categoryDict"
                    :key="index"
                  >
                    <el-tag
                      style="cursor: pointer"
                      @click="changeColor(item)"
                      :class="success(item)"
                      >{{ item.name }}</el-tag
                    >
                  </span>
                </p>
              </div>
            </el-row>

            <!-- <el-select
              ref="categorySelected"
              v-model="dataForm.code"
              placeholder="选择分类"
            >
              <el-option
                v-for="item in categoryDict"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              >
              </el-option>
            </el-select> -->
          </el-form-item>
          <el-form-item label="排序" prop="sequence" :error="sequenceError">
            <el-input-number
              v-model="dataForm.sequence"
              controls-position="right"
              :min="0"
            ></el-input-number>
          </el-form-item>

          <el-form-item label="状态" prop="status" :error="statusError">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="true">启用</el-radio>
              <el-radio :label="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="描述" prop="remark" :error="remarkError">
            <el-input
              type="textarea"
              :autosize="{ minRows: 4, maxRows: 8 }"
              v-model="dataForm.remark"
              placeholder="描述"
            ></el-input>
          </el-form-item>
        </el-form>
        <!-- <span slot="footer" class="dialog-footer"> -->
        <div style="margin-left: 120px">
          <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
          <el-button @click="visible = false">取消</el-button>
        </div>

        <!-- </span> -->
      </el-dialog>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    var validateConfirmPassword = (rule, value, callback) => {
      console.log(11111111111111111111)
      if (!this.dataForm.name) {
        callback(new Error('名称不能为空'))
      } else {
        callback()
      }
    }
    return {
      dataList: [],
      pageIndex: 1,
      pageSize: 30,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      visible: false,
      categoryDict: [],
      categoryCode: '',
      dataForm: {
        id: null,
        version: null,
        code: '',
        name: '',
        collect: false,
        level: '',
        sequence: '',
        status: true,
        remark: '',
        parentId: null,
        parentName: ''
      },
      dataRule: {
        code: [
          { required: true, message: '分类编码不能为空', trigger: 'blur' }
        ],
        name: [
          { required: true, validator: validateConfirmPassword, trigger: 'blur' }
        ],
        level: [
          { required: true, message: '层级不能为空', trigger: 'blur' }
        ],
        sequence: [
          { required: true, message: '排序不能为空', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '状态(启用/禁用)不能为空', trigger: 'blur' }
        ]
      },
      queryParams: {
        name: ''
      },
      codeError: null,
      nameError: null,
      collectError: null,
      levelError: null,
      sequenceError: null,
      statusError: null,
      remarkError: null,
      parentIdError: null,
      categoryNow: null,
      categoryHide: false

    }
  },
  activated () {
    this.queryPage()
  },
  methods: {
    queryPage () {
      this.pageIndex = 1
      this.getDataList()
    },
    load (tree, treeNode, resolve) {
      setTimeout(() => {
        resolve(tree.children)
      }, 300)
    },
    getStyle (id) {
      if (id !== 0) {
        return 'margin-left: 40px'
      }
      return ''
    },
    changeColor (item) {
      this.categoryNow = item.code + ''
      this.dataForm.code = item.code
      this.dataForm.name = item.name
    },
    success (item) {
      if (item.code + '' === this.categoryNow + '') {
        return 'select'
      } else {
        return 'moren'
      }
    },
    // 获取数据列表
    getDataList () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/position/category/tree/page'),
        method: 'get',
        params: {
          currentPage: this.pageIndex,
          pageSize: this.pageSize,
          name: this.queryParams.name
        }
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
        // this.forArr(this.dataList, true)
      })
    },
    forArr (arr, isExpand) {
      arr.forEach(i => {
        this.$refs.theTable.toggleRowExpansion(i, isExpand)
        if (i.children) {
          this.forArr(i.children, isExpand)
        }
      })
    },

    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle (val) {
      this.dataListSelections = val
    },
    addCategory (row) {
      this.dataForm = {}
      this.dataForm.collect = false
      this.dataForm.level = row.level
      this.visible = true
      if (row) {
        this.dataForm.parentId = row.id
        this.dataForm.parentName = row.name
        if (row.level + '' === '1') {
          this.categoryCode = 'PointTwoLevelType'
          this.categoryHide = true
        } else {
          this.categoryHide = false
        }
        if (row.level + '' === '2') {
          this.categoryCode = row.code
        }
        this.categoryNow = null
        this.getCategoryDict()
      }
      this.getDataList()
    },
    // 新增 / 修改
    addOrUpdateHandle (row) {
      this.addOrUpdateVisible = true
      if (row) {
        this.dataForm.parentName = row.name
      } else {
        this.dataForm.parentName = ''
      }
      const id = row ? row.id : ''
      this.init(id)
    },
    init (id) {
      this.dataForm.id = id || null
      this.visible = true
      this.$nextTick(() => {
        this.categoryDict = []
        this.categoryHide = false
        this.dataForm.version = null
        this.dataForm.code = null
        this.dataForm.name = null
        this.dataForm.collect = false
        this.dataForm.level = null
        this.dataForm.sequence = null
        this.dataForm.status = true
        this.dataForm.remark = null
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/position/category`),
            method: 'get',
            params: this.$http.adornParams({ id: this.dataForm.id })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
              if (this.dataForm.level + '' === '2') {
                this.categoryCode = 'PointTwoLevelType'
                this.categoryHide = true
                this.categoryNow = this.dataForm.code
                this.getCategoryDict()
              } else {
                this.categoryHide = false
                this.categoryCode = this.dataForm.code
              }
              // this.getDataList()
            }
          })
        }
      })
    },
    // 获取字典分类
    getCategoryDict () {
      const code = this.categoryCode
      this.$http({
        url: this.$http.adornUrl('/admin/dict/parent'),
        method: 'get',
        params: {
          code
        }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.categoryDict = resp.data.obj
        } else {
          this.categoryDict = []
        }
      })
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        // this.dataForm.name = this.$refs.categorySelected.selected.label
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/position/category/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
              this.categoryNow = null
              this.categoryDict = []
              this.getDataList()
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    // 删除
    deleteHandle (id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/position/category/removeByIds'),
          method: 'get',
          params: this.$http.adornParams({
            'ids': ids.join(',')
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    }
  }
}
</script>
<style scoped>
.moren {
  background-color: white;
  color: black;
}
.select {
}
</style>
