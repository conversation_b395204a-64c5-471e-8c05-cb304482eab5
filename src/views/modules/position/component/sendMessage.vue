<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    width="50%"
    append-to-body
    :close-on-click-modal="false"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      label-width="100px"
      :disabled="false"
    >
      <el-form-item label="短信内容" prop="title">
        <el-input v-model="dataForm.title"></el-input>
      </el-form-item>
      <el-form-item label="通知人" prop="sendPersonName">
        <el-input
          v-model="dataForm.sendPersonName"
          placeholder="请选择收件人"
          @focus="selectUser('1')"
        >
          <i
            @click="selectUser('1')"
            slot="suffix"
            style="margin-right: 5px"
            class="el-icon-arrow-down"
          ></i>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :disabled="isHide" @click="dataFormSubmit()"
          >发送</el-button
        >
        <el-button @click="visible = false">取消</el-button>
      </el-form-item>
    </el-form>
    <choose-executor
      v-if="selectUserDialogVisible"
      ref="selectUser"
      @change="getSelectUser"
    ></choose-executor>

    <choose-executor
      v-if="selectUserDialogVisible2"
      ref="selectUser"
      @change="getSelectUser"
    ></choose-executor>
  </el-dialog>
</template>

<script>
import ChooseExecutor from '../clap/component/choose-executor.vue'
export default {
  components: { ChooseExecutor },
  data () {
    return {
      isHide: false,
      title: '',
      visible: false,
      sendDataType: '',
      dataForm: {
        title: '',
        sendPersonName: '',
        copyPersonName: '',
        sendPerson: [],
        copyPerson: [],
        content: '',
        questionDesc: '',
        fileUrlList: [],
        htmlContent: ''
      },
      dataRule: {

      },
      prefix: '',
      selectUserDialogVisible: false,
      selectUserDialogVisible2: false,
      list: [],
      idList: []
    }
  },
  created () {
    this.prefix = window.SITE_CONFIG.baseUrl + '/file'
  },
  methods: {
    init (sendDataType, obj) {
      this.sendDataType = sendDataType
      // 为随手拍邮件
      if (sendDataType + '' === '1') {
        this.dataForm.questionDesc = obj.title
        this.dataForm.fileUrlList = []
        if (obj.fileUrlList) {
          obj.fileUrlList.forEach(file => {
            file = this.prefix + file
            this.dataForm.fileUrlList.push(file)
          })
        }
      }
      this.getDeptUserList(obj.questionTypeRelation.relationId)
    },
    dataFormSubmit () {
      this.isHide = true
      this.dataForm.htmlContent = ''
      this.dataForm.htmlContent = this.dataForm.title
      this.$http({
        url: this.$http.adornUrl('/admin/message/sendClapMessage'),
        method: 'post',
        data: this.$http.adornData({
          'content': this.dataForm.title,
          'userList': this.dataForm.sendPerson
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.$message({
            message: '发送成功',
            type: 'success'
          })
        } else {
          this.$message.error(data.msg)
        }
        this.isHide = false
      })
    },
    selectUser (type) {
      if (type + '' === '1') {
        this.selectUserDialogVisible = true
      } else if (type + '' === '2') {
        this.selectUserDialogVisible2 = true
      }
      this.$nextTick(() => {
        this.$refs.selectUser.type = type
        this.$refs.selectUser.getOrgTreeList()
        if (type + '' === '1') {
          this.$refs.selectUser.list = this.idList
        }
      })
    },
    getDeptUserList (orgId) {
      if (orgId) {
        this.$http({
          url: this.$http.adornUrl('/admin/user/getUserByOrgId'),
          method: 'get',
          params: { orgId: orgId }
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.list = data.obj
          } else {
            this.list = []
          }
          if (this.list) {
            this.list.forEach(data => {
              this.idList.push(data.id)
            })
            this.dataForm.sendPersonName = this.list ? this.list.map((item) => {
              return item.name + '<' + item.mobile + '>'
            }).join(',') : ''
          }
        })
      }
    },
    getSelectUser (type, value) {
      if (type + '' === '1' && value) {
        this.dataForm.sendPerson = value
        this.dataForm.sendPersonName = value ? value.map((item) => {
          return item.name + '<' + item.mobile + '>'
        }).join(',') : ''
        this.idList = []
        value.forEach(data => {
          this.idList.push(data.id)
        })
      }
    }
  }
}
</script>

<style>
</style>
