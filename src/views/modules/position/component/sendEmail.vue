<template>
  <!-- 整改通知书 -->
  <el-dialog
    :title="title"
    :visible.sync="visible"
    width="50%"
    append-to-body
    :close-on-click-modal="false"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      label-width="120px"
      :disabled="false"
    >
      <el-form-item label="发送内容标题" prop="title">
        <el-input v-model="dataForm.title"></el-input>
      </el-form-item>
      <el-form-item label="邮件正文" prop="content">
        <el-card shadow="never" style="">
          <el-form label-width="75px">
            <el-form-item label="问题描述:">
              {{ dataForm.questionDesc }}
            </el-form-item>
            <el-form-item label="问题图片:">
              <el-row :gutter="20">
                <el-col
                  :span="6"
                  v-for="(file, index) in dataForm.fileUrlList"
                  :key="index"
                >
                  <img :src="file" width="150px" @click="catPic(file)" alt="" />
                </el-col>
              </el-row>
              <el-dialog :visible.sync="dialogVisible" append-to-body>
                <img width="100%" :src="dialogImageUrl" alt="" />
              </el-dialog>
            </el-form-item>
          </el-form>
        </el-card>
      </el-form-item>
      <el-form-item label="短信内容" prop="personMessage">
        <el-input v-model="dataForm.personMessage"></el-input>
      </el-form-item>
      <el-form-item label="收件人" prop="sendPersonName">
        <el-input
          v-model="dataForm.sendPersonName"
          placeholder="请选择收件人"
          @focus="selectUser('1')"
        >
          <i
            @click="selectUser('1')"
            slot="suffix"
            style="margin-right: 5px"
            class="el-icon-arrow-down"
          ></i>
        </el-input>
      </el-form-item>
      <el-form-item label="抄送" prop="copyPersonName">
        <el-input
          v-model="dataForm.copyPersonName"
          placeholder="请选择抄送人"
          @focus="selectUser('2')"
        >
          <i
            @click="selectUser('2')"
            slot="suffix"
            style="margin-right: 5px"
            class="el-icon-arrow-down"
          ></i>
        </el-input>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" :disabled="isHide" @click="dataFormSubmit()"
          >发送</el-button
        >
        <el-button @click="visible = false">取消</el-button>
      </el-form-item>
    </el-form>
    <choose-executor
      v-if="selectUserDialogVisible"
      ref="selectUser"
      @change="getSelectUser"
    ></choose-executor>

    <choose-executor
      v-if="selectUserDialogVisible2"
      ref="selectUser"
      @change="getSelectUser"
    ></choose-executor>
  </el-dialog>
</template>

<script>
import ChooseExecutor from '../clap/component/choose-executor.vue'
export default {
  components: { ChooseExecutor },
  data () {
    return {
      isHide: false,
      title: '',
      visible: false,
      sendDataType: '',
      dataForm: {
        title: '',
        sendPersonName: '',
        copyPersonName: '',
        sendPerson: [],
        copyPerson: [],
        content: '',
        questionDesc: '',
        fileUrlList: [],
        htmlContent: '',
        personMessage: ''
      },
      dataRule: {

      },
      prefix: '',
      selectUserDialogVisible: false,
      selectUserDialogVisible2: false,
      dialogVisible: false,
      dialogImageUrl: '',
      list: [],
      idList: [],
      list2: [],
      idList2: []
    }
  },
  created () {
    this.prefix = window.SITE_CONFIG.baseUrl + '/file'
  },
  methods: {
    init (sendDataType, obj) {
      this.sendDataType = sendDataType
      // 为随手拍邮件
      if (sendDataType + '' === '1') {
        this.dataForm.questionDesc = obj.title
        this.dataForm.fileUrlList = []
        if (obj.fileUrlList) {
          obj.fileUrlList.forEach(file => {
            file = this.prefix + file
            this.dataForm.fileUrlList.push(file)
          })
        }
      }
      if (obj.questionTypeRelation) {
        this.getDeptUserList(obj.questionTypeRelation.relationId)
      }
    },
    getDeptUserList (orgId) {
      if (orgId) {
        this.$http({
          url: this.$http.adornUrl('/admin/user/getUserByOrgId'),
          method: 'get',
          params: { orgId: orgId }
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.list = data.obj
          } else {
            this.list = []
          }
          if (this.list) {
            this.list.forEach(data => {
              this.idList.push(data.id)
            })
            this.dataForm.sendPersonName = this.list ? this.list.map((item) => {
              return item.name + '<' + item.email + '>'
            }).join(',') : ''
          }
        })
      }
    },
    // 查看图片
    catPic (file) {
      this.dialogVisible = true
      this.dialogImageUrl = file
    },
    dataFormSubmit () {
      this.isHide = true
      this.dataForm.htmlContent = ''
      this.dataForm.htmlContent = '<p>' + this.dataForm.title + '</p>'
      if (this.dataForm.fileUrlList) {
        this.dataForm.fileUrlList.forEach(file => {
          this.dataForm.htmlContent += `<img :src="${file}" width="30%" alt="" /><br/><br/>`
        })
      }
      this.$http({
        url: this.$http.adornUrl('/admin/email/sendMessageAndEmail'),
        method: 'post',
        data: this.dataForm
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.$message({
            message: '发送成功',
            type: 'success'
          })
          this.visible = false
        } else {
          this.$message.error(data.msg)
        }
        this.isHide = false
      })
    },
    selectUser (type) {
      if (type + '' === '1') {
        this.selectUserDialogVisible = true
      } else if (type + '' === '2') {
        this.selectUserDialogVisible2 = true
      }
      this.$nextTick(() => {
        this.$refs.selectUser.type = type
        this.$refs.selectUser.getOrgTreeList()
        if (type + '' === '1') {
          this.$refs.selectUser.list = this.idList
        }
      })
    },
    getSelectUser (type, value) {
      if (type + '' === '1') {
        this.dataForm.sendPerson = value
        this.dataForm.sendPersonName = value ? value.map((item) => {
          return item.name + '<' + item.email + '>'
        }).join(',') : ''
        this.idList = []
        value.forEach(data => {
          this.idList.push(data.id)
        })
      } else if (type + '' === '2') {
        this.dataForm.copyPerson = value
        this.dataForm.copyPersonName = value ? value.map((item) => {
          return item.name + '<' + item.email + '>'
        }).join(',') : ''
      }
    }
  }
}
</script>

<style>
</style>
