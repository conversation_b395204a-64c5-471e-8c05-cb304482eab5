<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="queryPage()">
      <el-form-item>
        <el-input v-model="dataForm.executor" placeholder="姓名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.month" placeholder="手输月份,示例: xxxx-xx" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" @click="queryPage()">查询</el-button>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-download" type="success" @click="exportData">导出报表</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      style="width: 100%;">
      <el-table-column
        prop="executor"
        header-align="center"
        align="center"
        label="姓名">
      </el-table-column>
      <el-table-column
        prop="month"
        header-align="center"
        align="center"
        label="月份">
      </el-table-column>
      <el-table-column
        prop="taskCount"
        header-align="center"
        align="center"
        label="任务数">
      </el-table-column>
      <el-table-column
        prop="completedCount"
        header-align="center"
        align="center"
        label="完成数"
      >
      </el-table-column>
      <!--      <el-table-column-->
      <!--        fixed="right"-->
      <!--        header-align="center"-->
      <!--        align="center"-->
      <!--        width="150"-->
      <!--        label="操作">-->
      <!--        <template slot-scope="scope">-->
      <!--          <el-button type="text" size="small" @click="getDetail(scope.row.id)">详情</el-button>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 30, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
  </div>
</template>

<script>
export default {
  data () {
    return {
      dataForm: {
        executor: '',
        month: ''
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 30,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false
    }
  },
  activated () {
    this.queryPage()
  },
  methods: {
    queryPage () {
      this.pageIndex = 1
      this.getDataList()
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 获取数据列表
    getDataList () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/position/task/distribute/assessmentPages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'executor': this.dataForm.executor,
          'month': this.dataForm.month
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
          this.$message.error(data.msg)
        }
        this.dataListLoading = false
      })
    },
    // 导出数据
    exportData () {
      this.$http({
        url: this.$http.adornUrl('/position/task/distribute/assessmentPages/export'),
        method: 'post',
        responseType: 'arraybuffer',
        data: this.$http.adornData(this.dataForm)
      }).then(({data}) => {
        if (data.code && data.code !== 0) {
          this.$message.error('导出失败')
        } else {
          let blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'})
          window.location.href = URL.createObjectURL(blob)
          this.$message({
            type: 'success',
            message: '导出数据成功'
          })
        }
      })
    }
  }
}
</script>
