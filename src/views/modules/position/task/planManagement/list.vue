<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <el-form-item label="任务计划名称">
        <el-input
          v-model="dataForm.taskName"
          placeholder="请输入任务计划名称"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" @click="getDataList()">查询</el-button>
        <el-button icon="el-icon-plus" type="primary" @click="edit()">新增</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      style="width: 100%"
    >
      <el-table-column
        prop="taskName"
        header-align="center"
        align="center"
        label="任务计划名称"
      >
      </el-table-column>
      <el-table-column
        prop="relationType"
        header-align="center"
        align="center"
        label="计划类型"
      >
        <template slot-scope="scope">
          <el-tag size="small">{{
            scope.row.state == 1 ? "点位采集计划" : "点位测评计划"
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="startTime"
        header-align="center"
        align="center"
        label="开始时间"
      >
      </el-table-column>
      <el-table-column
        prop="endTime"
        header-align="center"
        align="center"
        label="结束时间"
      >
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button type="text" @click="edit(scope.row)">编辑</el-button>
          <el-button type="text" @click="remove(scope.row.id)">删除</el-button>
          <el-button
            type="text"
            v-if="scope.row.state == 3"
            @click="edit(scope.row.id, 'check')"
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 30, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination>
    <!-- 工单编辑页面 -->
    <update
      v-if="updateVisible"
      ref="update"
      @refreshDataList="getDataList"
    ></update>
  </div>
</template>

<script>
import moment from 'moment'
import Update from './update'

export default {
  data () {
    return {
      dataForm: {
        taskName: '',
        state: ''
      },
      states: [{
        value: '1',
        label: '未开始'
      }, {
        value: '2',
        label: '进行中'
      }, {
        value: '3',
        label: '已结束'
      }],
      updateVisible: false,
      dataList: [],
      pageIndex: 1,
      pageSize: 30,
      totalPage: 0,
      isHide: true,
      dataListLoading: false
    }
  },
  activated () {
    this.getDataList()
  },
  components: {
    Update
  },
  methods: {
    // 获取数据列表
    getDataList () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/position/task/pages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'taskName': this.dataForm.taskName
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          data.obj.records.forEach((item) => {
            item.startTime = moment(item.startTime).format('YYYY-MM-DD')
            item.endTime = moment(item.endTime).format('YYYY-MM-DD')
          })
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 删除
    remove (id) {
      this.$confirm(`确定要删除此任务计划吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/position/task/delete'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '删除成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 编辑页面
    edit (data, type) {
      this.updateVisible = true
      this.$nextTick(() => {
        this.$refs.update.init(data, type)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
// /deep/.el-table{
//   .el-button{
//     padding: 0 !important;
//   }
// }
</style>
