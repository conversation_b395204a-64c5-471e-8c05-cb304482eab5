<template>
  <el-dialog
  append-to-body
    class="plan-dialog"
    :title="
      !dataForm.id ? '新增计划' : editType === 'check' ? '计划详情' : '修改计划'
    "
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :inline="true"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="120px"
      :disabled="editType === 'check' ? true : false"
    >
      <el-form-item
        class="long-name"
        label="任务计划名称"
        prop="taskName"
        :error="taskNameError"
      >
        <el-input
          v-model="dataForm.taskName"
          clearable
          placeholder="请输入任务计划名称"
        ></el-input>
      </el-form-item>
      <el-form-item
        class="long-name"
        prop="taskType"
        label="计划类型"
        :error="taskTypeError"
      >
        <el-select
          v-model="dataForm.taskType"
          clearable
          placeholder="请选择计划类型"
        >
          <el-option
            v-for="item in typeList"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="开始时间" prop="startTime" :error="startTimeError">
        <el-date-picker
          type="date"
          value-format="yyyy-MM-dd"
          v-model="dataForm.startTime"
          placeholder="请选择开始时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="结束时间" prop="endTime" :error="endTimeError">
        <el-date-picker
          type="date"
          value-format="yyyy-MM-dd"
          v-model="dataForm.endTime"
          placeholder="请选择结束时间"
        >
        </el-date-picker>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer" v-if="editType != 'check'">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary"  :disabled="loading" @click="dataFormSubmit()">保存</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      loading: false,
      editType: '',
      visible: false,
      dialogVisible: false,
      typeList: [],
      dataForm: {
        id: null,
        taskName: '',
        taskType: '',
        startTime: null,
        endTime: null
      },
      dataRule: {
        taskName: [
          { required: true, message: '任务计划名称不能为空', trigger: 'blur' }
        ],
        taskType: [
          { required: true, message: '计划类型不能为空', trigger: 'blur' }
        ],
        startTime: [
          { required: true, message: '开始时间不能为空', trigger: 'blur' }
        ],
        endTime: [
          { required: true, message: '结束时间不能为空', trigger: 'blur' }
        ]
      },
      taskNameError: null,
      taskTypeError: null,
      startTimeError: null,
      endTimeError: null
    }
  },
  methods: {
    init (data, type) {
      this.loading = false
      this.getType()
      this.editType = type
      this.dataForm.id = data ? data.id : null
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (data) {
          data.taskType = JSON.stringify(data.taskType)
          this.dataForm = { ...this.dataForm, ...data }
        }
      })
    },
    // 获取计划类型
    getType () {
      this.$http({
        url: this.$http.adornUrl('/admin/dict/parent'),
        method: 'get',
        params: this.$http.adornParams({
          'code': 'task_plan_type'
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.typeList = data.obj
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.clearErrors()
          if (this.dataForm.id) {
            console.log('编辑')
            this.updateTask()
          } else {
            console.log('新增')
            this.addTask()
          }
        }
      })
    },
    /* 编辑更新 */
    updateTask () {
      this.loading = true
      this.$http({
        url: this.$http.adornUrl(`/position/task/update`),
        method: 'post',
        data: this.$http.adornData(this.dataForm)
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
              this.loading = false
            }
          })
        } else if (data && data.code === 303) {
          this.loading = false
          for (let it of data.obj) {
            this[`${it.field}Error`] = it.message
          }
        } else {
          this.loading = false
          this.$message.error(data.msg)
        }
      })
    },
    /* 新增保存 */
    addTask () {
      this.loading = true
      this.$http({
        url: this.$http.adornUrl(`/position/task/save`),
        method: 'post',
        data: this.$http.adornData(this.dataForm)
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
              this.loading = false
            }
          })
        } else if (data && data.code === 303) {
          for (let it of data.obj) {
            this[`${it.field}Error`] = it.message
          }
          this.loading = false
        } else {
          this.loading = false
          this.$message.error(data.msg)
        }
      })
    },
    clearErrors () {
      this.taskNameError = null
      this.taskTypeError = null
      this.startTimeError = null
      this.endTimeError = null
    }
  }
}
</script>

<style lang="scss" scoped>
.plan-dialog {
  /deep/.long-name {
    width: 100%;
    display: flex;
    flex-direction: row;
    .el-form-item__content {
      flex: 1;
      .el-select {
        width: 100%;
      }
    }
  }
}
</style>
