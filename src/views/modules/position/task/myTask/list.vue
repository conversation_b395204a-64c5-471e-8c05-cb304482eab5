<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <el-form-item label="任务名称">
        <el-input
          v-model="dataForm.taskName"
          placeholder="请输入任务计划名称"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="dataForm.state" clearable placeholder="请选择">
          <el-option
            v-for="item in stateList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" @click="getDataList()">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      default-expand-all
      v-loading="dataListLoading"
      style="width: 100%"
    >
      <el-table-column
        prop="taskName"
        label="标题"
        align="center"
        min-width="30%"
      >
        <template slot-scope="scope">
          <span style="display: inline-block">
            <!-- <i
              style="margin-left: 2px"
              v-if="!scope.row.subCode && !scope.row.children && isHide"
              class="el-icon-arrow-down"
              @click="isHide = !isHide"
            ></i>
            <i
              style="margin-left: 3px"
              v-if="!scope.row.subCode && !scope.row.children && !isHide"
              class="el-icon-arrow-right"
              @click="isHide = !isHide"
            ></i>
            <span v-if="scope.row.subCode" class="status-info"></span> -->
            <span>{{ scope.row.taskName }}</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column
        prop="subCode"
        label="任务编号"
        header-align="center"
        align="center"
        min-width="25%"
      ></el-table-column>
      <el-table-column
        label="任务类型"
        prop="taskType"
        header-align="center"
        align="center"
        min-width="12%"
      >
        <template slot-scope="scope">
          {{ scope.row.state == 1 ? "点位采集计划" : "点位测评计划" }}
        </template>
      </el-table-column>
      <el-table-column
        prop="executor"
        label="执行人"
        header-align="center"
        align="center"
        min-width="10%"
      ></el-table-column>
      <el-table-column
        label="开始时间"
        header-align="center"
        align="center"
        min-width="10%"
      >
        <template slot-scope="scope">
          {{ scope.row.startTime ? scope.row.startTime.split(" ")[0] : "" }}
        </template>
      </el-table-column>
      <el-table-column
        label="结束时间"
        header-align="center"
        align="center"
        min-width="10%"
      >
        <template slot-scope="scope">
          {{ scope.row.endTime ? scope.row.endTime.split(" ")[0] : "" }}
        </template>
      </el-table-column>
      <el-table-column
        label="状态"
        header-align="center"
        align="center"
        min-width="10%"
        :formatter="statusFormatter"
      >
        <!-- <template slot-scope="scope">
          <span
            v-for="(item, index) in stateList"
            :key="index"
            v-if="item.id === scope.row.state"
          >
            {{ item.name }}
          </span>
        </template> -->
      </el-table-column>
      <el-table-column
        label="操作"
        fixed="right"
        min-width="15%"
        header-align="center"
        align="center"
      >
        <template slot-scope="scope">
          <!-- <el-button type="text">转发</el-button> -->
          <el-button type="text" @click="getDetail(scope.row.id)"
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 30, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination>
  </div>
</template>

<script>
export default {
  data () {
    return {
      stateList: [{
        name: '待接收',
        id: 1
      }, {
        name: '已撤回',
        id: 2
      }, {
        name: '已退回',
        id: 4
      }, {
        name: '执行中',
        id: 8
      }, {
        name: '审核中',
        id: 16
      }, {
        name: '已完成',
        id: 32
      }],
      dataForm: {
        taskName: '',
        state: ''
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 30,
      totalPage: 0,
      isHide: true,
      dataListLoading: false
    }
  },
  activated () {
    this.getDataList()
  },
  components: {},
  methods: {
    // 获取数据列表
    getDataList () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/position/task/distribute/personPages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'executorId': this.$store.state.user.id,
          'state': this.dataForm.state,
          'taskName': this.dataForm.taskName
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 详情跳转页面
    getDetail (id) {
      this.$router.push({ name: 'taskDetail', query: { id: id } })
    },
    // 状态处理类
    statusFormatter (row, column, cellValue, index) {
      if (row.state + '' === '1') { return '待接收' }

      if (row.state + '' === '2') { return '已撤回' }

      if (row.state + '' === '4') { return '已退回' }

      if (row.state + '' === '8') { return '执行中' }

      if (row.state + '' === '16') { return '审核中' }

      if (row.state + '' === '32') { return '已完成' }

      return row.state
    }
  }
}
</script>

<style lang="scss" scoped>
// /deep/.child-table{
//   .el-table__header-wrapper{
//     display: none;
//   }
// }
// /deep/.el-table{
//   .el-button{
//     padding: 0 !important;
//   }
// }
.status-info {
  display: inline-block;
  width: 12px;
  height: 12px;
  background: #909399;
  border-radius: 50%;
  border: 1px solid #909399;
}
</style>
