<template>
  <el-dialog
  append-to-body
    class="plan-dialog"
    title="审核不通过"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
    >
      <el-form-item prop="fbDesc" :error="fbDescError">
        <el-input
          v-model="dataForm.fbDesc"
          type="textarea"
          :autosize="{ minRows: 4 }"
          placeholder="请输入审核不通过原因"
        ></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确认</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  components: {},
  data () {
    return {
      visible: false,
      dataForm: {
        id: null,
        fbDesc: ''
      },
      dataRule: {
        fbDesc: [
          { required: true, message: '请输入审核不通过原因', trigger: 'blur' }
        ]
      },
      fbDescError: null
    }
  },
  methods: {
    init (id) {
      this.dataForm.id = id
      this.visible = true
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.clearErrors()
          this.$http({
            url: this.$http.adornUrl(`/position/task/distribute/refuse`),
            method: 'get',
            params: this.$http.adornParams({
              'id': this.dataForm.id,
              'fbDesc': this.dataForm.fbDesc
            })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    },
    clearErrors () {
      this.fbDescError = null
    }
  }
}
</script>

<style lang="scss" scoped>
.plan-dialog {
  /deep/ .el-form-item {
    .el-form-item__content {
      .el-select {
        width: 100%;
      }
      .el-date-editor {
        width: 100% !important;
      }
      .add-text {
        color: #409eff;
        margin-left: 3px;
        font-weight: bold;
      }
    }
  }
}
</style>
