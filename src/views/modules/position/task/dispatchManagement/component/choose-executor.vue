<template>
  <el-dialog
    append-to-body
    class="user-dialog"
    title="选择参与人"
    :visible.sync="selectUserDialogVisible"
  >
    <el-row :gutter="12">
      <el-col :span="12">
        <el-card shadow="never">
          <div slot="header" class="clearfix">
            <span>已选</span
            ><span style="margin-left: 15px; color: blue">{{ userNum }}</span>
          </div>
          <el-scrollbar style="height: 350px">
            <el-tree
              :data="orgList"
              :props="props"
              node-key="id"
              ref="dictTree"
              @current-change="currentChangeHandle"
              :default-expand-all="true"
              :highlight-current="true"
              :expand-on-click-node="false"
              style="height: 100%"
            >
            </el-tree>
          </el-scrollbar>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="never">
          <div slot="header" class="clearfix">
            <span>全部</span
            ><span style="margin-left: 15px; color: blue">{{ allNum }}</span>
          </div>
          <el-scrollbar style="height: 350px">
            <el-checkbox-group v-model="list" @change="getSelectUser">
              <el-checkbox
                style="margin-top: 5px; display: block"
                v-for="item in userList"
                :label="item.id"
                :key="item.id"
                >{{ item.name }}</el-checkbox
              >
            </el-checkbox-group>
          </el-scrollbar>
        </el-card>
      </el-col>
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitUser">确 定</el-button>
      <el-button @click="selectUserDialogVisible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      selectUserDialogVisible: false,
      // 已选
      userNum: 0,
      // 全部
      allNum: 0,
      orgList: [],
      props: {
        key: 'id',
        label: 'name',
        children: 'children'
      },
      userList: [],
      list: [],
      list2: []
    }
  },
  created () { },
  methods: {
    getOrgTreeList (userList) {
      this.list = []
      this.list2 = []
      if (userList) {
        userList.forEach(user => {
          this.list.push(user.userId)
        })
        console.log(this.list, this.list2, this.list === this.list2)
        this.userNum = userList.length || 0
      } else {
        this.userNum = 0
        this.list = []
      }

      this.$http({
        url: this.$http.adornUrl('/admin/org/tree'),
        method: 'get',
        params: this.$http.adornParams({ level: 1 })
      }).then(resp => {
        if (resp.data && resp.data.code === 0) {
          this.orgList = resp.data.obj
          this.selectUserDialogVisible = true
        }
      })
    },
    currentChangeHandle (data, node) {
      this.$http({
        url: this.$http.adornUrl('/admin/user/getUserByOrgCode'),
        method: 'get',
        params: { code: data.code }
      }).then((resp) => {
        if (resp.data && resp.data.code === 0) {
          this.userList = resp.data.obj
          this.allNum = this.userList.length
        }
      })
    },
    getSelectUser () {
      this.userNum = 0
      this.list.forEach(data => {
        if (data) {
          this.userNum = this.userNum + 1
        }
      })
      // this.userNum = this.list.length
      console.log(this.list)
    },
    submitUser () {
      this.selectUserDialogVisible = false
      if (this.list) {
        let list = []
        this.list.forEach((id) => {
          this.userList.forEach(item => {
            if (id === item.id) {
              list.push(item)
            }
          })
        })
        this.$parent.dataForm.jurisdictionList = list.length > 0 ? list.map((item) => {
          return {
            userId: item.id,
            userName: item.name
          }
        }) : []
        this.$parent.dataForm.jurisdictionName = list.length > 0 ? list.map((item) => {
          return item.name
        }).join(',') : []
        this.$parent.dataForm.counts = this.list.length
      } else {
        this.$parent.dataForm.counts = null
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.user-dialog {
  /deep/.el-card {
    .el-scrollbar {
      .el-scrollbar__wrap {
        overflow: hidden scroll !important;
      }
    }
    .el-tree-node__content {
      height: 30px;
      line-height: 30px;
    }
    .el-checkbox-group {
      display: flex;
      flex-direction: column;
      .el-checkbox {
        line-height: 30px;
      }
    }
  }
}
</style>
