<template>
  <div>
    <el-dialog
      class="plan-dialog"
      :title="dataForm.id ? '编辑任务派单' : '新增任务派单'"
      :close-on-click-modal="false"
      :visible.sync="visible"
    >
      <el-form
        :model="dataForm"
        :rules="dataRule"
        ref="dataForm"
        @keyup.enter.native="dataFormSubmit()"
        label-width="120px"
      >
        <el-form-item label="任务名称" prop="taskName" :error="orderNameError">
          <el-input
            v-model="dataForm.taskName"
            clearable
            placeholder="请输入任务名称"
          ></el-input>
        </el-form-item>

        <el-form-item label="任务明细">
          <Editor :taskDesc.sync="dataForm.taskDesc"></Editor>
        </el-form-item>

        <el-form-item label="测评明细" prop="evaluationList">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            circle
            @click="addPoint"
          ></el-button>
          <span class="add-text" @click="addPoint">添加</span>
        </el-form-item>

        <el-form-item>
          <el-input
            ref="evaluationList"
            v-model="dataForm.evaluationList"
            v-if="false"
            @input="changeData"
          ></el-input>
          <!--选中测评明细点-->
          <div
            class="point-list"
            v-for="(item, index) in dataForm.evaluationList"
            :key="index"
          >
            <span>{{ item.typeName }}</span>
            <div class="button">
              <el-input-number
                v-model="item.counts"
                :min="1"
                :precision="0"
                :step="1"
                size="small"
              ></el-input-number>
              <el-button type="text" @click="deletePoint(index)"
                >移除</el-button
              >
            </div>
          </div>
        </el-form-item>

        <el-form-item label="执行人" prop="jurisdictionName">
          <el-input
            v-model="dataForm.jurisdictionName"
            placeholder="请选择执行人"
            @focus="selectUser"
            :disabled="dataForm.id"
          >
            <i
              @click="selectUser"
              slot="suffix"
              style="margin-right: 5px"
              class="el-icon-arrow-down"
            ></i>
          </el-input>
        </el-form-item>

        <el-form-item
          prop="regionCode"
          label="测评区域"
          :error="relationTypeError"
        >
          <el-select
            ref="regionSelected"
            v-model="dataForm.regionCode"
            clearable
            placeholder="请选择测评区域"
          >
            <el-option
              v-for="item in regionList"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime" :error="startTimeError">
          <el-date-picker
            type="date"
            v-model="dataForm.startTime"
            placeholder="请选择开始时间"
            value-format="yyyy-MM-dd"
            align="right"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime" :error="endTimeError">
          <el-date-picker
            type="date"
            v-model="dataForm.endTime"
            placeholder="请选择结束时间"
            value-format="yyyy-MM-dd"
            align="right"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" :disabled="loading" @click="dataFormSubmit()"
          >保存</el-button
        >
      </span>
    </el-dialog>
    <pointEvaluate
      v-if="pointVisible"
      ref="point"
      @fatherMethod="getFoucus"
      :evaluationList.sync="dataForm.evaluationList"
    ></pointEvaluate>
    <!-- 选择部门，人员 -->
    <chooseExecutor
      v-if="selectUserDialogVisible"
      ref="selectUser"
    ></chooseExecutor>
  </div>
</template>

<script>
import Editor from './editor'
import pointEvaluate from './point-evaluation'
import chooseExecutor from './choose-executor'

export default {
  components: {
    chooseExecutor,
    Editor,
    pointEvaluate
  },
  data () {
    var validateEvaluationList = (rule, value, callback) => {
      console.log(this.dataForm.evaluationList, this.dataForm.evaluationList.length === 0, !this.dataForm.evaluationList && this.dataForm.evaluationList.length !== 0)
      if (!this.dataForm.evaluationList) {
        callback(new Error('测评明细不能为空'))
      } else {
        if (this.dataForm.evaluationList.length === 0) {
          callback(new Error('测评明细不能为空'))
        } else {
          callback()
        }
      }
    }
    return {
      loading: false,
      type: '',
      selectUserDialogVisible: false,
      visible: false,
      pointVisible: false,
      regionList: [],
      region: {},
      dataForm: {
        id: null,
        taskId: 0,
        taskName: '',
        taskDesc: '',
        jurisdictionName: '',
        evaluationList: [],
        jurisdictionList: [],
        regionCode: '',
        regionName: '',
        startTime: null,
        endTime: null,
        counts: null
      },
      dataRule: {
        jurisdictionName: [
          { required: true, message: '执行人未选择', trigger: 'change' }
        ],
        regionCode: [
          { required: true, message: '测评区域未选择', trigger: 'change' }
        ],
        startTime: [
          { required: true, message: '开始时间不能为空', trigger: 'blur' }
        ],
        endTime: [
          { required: true, message: '结束时间不能为空', trigger: 'blur' }
        ],
        evaluationList: [
          { required: true, validator: validateEvaluationList, trigger: 'change' }
        ]
      },
      noEdit: false,
      orderNameError: null,
      relationTypeError: null,
      startTimeError: null,
      endTimeError: null
    }
  },
  methods: {
    init (data, type) {
      this.dataForm.jurisdictionList = []
      this.loading = false
      this.type = type
      this.dataForm.evaluationList = []
      this.dataForm.taskDesc = ''
      this.dataForm.taskId = (type === 'edit') ? data.taskId : data.id
      this.dataForm.id = (type === 'edit') ? data.id : null
      this.evaluationList = []
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (type === 'edit') {
          this.noEdit = true
          this.getRelationList(data.id)
        }
        this.getAreaList()
      })
    },
    getFoucus () {
      this.$refs.dataForm.validate()
    },
    changeData (e) {
      e.srcElement.blur()
    },
    // 获取区域下拉列表
    getAreaList () {
      this.$http({
        url: this.$http.adornUrl(`/position/area/top`),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 0) {
          if (data.obj) {
            this.regionList = data.obj
          }
        }
      })
    },
    getDetail (id) {
      this.$http({
        url: this.$http.adornUrl(`/position/task/distribute/detail`),
        method: 'get',
        params: this.$http.adornParams({ id: id })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataForm = {
            ...this.dataForm,
            ...data.obj,
            ...{
              'jurisdictionName': data.obj.executor
            }
          }
        }
      })
    },
    getRelationList (id) {
      this.relationList = []
      this.$http({
        url: this.$http.adornUrl('/position/task/distribute/subRelationGroup'),
        method: 'get',
        params: { id: id }
      }).then((res) => {
        if (res.data && res.data.code === 0) {
          this.dataForm.evaluationList = res.data.obj.map((item) => {
            return {
              'typeName': item.typeName,
              'counts': item.totalCount,
              'typeId': item.relationList[0].typeId,
              'typeSubId': item.relationList[0].typeSubId,
              'typeSubName': item.relationList[0].typeSubName
            }
          })
          this.$nextTick(() => {
            console.log('evaluationList', this.dataForm.evaluationList)
            this.getDetail(id)
          })
        }
      })
    },
    selectUser () {
      this.selectUserDialogVisible = true
      this.$nextTick(() => {
        this.$refs.selectUser.getOrgTreeList(this.dataForm.jurisdictionList)
      })
    },
    // 添加测评明细
    addPoint () {
      this.pointVisible = true
      this.$nextTick(() => {
        this.$refs.point.init(this.dataForm.evaluationList)
      })
    },
    /* 移除测评明细 */
    deletePoint (index) {
      this.dataForm.evaluationList = this.dataForm.evaluationList.filter((item, idx) => {
        return idx !== index
      })
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.clearErrors()
          if (this.type === 'edit') {
            this.edit()
          } else {
            this.add()
          }
        }
      })
    },
    add () {
      this.loading = true
      this.dataForm.regionName = this.$refs.regionSelected.selected.label
      this.$http({
        url: this.$http.adornUrl(`/position/task/distribute/create`),
        method: 'post',
        data: this.$http.adornData(this.dataForm)
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
              this.loading = false
            }
          })
        } else if (data && data.code === 303) {
          for (let it of data.obj) {
            this[`${it.field}Error`] = it.message
          }
          this.loading = false
        } else {
          this.$message.error(data.msg)
          this.loading = false
        }
      })
    },
    edit () {
      this.loading = false
      this.dataForm.regionName = this.$refs.regionSelected.selected.label
      this.$http({
        url: this.$http.adornUrl(`/position/task/distribute/edit`),
        method: 'post',
        data: this.$http.adornData(this.dataForm)
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
              this.loading = false
            }
          })
        } else if (data && data.code === 303) {
          for (let it of data.obj) {
            this[`${it.field}Error`] = it.message
          }
          this.loading = false
        } else {
          this.$message.error(data.msg)
          this.loading = false
        }
      })
    },
    clearErrors () {
      this.orderNameError = null
      this.relationTypeError = null
      this.startTimeError = null
      this.endTimeError = null
    }
  }
}
</script>

<style lang="scss" scoped>
.plan-dialog {
  /deep/ .el-form-item {
    .el-form-item__content {
      .point-list {
        display: flex;
        flex-direction: row;
        margin-bottom: 5px;
        .button {
          margin-left: auto;
          .el-button {
            margin-left: 5px;
          }
        }
      }
      .el-select {
        width: 100%;
      }
      .el-date-editor {
        width: 100% !important;
      }
      .add-text {
        color: #409eff;
        margin-left: 3px;
        font-weight: bold;
        cursor: pointer;
      }
    }
  }
}
</style>
