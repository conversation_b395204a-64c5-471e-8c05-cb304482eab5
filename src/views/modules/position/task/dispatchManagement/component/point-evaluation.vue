<template>
  <el-dialog
    append-to-body
    class="point-dialog"
    title="添加测评点位"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <!-- <el-row :gutter="20" type="flex" class="role-distribute">
      <el-col :span="12">
        <el-card class="box-card" shadow="never">
          <div slot="header" class="clearfix">
            已选<span class="count" style="color: #409EFF;">{{ checkList.length }}</span>
          </div>
          <el-scrollbar style="height: 350px;">
            <div v-for="(item,index) in checkLabel" :key="index" class="text">
              <span>{{ item.typeName }}</span>
            </div>
          </el-scrollbar>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="box-card" shadow="never">
          <div slot="header" class="clearfix">
            全部<span class="count">{{ pointList.length }}</span>
          </div>
          <el-scrollbar style="height: 350px;">
            <el-checkbox-group v-model="checkList" @change="getLabel">
              <el-checkbox v-for="(item,index) in pointList" :key="index" :label="item.id">{{ item.categoryName }}</el-checkbox>
            </el-checkbox-group>
          </el-scrollbar>
        </el-card>
      </el-col>
    </el-row> -->
    <el-card class="box-card" shadow="never">
      <el-input placeholder="输入关键字进行过滤" v-model="filterText">
      </el-input>
      <el-tree
        class="filter-tree"
        style="width: 550px"
        :data="pointList"
        :props="orgProps"
        node-key="id"
        ref="tree"
        show-checkbox
        :default-expand-all="false"
        :highlight-current="true"
        :expand-on-click-node="false"
        :filter-node-method="filterNode"
        @check-change="handleCheckChange"
      >
      </el-tree>
    </el-card>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirmPoint">确认</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  watch: {
    filterText (val) {
      this.$refs.tree.filter(val)
    }
  },
  props: {
    evaluationList: {
      type: Array,
      default: []
    }
  },
  data () {
    return {
      visible: true,
      checkList: [],
      checkLabel: [],
      pointList: [],
      dataIds: [],
      orgProps: {
        key: 'id',
        label: 'categoryName',
        children: 'children'
      },
      filterText: ''
    }
  },
  methods: {
    init (evaluationList) {
      this.checkList = []
      this.checkLabel = []
      this.pointList = []
      this.evaluationList = evaluationList
      this.getPointType()
      this.visible = true
      if (this.evaluationList.length > 0) {
        this.checkLabel = this.evaluationList
        this.checkList = this.evaluationList.map((item) => {
          return item.typeId
        })
      }
      this.initTreeData()
    },
    filterNode (value, data) {
      if (!value) return true
      return data.categoryName.indexOf(value) !== -1
    },
    initTreeData () {
      if (this.checkLabel) {
        this.checkLabel.forEach(data => {
          this.dataIds.push(data.typeSubId)
        })
        this.$refs.tree.setCheckedKeys(this.dataIds)
      }
    },
    getLabel (val) {
      this.checkLabel = []
      val.map((itemId) => {
        this.pointList.map((item) => {
          if (item.id === itemId) {
            this.checkLabel.push({
              'typeName': item.categoryName,
              'counts': 1,
              'typeId': item.id
            })
          }
        })
      })
    },
    handleCheckChange (data, checked, indeterminate) {
    },
    searchChange () {
      this.$refs.tree.filter(this.filterText)
    },
    confirmPoint () {
      this.getCheckTreeData()
      this.visible = false
      this.$emit('update:evaluationList', this.checkLabel)
      this.$parent.getFoucus()
      this.$parent.$forceUpdate()
    },
    getCheckTreeData () {
      this.checkLabel = []
      const a = this.$refs.tree.getCheckedNodes()
      if (a) {
        a.forEach(data => {
          if (!data.children) {
            this.checkLabel.push({
              'typeName': data.parentName,
              'counts': 1,
              'typeId': data.parentId,
              'typeSubId': data.id,
              'typeSubName': data.categoryName
            })
          }
        })
      }
    },
    getPointType () {
      this.$http({
        url: this.$http.adornUrl('/evaluate/cardCategory/getTreeType'),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.pointList = data.obj
        } else {
          this.$message.error(data.msg)
          this.pointList = []
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.point-dialog {
  /deep/.el-card {
    .el-scrollbar {
      .el-scrollbar__wrap {
        overflow: hidden scroll !important;
      }
    }
    .count {
      margin-left: 5px;
    }
    .text {
      display: flex;
      flex-direction: row;
      span {
        line-height: 40px;
      }
      .el-button {
        padding: 0;
        margin-left: auto;
      }
    }
    .el-checkbox-group {
      display: flex;
      flex-direction: column;
      .el-checkbox {
        line-height: 40px;
      }
    }
  }
}
</style>
