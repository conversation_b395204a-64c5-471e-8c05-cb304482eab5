<template>
  <quill-editor class="editor"
                v-model="content"
                ref="myQuillEditor"
                :options="editorOption"
                @blur="onEditorBlur($event)" @focus="onEditorFocus($event)"
                @change="onEditorChange($event)"
  ></quill-editor>
</template>

<script>
  export default {
    name: 'editor',
    props: ['taskDesc'],
    data () {
      return {
        content: null,
        editorOption: {
          placeholder: '请输入任务明细'
        }
      }
    },
    watch: {
      'taskDesc': {
        handler (val) {
          this.content = val
        }
      }
    },
    methods: {
      onEditorBlur () {
      },
      onEditorFocus () {
        // 获得焦点事件
      },
      onEditorChange () {
        // 内容改变事件
        this.$emit('update:taskDesc', this.content)
      }
    }
  }
</script>

<style lang="scss" scoped>
  /deep/.quill-editor{
    .ql-container{
      height: 300px;
    }
  }
</style>
