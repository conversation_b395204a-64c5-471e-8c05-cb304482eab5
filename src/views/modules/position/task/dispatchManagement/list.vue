<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <!-- label="任务计划名称" -->
      <el-form-item>
        <el-input
          v-model="dataForm.taskName"
          placeholder="请输入任务计划名称"
          clearable
        ></el-input>
      </el-form-item>
      <!-- label="执行人" -->
      <el-form-item>
        <el-input
          v-model="dataForm.executor"
          placeholder="请输入执行人"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.state" clearable placeholder="状态">
          <el-option
            v-for="item in stateList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="dataForm.startTime"
          type="date"
          placeholder="开始时间"
          format="yyyy-MM-dd"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="dataForm.endTime"
          type="date"
          placeholder="结束时间"
          format="yyyy-MM-dd"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" @click="getDataList()">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      row-key="id"
      border
      default-expand-all
      v-loading="dataListLoading"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <el-table-column
        prop="taskName"
        label="标题"
        header-align="center"
        min-width="30%"
      >
        <template slot-scope="scope">
          <span style="display: inline-block; margin-left: 3vh">
            <span v-if="scope.row.subCode" class="status-info"></span>
            <span>{{ scope.row.taskName }}</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column
        prop="subCode"
        label="任务编号"
        header-align="center"
        align="center"
        min-width="25%"
      ></el-table-column>
      <el-table-column
        label="任务类型"
        prop="taskType"
        header-align="center"
        align="center"
        min-width="12%"
      >
        <template slot-scope="scope">
          {{ scope.row.state == 1 ? "点位采集计划" : "点位测评计划" }}
        </template>
      </el-table-column>
      <el-table-column
        prop="executor"
        label="执行人"
        header-align="center"
        align="center"
        min-width="10%"
      ></el-table-column>
      <el-table-column
        label="开始时间"
        header-align="center"
        align="center"
        min-width="10%"
      >
        <template slot-scope="scope">
          <span>{{
            scope.row.startTime ? scope.row.startTime.split(" ")[0] : ""
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="结束时间"
        header-align="center"
        align="center"
        min-width="10%"
      >
        <template slot-scope="scope">
          <span>
            {{ scope.row.endTime ? scope.row.endTime.split(" ")[0] : "" }}</span
          >
        </template>
      </el-table-column>
      <el-table-column
        label="状态"
        header-align="center"
        align="center"
        min-width="10%"
        :formatter="statusFormatter"
      >
        <!-- <template slot-scope="scope">
          <span
            v-for="(item, index) in stateList"
            :key="index"
            v-if="item.id === scope.row.state"
          >
            {{ item.name }}
          </span>
        </template> -->
      </el-table-column>
      <el-table-column
        label="操作"
        header-align="center"
        align="center"
        fixed="right"
        min-width="15%"
      >
        <template slot-scope="scope">
          <div v-if="scope.row.children === undefined">
            <el-button
              type="text"
              v-if="scope.row.state === 2 || scope.row.state === 4"
              @click="edit(scope.row, 'edit')"
              >编辑</el-button
            >
            <el-button
              type="text"
              v-if="scope.row.state === 2 || scope.row.state === 4"
              @click="remove(scope.row.id)"
              >删除</el-button
            >
            <el-button
              type="text"
              v-if="scope.row.state === 1"
              @click="withdraw(scope.row.id)"
              >撤回</el-button
            >
            <el-button type="text" @click="getDetail(scope.row.id)"
              >详情</el-button
            >
          </div>
          <el-button v-else type="text" @click="edit(scope.row)"
            >任务派单</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 30, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination>
    <!-- 工单编辑页面 -->
    <update
      v-if="updateVisible"
      ref="update"
      @refreshDataList="getDataList"
    ></update>
  </div>
</template>

<script>
import Update from './component/update'

export default {
  data () {
    return {
      dataForm: {
        taskName: '',
        executor: '',
        state: '',
        username: '',
        startTime: null,
        endTime: null
      },
      stateList: [{
        name: '待接收',
        id: 1
      }, {
        name: '已撤回',
        id: 2
      }, {
        name: '已退回',
        id: 4
      }, {
        name: '执行中',
        id: 8
      }, {
        name: '审核中',
        id: 16
      }, {
        name: '已完成',
        id: 32
      }],
      updateVisible: false,
      dataList: [],
      pageIndex: 1,
      pageSize: 30,
      totalPage: 0,
      isHide: true,
      dataListLoading: false
    }
  },
  activated () {
    this.getDataList()
  },
  components: {
    Update
  },
  methods: {
    // 获取数据列表
    getDataList () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/position/task/pages/tree'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'taskName': this.dataForm.taskName,
          'executor': this.dataForm.executor,
          'state': this.dataForm.state,
          'startTime': this.dataForm.startTime,
          'endTime': this.dataForm.endTime
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 删除
    remove (id) {
      this.$confirm(`确定要删除此任务派单记录吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/position/task/distribute/delete'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '删除成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 撤回
    withdraw (id) {
      this.$confirm(`确定要撤回此任务派单记录吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/position/task/distribute/withdraw'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '撤回成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 编辑页面
    edit (data, type) {
      this.updateVisible = true
      this.$nextTick(() => {
        this.$refs.update.init(data, type)
      })
    },
    // 详情跳转页面
    getDetail (id) {
      this.$router.push({ name: 'taskDetail', query: { id: id } })
    },
    // 状态处理类
    statusFormatter (row, column, cellValue, index) {
      if (row.state + '' === '1') { return '待接收' }

      if (row.state + '' === '2') { return '已撤回' }

      if (row.state + '' === '4') { return '已退回' }

      if (row.state + '' === '8') { return '执行中' }

      if (row.state + '' === '16') { return '审核中' }

      if (row.state + '' === '32') { return '已完成' }

      return row.state
    }
  }
}
</script>

<style lang="scss" scoped>
// /deep/.child-table{
//   .el-table__header-wrapper{
//     display: none;
//   }
// }
// /deep/.el-table{
//   .el-button{
//     padding: 0 !important;
//   }
// }
.status-info {
  display: inline-block;
  width: 12px;
  height: 12px;
  background: #909399;
  border-radius: 50%;
  border: 1px solid #909399;
}
</style>
