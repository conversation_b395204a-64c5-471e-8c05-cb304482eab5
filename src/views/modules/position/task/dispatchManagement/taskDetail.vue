<template>
  <div class="detail">
    <p align="left" style="font-size: 20px">详情</p>
    <el-divider></el-divider>
    <div class="content">
      <div class="item-row">
        <div class="item-column">
          <div class="label">任务名称</div>
          <div class="text">{{ taskDetail.taskName }}</div>
        </div>
        <div class="item-column">
          <div class="label">测评区域</div>
          <div class="text">{{ taskDetail.regionName }}</div>
        </div>
        <div class="item-column">
          <div class="label">执行人</div>
          <div class="text">{{ taskDetail.executor }}</div>
        </div>
      </div>

      <div class="item-row">
        <div class="item-column">
          <div class="label">开始时间</div>
          <div class="text">{{ taskDetail.startTime }}</div>
        </div>
        <div class="item-column">
          <div class="label">结束时间</div>
          <div class="text">{{ taskDetail.endTime }}</div>
        </div>
        <div class="item-column">
          <div class="label">任务状态</div>
          <div
            class="text"
            v-for="(item, index) in stateList"
            :key="index"
            v-if="item.id === taskDetail.state"
          >
            {{ item.name }}
          </div>
        </div>
      </div>

      <div class="item-row">
        <div class="item-column">
          <div class="label">任务明细</div>
          <div
            class="text"
            style="margin-top: -16px"
            v-html="taskDetail.taskDesc"
          ></div>
        </div>
      </div>

      <!-- <div class="item-list" style="margin-top: -15px"> -->
      <div class="item-list" style="margin-top: -16px">
        <div class="label">测评明细</div>
      </div>
      <div class="text">
        <div
          class="point-list"
          v-for="(item, index) in relationList"
          :key="index"
        >
          <div class="name">{{ item.typeSubName }}</div>
          <div class="number">{{ item.totalCount }}</div>
          <div class="info">
            <span
              style="cursor: pointer"
              @click="goDetail(item2)"
              v-for="(item2, index) in item.relationList"
              :key="index"
              >{{ item2.numbers }}
              <span v-if="item2.numbers">,</span>
            </span>
            <!-- <span>{{ item.number }}</span> -->
          </div>
        </div>
      </div>
      <!-- </div> -->

      <div class="button">
        <el-button
          type="primary"
          v-if="taskDetail.state === 1"
          @click="withdraw(taskDetail.id)"
          >撤回</el-button
        >
        <el-button
          type="danger"
          v-if="taskDetail.state === 2 || taskDetail.state === 4"
          @click="remove(taskDetail.id)"
          >删除</el-button
        >
      </div>

      <div class="codeInfo">
        <!-- <div v-if="detailQuestionId"> -->

        <br />
        <p align="left" style="font-size: 20px">问题流转跟踪</p>
        <el-divider></el-divider>
        <br />
        <el-table :data="recordData" border stripe style="width: 100%">
          <el-table-column align="center" prop="operator" label="用户" />
          <el-table-column align="center" prop="operateDes" label="操作记录" />
          <el-table-column align="center" prop="remark" label="说明" />
          <el-table-column align="center" prop="createDate" label="操作时间" />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data () {
    return {
      id: '',
      detailQuestionId: '',
      taskDetail: {},
      stateList: [{
        name: '待接收',
        id: 1
      }, {
        name: '已撤回',
        id: 2
      }, {
        name: '已退回',
        id: 4
      }, {
        name: '执行中',
        id: 8
      }, {
        name: '审核中',
        id: 16
      }, {
        name: '已完成',
        id: 32
      }],
      relationList: [],
      codes: [],
      recordData: []

    }
  },
  activated () {
    if (this.$route.query.id) {
      this.id = this.$route.query.id
      this.getDetail()
      this.getRelationList()
    }
  },
  methods: {
    getDetail () {
      this.$http({
        url: this.$http.adornUrl('/position/task/distribute/detail'),
        method: 'get',
        params: { id: this.$route.query.id }
      }).then((res) => {
        if (res.data && res.data.code === 0) {
          this.taskDetail = res.data.obj
          this.recordData = res.data.obj.logList
        } else {
          this.taskDetail = {}
          this.recordData = []
        }
      })
    },
    getRelationList () {
      this.relationList = []
      this.$http({
        url: this.$http.adornUrl('/position/task/distribute/subRelationGroup'),
        method: 'get',
        params: { id: this.$route.query.id }
      }).then((res) => {
        if (res.data && res.data.code === 0) {
          /* this.relationList = res.data.obj */
          res.data.obj.forEach((item) => {
            this.codes = []
            item.relationList.forEach(data => {
              if (data.numbers) {
                this.codes.push(data.numbers)
              }
            })

            this.relationList.push({
              ...item,
              ...{
                'number': this.codes.join(',')
              }
            })
          })
        }
      })
    },
    // 删除
    remove (id) {
      this.$confirm(`确定要删除此任务派单记录吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/position/task/distribute/delete'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '删除成功',
              type: 'success',
              duration: 500
            })
            this.getDetail()
            this.getRelationList()
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 撤回
    withdraw (id) {
      this.$confirm(`确定要撤回此任务派单记录吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/position/task/distribute/withdraw'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '撤回成功',
              type: 'success',
              duration: 500
            })
            this.getDetail()
            this.getRelationList()
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 测评详情跳转页面
    goDetail (item) {
      this.$router.push('/position-evaluate/question/card/list')
    }
  }
}
</script>

<style lang="scss" scoped>
.el-divider {
  margin: 8px 0;
  background: 0 0;
  border-top: 1px solid #e6ebf5;
}
.detail {
  .content {
    width: 100%;
    .item-row {
      line-height: 30px;
      margin-top: 25px;
      margin-bottom: 25px;
      display: flex;
      flex-direction: row;
      .item-column {
        margin-right: 20px;
        flex: 1;
        .label {
          color: #7f7f7fd8;
        }
        .text {
          margin-top: 5px;
          display: flex;
          .question-img {
            margin-right: 20px;
            width: 148px;
            height: 148px;
            border-radius: 10px 10px;
            img {
              width: 100%;
              height: 100%;
            }
          }
        }
        &:last-child {
          margin-right: unset;
        }
      }
    }
    .item-list {
      line-height: 30px;
      margin-top: 25px;
      margin-bottom: 25px;
      display: flex;
      flex-direction: row;
      flex: 1;
      .label {
        // width: 120px;
        color: #7f7f7fd8;
        margin-right: 50px;
        text-align: right;
      }
    }
  }
}
.text {
  margin-top: -16px;
  flex: auto;
  .point-list {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    margin-bottom: 15px;
    .name {
      width: 500px;
      // padding-right: 15px;
    }
    .number {
      flex-basis: 50px;
    }
    .info {
      flex: 1;
      min-width: 0;
      color: #1890ff;
      word-break: break-all;
    }
  }
}
.button {
  margin: 35px 0;
  text-align: center;
  .el-button {
    margin: 0 20px;
  }
}
.icon-c {
  color: #409eff;
}
.mg-100 {
  margin-bottom: 100px;
}
</style>
