<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="80px"
    >
      <el-form-item label="标签" prop="tag">
        <el-input v-model="dataForm.tag" placeholder="标签"></el-input>
      </el-form-item>
      <el-form-item label="属性名" prop="attributeKey">
        <el-input
          v-model="dataForm.attributeKey"
          placeholder="属性名"
        ></el-input>
      </el-form-item>
      <el-form-item label="属性值" prop="attributeValue">
        <el-input
          v-model="dataForm.attributeValue"
          placeholder="属性值"
        ></el-input>
      </el-form-item>
      <el-form-item label="协议" prop="protocol">
        <el-input v-model="dataForm.protocol" placeholder="协议"></el-input>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="dataForm.status">
          <el-radio :label="false">禁用</el-radio>
          <el-radio :label="true">启用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  data () {
    return {
      visible: false,
      dataList: [],
      dataForm: {
        id: null,
        tag: '',
        attributeKey: '',
        attributeValue: '',
        protocol: '',
        status: '',
        version: 0
      }
    }
  },
  methods: {
    init (id) {
      this.dataForm.id = id || undefined
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
      })
      if (this.dataForm.id) {
        this.$http({
          url: this.$http.adornUrl(`/admin/whiteList/tags`),
          method: 'get',
          params: this.$http.adornParams({
            'id': this.dataForm.id
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.dataForm.tag = data.obj.tag
            this.dataForm.attributeKey = data.obj.attributeKey
            this.dataForm.attributeValue = data.obj.attributeValue
            this.dataForm.protocol = data.obj.protocol
            this.dataForm.status = data.obj.status
            this.dataForm.version = data.obj.version
          }
        })
      }
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$http({
            url: this.$http.adornUrl(`/admin/whiteList/tags/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData({
              'id': this.dataForm.id || null,
              'tag': this.dataForm.tag,
              'attributeKey': this.dataForm.attributeKey,
              'attributeValue': this.dataForm.attributeValue,
              'protocol': this.dataForm.protocol,
              'status': this.dataForm.status,
              'version': this.dataForm.version
            })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }
      })
    }
  }
}
</script>
