<template>
  <div class="mod-config">
    <el-form
      :inline="true"
      :model="dataForm"
      ref="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <el-form-item label="名称:" prop="name">
        <el-input v-model="dataForm.name" placeholder="名称"></el-input>
      </el-form-item>
      <el-form-item label="微信号" prop="wechatAccountCode">
        <el-select v-model="dataForm.wechatAccountCode">
          <el-option
            v-for="item in wechatAccountList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="search()">查询</el-button>
        <el-button type="warning" @click="resetForm()">重置</el-button>
        <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      style="width: 100%"
    >
      <el-table-column
        prop="code"
        header-align="center"
        align="center"
        label="编码"
      >
      </el-table-column>
      <el-table-column
        prop="name"
        header-align="center"
        align="center"
        label="名称"
      >
      </el-table-column>
      <el-table-column
        prop="wechatAccountName"
        header-align="center"
        align="center"
        label="微信号"
      >
      </el-table-column>
      <el-table-column
        prop="scopeText"
        header-align="center"
        align="center"
        label="授权作用域"
      >
      </el-table-column>
      <el-table-column
        prop="backUrl"
        header-align="center"
        align="center"
        label="回调页面地址"
      >
      </el-table-column>
      <el-table-column
        prop="authorizeUrl"
        header-align="center"
        align="center"
        label="授权链接"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.authorizeUrl">{{
            scope.row.authorizeUrl + scope.row.code
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        label="状态"
      >
        <template slot-scope="scope">
          <el-tag v-if="!scope.row.status" size="small" type="danger"
            >禁用</el-tag
          >
          <el-tag v-else size="small">启用</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="addOrUpdateHandle(scope.row.id)"
            >修改</el-button
          >
          <el-button
            v-if="!scope.row.status"
            type="text"
            size="small"
            @click="setEnableHandle(scope.row.id, true)"
            >启用</el-button
          >
          <el-button
            v-if="scope.row.status"
            type="text"
            size="small"
            @click="setEnableHandle(scope.row.id, false)"
            >禁用</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @refreshDataList="getDataList"
    ></add-or-update>
  </div>
</template>

<script>
import AddOrUpdate from './oauthurl-add-or-update'
export default {
  data () {
    return {
      dataForm: {
        name: null,
        wechatAccountCode: null
      },
      wechatAccountList: [],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false
    }
  },
  components: {
    AddOrUpdate
  },
  activated () {
    this.getDataList()
    this.getWechatAccountList()
  },
  methods: {
    // 获取数据列表
    getDataList () {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/wechat/oauth/url/pages'),
        method: 'post',
        data: this.$http.adornData({
          'currentPage': this.pageIndex,
          'pageSize': this.pageSize,
          'name': this.dataForm.name,
          'wechatAccountCode': this.dataForm.wechatAccountCode
        })
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.obj.records
          this.totalPage = data.obj.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    getWechatAccountList () {
      this.$http({
        url: this.$http.adornUrl('/wechat/account/getMpAndOpenList'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        this.wechatAccountList = data.obj
      })
    },
    // 每页数
    sizeChangeHandle (val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle (val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle (val) {
      this.dataListSelections = val
    },
    search () {
      this.pageIndex = 1
      this.getDataList()
    },
    // 重置
    resetForm () {
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.getDataList()
      })
    },
    // 新增 / 修改
    addOrUpdateHandle (id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 启用、禁用
    setEnableHandle (id, enable) {
      this.$confirm(`确定进行[${enable ? '启用' : '禁用'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/wechat/oauth/url/setEnable'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      }).catch(() => { })
    }
  }
}
</script>
