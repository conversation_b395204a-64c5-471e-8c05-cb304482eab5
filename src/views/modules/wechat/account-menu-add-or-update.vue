<template>
  <div>
    <el-form
      :inline="true"
      :model="dataForm"
      @keyup.enter.native="getDataList()"
    >
      <el-form-item>
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="initItem()">添加父菜单</el-button>
        <el-button type="danger" @click="syncFromWeChat"
          >从微信同步到本地</el-button
        >
        <el-button type="danger" @click="pushToWeChat">推送到微信</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border style="width: 100%">
      <table-tree-column
        prop="name"
        header-align="center"
        treeKey="id"
        label="名称"
      >
      </table-tree-column>
      <el-table-column
        prop="type"
        header-align="center"
        align="center"
        label="类型"
      >
      </el-table-column>
      <el-table-column
        prop="sequence"
        header-align="center"
        align="center"
        label="排序"
      >
      </el-table-column>
      <el-table-column
        v-if="isAuth('menu-update') || isAuth('menu-delete')"
        fixed="right"
        header-align="center"
        align="center"
        label="操作"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            v-if="scope.row.parentId === null"
            size="small"
            @click="initItem(scope.row.id)"
            >新增子菜单
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="initItem(scope.row.parentId, scope.row.id)"
            >修改</el-button
          >
          <el-button
            type="text"
            size="small"
            @click="deleteHandle(scope.row.id)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <menu-button ref="menuButton" @refreshDataList="init(mpCode)"></menu-button>
  </div>
</template>

<script>
import TableTreeColumn from '@/components/table-tree-column'
import { treeDataTranslate } from '@/utils'
import MenuButton from './account-menu-button'
import _ from 'lodash'

export default {
  data () {
    return {
      mpCode: '',
      visible: false,
      dataForm: {},
      dataList: []
    }
  },
  components: {
    TableTreeColumn,
    MenuButton
  },
  methods: {
    // 添加菜单，parentId如果为空则是父菜单，子菜单需要传入
    initItem (parentId, id) {
      // 没有parentId也没有id，则为添加父菜单
      if (!parentId && !id && this.dataList.length >= 3) {
        this.$message.error('至多设置3个父级菜单')
        return
      }
      let item = _.find(this.dataList, { 'id': parentId })
      // 有parentId但是没有id则为添加子菜单
      if (parentId && id === undefined && item.children && item.children.length >= 5) {
        this.$message.error('至多设置5个子菜单')
        return
      }
      this.$refs.menuButton.init(this.mpCode, parentId, id)
    },
    deleteHandle (id) {
      this.$confirm(`确定要删除吗？如果是父级菜单，子菜单也会被删除`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/wechat/menu/delete'),
          method: 'get',
          params: this.$http.adornParams({
            'id': id
          })
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.init(this.mpCode)
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      }).catch(() => { })
    },
    handleCancel () {
      this.visible = false
      this.$emit('refreshDataList')
    },
    init (code) {
      this.$nextTick(() => {
        this.visible = true
        this.mpCode = code
        this.$http({
          url: this.$http.adornUrl('/admin/wechat/menu/getMenusByCode'),
          method: 'post',
          params: this.$http.adornParams(
            { 'code': code }
          )
        }).then(({ data }) => {
          this.dataList = treeDataTranslate(data.obj, 'id')
        })
      })
    },
    // 推送到腾讯
    pushToWeChat () {
      this.$confirm(`请确定是否要推送至微信`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/wechat/menu/pushToWeChat'),
          method: 'post',
          params: this.$http.adornParams({
            'code': this.mpCode
          })
        }).then(({ data }) => {
          if (data.code === 0) {
            this.$message.info('推送成功')
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 从微信同步
    syncFromWeChat () {
      this.$confirm(`请确认是否从微信同步至本地，这将覆盖本地的菜单`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/admin/wechat/menu/syncFromWeChat'),
          method: 'post',
          params: this.$http.adornParams({
            'code': this.mpCode
          })
        }).then(({ data }) => {
          if (data.code === 0) {
            this.$message.info('同步成功')
          } else {
            this.$message.error(data.msg)
          }
          this.init(this.mpCode)
        })
      })
    }
  }
}
</script>
