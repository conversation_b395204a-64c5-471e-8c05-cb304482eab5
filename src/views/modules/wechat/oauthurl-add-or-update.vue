<template>
  <el-dialog
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    :visible.sync="visible"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataForm"
      @keyup.enter.native="dataFormSubmit()"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="编码" prop="code" :error="codeError">
            <el-input v-model="dataForm.code" placeholder="编码"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="名称" prop="name" :error="nameError">
            <el-input v-model="dataForm.name" placeholder="名称"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="微信号" prop="wechatAccountCode">
            <el-select v-model="dataForm.wechatAccountCode">
              <el-option
                v-for="item in wechatAccountList"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="授权作用域" prop="scope" :error="scopeError">
            <el-dict code="wechat_scope" v-model="dataForm.scope"></el-dict>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="状态" prop="status" :error="statusError">
            <el-radio-group v-model="dataForm.status">
              <el-radio :label="true">启用</el-radio>
              <el-radio :label="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item
            label="回调页面地址"
            prop="backUrl"
            :error="backUrlError"
          >
            <el-input
              v-model="dataForm.backUrl"
              placeholder="回调页面地址"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" ref="submitBtn" @click="dataFormSubmit()"
        >确定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
export default {
  data () {
    return {
      visible: false,
      wechatAccountList: [],
      dataForm: {
        id: null,
        version: null,
        code: null,
        name: null,
        wechatAccountCode: null,
        scope: null,
        backUrl: null,
        status: true
      },
      dataRule: {
        code: [
          { required: true, message: '编码不能为空', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '名称不能为空', trigger: 'blur' }
        ],
        wechatAccountCode: [
          { required: true, message: '微信号不能为空', trigger: 'blur' }
        ],
        scope: [
          { required: true, message: '授权作用域不能为空', trigger: 'blur' }
        ],
        backUrl: [
          { required: true, message: '回调页面地址不能为空', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '状态不能为空', trigger: 'blur' }
        ]
      },
      codeError: null,
      nameError: null,
      wechatAccountCodeError: null,
      scopeError: null,
      backUrlError: null,
      statusError: null
    }
  },
  methods: {
    init (id) {
      this.dataForm.id = id || null
      this.getWechatAccountList()
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.$http({
            url: this.$http.adornUrl(`/admin/wechat/oauth/url`),
            method: 'get',
            params: this.$http.adornParams({ id: this.dataForm.id })
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.dataForm = data.obj
            }
          })
        }
      })
    },
    getWechatAccountList () {
      this.$http({
        url: this.$http.adornUrl('/wechat/account/getMpAndOpenList'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        this.wechatAccountList = data.obj
      })
    },
    // 表单提交
    dataFormSubmit () {
      this.$refs['submitBtn'].disabled = true
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.clearErrors()
          this.$http({
            url: this.$http.adornUrl(`/wechat/oauth/url/${!this.dataForm.id ? 'save' : 'update'}`),
            method: 'post',
            data: this.$http.adornData(this.dataForm)
          }).then(({ data }) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            } else if (data && data.code === 303) {
              for (let it of data.obj) {
                this[`${it.field}Error`] = it.message
              }
              this.$refs['submitBtn'].disabled = false
            } else {
              this.$message.error(data.msg)
              this.$refs['submitBtn'].disabled = false
            }
          })
        } else {
          this.$refs['submitBtn'].disabled = false
        }
      })
    },
    clearErrors () {
      this.codeError = null
      this.nameError = null
      this.wechatAccountCodeError = null
      this.scopeError = null
      this.backUrlError = null
      this.statusError = null
    }
  }
}
</script>
