<template>
  <div>
    <el-select class="width185" v-model="selectValue" autocomplete clearable :disabled="disabled"
               :placeholder="placeholder" filterable style="width: 100%">
      <el-option
        v-for="item in options"
        :key="item.code"
        :label="item.name"
        :value="item.code">
      </el-option>
    </el-select>
  </div>
</template>

<script>
  export default {
    name: 'el-dict',
    componentName: 'ElDict',
    data () {
      return {
        placeholder: '请选择',
        options: [],
        selectValue: ''
      }
    },
    props: {
      disabled: {
        type: Boolean,
        default: false
      },
      // 导入的url地址
      code: {
        type: String
      },
      // initCode: {
      //   type: String
      // },
      // 接受外部v-model传入的值，必须使用value
      value: {
      }
    },
    watch: {
      // 判断下拉框的值是否有改变
      selectValue (val, oldVal) {
        if (val !== oldVal) {
          this.$emit('input', this.selectValue)
          console.log(this.selectValue)
        }
      },
      value (val) {
        if (typeof val === 'number') {
          this.selectValue = val.toString()
        } else {
          this.selectValue = val
        }
      }
    },
    mounted () {
      // 远程请求回来的数据
      this.selectValue = this.value
      this.$http({
        url: this.$http.adornUrl(`/admin/dict/parent`),
        method: 'get',
        params: this.$http.adornParams({ code: this.code })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.options = data.obj || []
          // if (this.initCode && data.obj) {
          //   const option = data.obj.find(item => item.code === this.initCode)
          //   if (option) {
          //     this.selectValue = this.initCode
          //   }
          // }
        }
      })
    }
  }
</script>
