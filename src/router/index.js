/**
 * 全站路由配置
 *
 * 建议:
 * 1. 代码中路由统一使用name属性跳转(不使用path属性)
 */
import Vue from 'vue'
import Router from 'vue-router'
import http from '@/utils/httpRequest'
import {isURL} from '@/utils/validate'
import {clearLoginInfo} from '@/utils'

Vue.use(Router)

// 开发环境不使用懒加载, 因为懒加载页面太多的话会造成webpack热更新太慢, 所以只有生产环境使用懒加载
const _import = require('./import-' + process.env.NODE_ENV)

// 全局路由(无需嵌套上左右整体布局)
const globalRoutes = [
  { path: '/404', component: _import('common/404'), name: '404', meta: { title: '404未找到' } },
  { path: '/login', component: _import('common/newLogin'), name: 'login', meta: { title: '登录' } },
  {path: '/login-wait', component: _import('common/login-wait'), name: 'login-wait', meta: {title: '登录中'}}
]

// 主入口路由(需嵌套上左右整体布局)
const mainRoutes = {
  path: '/',
  component: _import('main'),
  name: 'main',
  redirect: { name: 'home' },
  meta: { title: '主入口整体布局' },
  children: [
    // 通过meta对象设置路由展示方式
    // 1. isTab: 是否通过tab展示内容, true: 是, false: 否
    // 2. iframeUrl: 是否通过iframe嵌套展示内容, '以http[s]://开头': 是, '': 否
    // 提示: 如需要通过iframe嵌套展示内容, 但不通过tab打开, 请自行创建组件使用iframe处理!
    { path: '/home', component: _import('common/home'), name: 'home', meta: { title: '工作台' } },
    { path: '/theme', component: _import('common/theme'), name: 'theme', meta: { title: '主题' } },
    { path: '/orderDetail', component: _import('modules/position/order/detail'), name: 'orderDetail', meta: { title: '工单详情', isTab: true } },
    { path: '/evaluateDetail', component: _import('modules/position/evaluate/config/evaluateDetail'), name: 'evaluateDetail', meta: { title: '测评明细', isTab: true } },
    { path: '/evaluateQuestionDetail', component: _import('modules/position/evaluate/question/questionDetail'), name: 'evaluateQuestionDetail', meta: { title: '测评问题信息', isTab: true } },
    { path: '/evaluateImport', component: _import('modules/position/evaluate/component/import'), name: 'import', meta: { title: '导入', isTab: true } },
    { path: '/evaluateResult', component: _import('modules/position/evaluate/component/importResult'), name: 'importResult', meta: { title: '结果', isTab: true } },
    { path: '/taskDetail', component: _import('modules/position/task/dispatchManagement/taskDetail'), name: 'taskDetail', meta: { title: '派单详情', isTab: true } },
    { path: '/clapDetail', component: _import('modules/position/clap/detail'), name: 'clapDetail', meta: { title: '问题详情', isTab: true } },
    { path: '/codeDetail', component: _import('modules/position/code/detail'), name: 'codeDetail', meta: { title: '二维码详情', isTab: true } },
    { path: '/informationDetail', component: _import('modules/position/informationdelivery/compent/detail'), name: 'informationDetail', meta: { title: '资讯详情', isTab: true } },
    { path: '/twoPosition', component: _import('modules/position/position/manage/twoList'), name: 'twoPosition', meta: { title: '二级点位', isTab: true } },
    { path: '/threePosition', component: _import('modules/position/position/manage/threeList'), name: 'threePosition', meta: { title: '三级点位', isTab: true } },
    { path: '/examineDetail', component: _import('modules/position/position/hand/detail'), name: 'examineDetail', meta: { title: '点位详情', isTab: true } }
  ],
  beforeEnter (to, from, next) {
    let token = sessionStorage.getItem('Authorization')
    if (!token || !/\S/.test(token)) {
      clearLoginInfo()
      return next(`/login?redirect=${to.fullPath}`)
    }
    next()
  }
}

const router = new Router({
  mode: 'hash',
  scrollBehavior: () => ({ y: 0 }),
  isAddDynamicMenuRoutes: false, // 是否已经添加动态(菜单)路由
  routes: globalRoutes.concat(mainRoutes)
})

router.beforeEach(async (to, from, next) => {
  // 新增代码：处理所有路由中的token参数
  const token = to.query.token
  if (token) {
    // 存储token信息
    sessionStorage.setItem('Authorization', 'bearer ' + token)
    sessionStorage.setItem('token', token)
    // 清理URL中的token参数
    const cleanedQuery = { ...to.query, 'isPluginPage': true }
    delete cleanedQuery.token
    if (cleanedQuery['userInfoCode']) {
      delete cleanedQuery['userInfoCode']
    }
    next({
      path: to.path,
      query: cleanedQuery,
      replace: true
    })
    return
  } else {
    // 文明园区服务器不能访问外网, 这种情况暂不考虑
    // const userInfoCode = to.query.userInfoCode
    // if (userInfoCode) {
    //   // 存储token信息
    //   await loginWithUserInfoCode(userInfoCode)
    //   // 清理URL中的token参数
    //   const cleanedQuery = {...to.query}
    //   delete cleanedQuery.userInfoCode
    //   next({
    //     path: to.path,
    //     query: cleanedQuery,
    //     replace: true
    //   })
    //   return
    // }
  }

  // 添加动态(菜单)路由
  // 1. 已经添加 or 全局路由, 直接访问
  // 2. 获取菜单列表, 添加并保存本地存储
  if (router.options.isAddDynamicMenuRoutes || fnCurrentRouteType(to, globalRoutes) === 'global') {
    next()
  } else {
    http({
      url: http.adornUrl('/admin/user/authorities'),
      method: 'get',
      params: http.adornParams({ groupType: 0 })
    }).then(({ data }) => {
      if (data && data.code === 0) {
        if (!data.obj.menuList || data.obj.menuList.length === 0) {
          this.$message.error('该用户没有任何菜单权限，无法访问')
        }
        fnAddDynamicMenuRoutes(data.obj.menuList)
        router.options.isAddDynamicMenuRoutes = true
        sessionStorage.setItem('menuList', JSON.stringify(data.obj.menuList || '[]'))
        sessionStorage.setItem('permissions', JSON.stringify(data.obj.permissions || '[]'))
        next({ ...to, replace: true })
      } else {
        sessionStorage.setItem('menuList', '[]')
        sessionStorage.setItem('permissions', '[]')
        next(`/login?redirect=${to.fullPath}`)
      }
    }).catch((e) => {
      console.log(`%c${e} 请求菜单列表和权限失败，跳转至登录页！！`, 'color:blue')
      // router.push({ name: 'login' })
      next(`/login?redirect=${to.fullPath}`)
    })
  }
})

async function loginWithUserInfoCode (userInfoCode) {
  await http({
    url: http.adornUrl('/auth/auth/sso/loginWithOneCode?userInfoCode=' + userInfoCode),
    method: 'post'
  }).then(({ data }) => {
    if (data && data.code === 0) {
      this.$message.info(data.msg)
      // oauth2用的token
      // 把token对象放在sessionStorage中
      sessionStorage.setItem('Authorization', data.obj.token_type + ' ' + data.obj.access_token)
      sessionStorage.setItem('token', data.obj.access_token)
      sessionStorage.setItem('oAuthToken', data.obj)
      // this.$router.push({ path: this.redirect || '/home' })
    }
  })
}
/**
 * 判断当前路由类型, global: 全局路由, main: 主入口路由
 * @param {*} route 当前路由
 */
function fnCurrentRouteType (route, globalRoutes = []) {
  var temp = []
  for (var i = 0; i < globalRoutes.length; i++) {
    if (route.path === globalRoutes[i].path) {
      return 'global'
    } else if (globalRoutes[i].children && globalRoutes[i].children.length >= 1) {
      temp = temp.concat(globalRoutes[i].children)
    }
  }
  return temp.length >= 1 ? fnCurrentRouteType(route, temp) : 'main'
}

/**
 * 添加动态(菜单)路由
 * @param {*} menuList 菜单列表
 * @param {*} routes 递归创建的动态(菜单)路由
 */
function fnAddDynamicMenuRoutes (menuList = [], routes = []) {
  var temp = []
  for (var i = 0; i < menuList.length; i++) {
    if (menuList[i].children && menuList[i].children.length >= 1) {
      temp = temp.concat(menuList[i].children)
    } else if (menuList[i].url && /\S/.test(menuList[i].url)) {
      menuList[i].url = menuList[i].url.replace(/^\//, '')
      var route = {
        path: menuList[i].url.replace('/', '-'),
        component: null,
        name: menuList[i].url.replace('/', '-'),
        meta: {
          menuId: menuList[i].id,
          title: menuList[i].name,
          isDynamic: true,
          isTab: true,
          iframeUrl: ''
        }
      }
      // 非根节点
      // todo 前端帮忙看下怎么实现新窗口打开，我不会-~
      // url以http[s]://开头, 通过iframe展示
      if (isURL(menuList[i].url)) {
        route['path'] = `i-${menuList[i].id}`
        route['name'] = `i-${menuList[i].id}`
        route['meta']['iframeUrl'] = menuList[i].url
      } else {
        try {
          route['component'] = _import(`modules/${menuList[i].url}`) || null
        } catch (e) { }
      }
      routes.push(route)
    }
  }
  if (temp.length >= 1) {
    fnAddDynamicMenuRoutes(temp, routes)
  } else {
    mainRoutes.name = 'main-dynamic'
    mainRoutes.children = routes
    router.addRoutes([
      mainRoutes,
      { path: '*', redirect: { name: '404' } }
    ])
    sessionStorage.setItem('dynamicMenuRoutes', JSON.stringify(mainRoutes.children || '[]'))
  }
}

export default router
